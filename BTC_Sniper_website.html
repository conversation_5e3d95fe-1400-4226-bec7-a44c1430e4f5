<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Analysis: BTC Big Move Hunter v2.3</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Calm Harmony Neutrals -->
    <!-- Application Structure Plan: A single-page, scrolling dashboard design. The structure guides the user through a narrative: 1. Introduction to the indicator. 2. Deconstruction of its original logic via an interactive diagram. 3. Highlighting key weaknesses. 4. Presenting proposed enhancements in a clear, card-based format. 5. The centerpiece: an interactive chart visualizing the Walk-Forward Analysis results, allowing users to connect parameters to performance. 6. A visual guide to the long-term maintenance plan. This structure transforms a dense report into an exploratory journey, prioritizing user understanding and engagement over a linear presentation. -->
    <!-- Visualization & Content Choices: 
        - Report Info: Core indicator architecture -> Goal: Organize/Inform -> Viz: Interactive HTML/CSS flowchart -> Interaction: Click to reveal details -> Justification: Simplifies complex architecture into explorable components.
        - Report Info: Walk-Forward Analysis data table -> Goal: Compare/Analyze -> Viz: Chart.js Line Chart -> Interaction: Hover for KPIs, click to update a detailed info panel -> Justification: Makes the quantitative results tangible and allows users to discover the relationship between parameters and out-of-sample performance.
        - Report Info: Proposed enhancements -> Goal: Inform/Explain -> Viz: Tabbed content/cards with icons -> Interaction: Click tabs to switch content -> Justification: Organizes multiple solutions into digestible, thematic sections.
        - Report Info: Long-term maintenance plan -> Goal: Organize/Instruct -> Viz: HTML/CSS process diagram -> Interaction: Static visual -> Justification: Clarifies a multi-step process more effectively than text alone.
        - Library/Method: Chart.js for canvas-based charts, Tailwind CSS for all layouts and diagrams. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #FDFBF7;
            color: #3D405B;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }
        .nav-link {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .nav-link:hover, .nav-link.active {
            color: #E07A5F;
            border-bottom-color: #E07A5F;
        }
        .section-card {
            background-color: #FFFFFF;
            border: 1px solid #F0EAD2;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
            transition: all 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .tab.active {
            background-color: #81B29A;
            color: white;
        }
        .tab {
            transition: background-color 0.3s ease;
        }
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        .arrow {
            font-size: 2rem;
            color: #81B29A;
            line-height: 1;
        }
    </style>
</head>
<body class="smooth-scroll">

    <header class="bg-white/80 backdrop-blur-md sticky top-0 z-50 border-b border-gray-200">
        <nav class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex-shrink-0">
                    <h1 class="text-xl font-bold text-gray-800">BTC Indicator Analysis</h1>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#logic" class="nav-link text-gray-600 hover:text-[#E07A5F] px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent">Core Logic</a>
                        <a href="#weaknesses" class="nav-link text-gray-600 hover:text-[#E07A5F] px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent">Weaknesses</a>
                        <a href="#enhancements" class="nav-link text-gray-600 hover:text-[#E07A5F] px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent">Enhancements</a>
                        <a href="#performance" class="nav-link text-gray-600 hover:text-[#E07A5F] px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent">Performance</a>
                        <a href="#blueprint" class="nav-link text-gray-600 hover:text-[#E07A5F] px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent">Blueprint</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        <section id="intro" class="text-center mb-20">
            <h2 class="text-4xl font-bold tracking-tight text-[#3D405B] sm:text-5xl">Interactive Analysis of the BTC Big Move Hunter v2.3</h2>
            <p class="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">An interactive exploration of a professional-grade trading indicator. This dashboard deconstructs its logic, reveals its weaknesses, and visualizes its performance potential through rigorous backtesting and proposed enhancements.</p>
        </section>

        <section id="logic" class="mb-20 pt-16 -mt-16">
            <h3 class="text-3xl font-bold text-center mb-4 text-[#3D405B]">Indicator Architecture Deconstructed</h3>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">The indicator's original design is a multi-layered system. Click on each component to understand its role in generating a trade signal.</p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 items-center text-center">
                <div class="logic-component section-card p-6 cursor-pointer" data-component="scoring">
                    <div class="text-5xl mb-4">🧮</div>
                    <h4 class="text-xl font-semibold mb-2">Confluence Scoring Engine</h4>
                    <p class="text-sm text-gray-500">Quantifies bullish/bearish conviction from multiple indicators.</p>
                </div>
                <div class="arrow hidden md:block">&rarr;</div>
                <div class="logic-component section-card p-6 cursor-pointer" data-component="filters">
                     <div class="text-5xl mb-4">🛡️</div>
                    <h4 class="text-xl font-semibold mb-2">The Filter Gauntlet</h4>
                    <p class="text-sm text-gray-500">A seven-layer defense to prevent entries in suboptimal conditions.</p>
                </div>
                <div class="arrow hidden md:block">&rarr;</div>
                 <div class="logic-component section-card p-6 cursor-pointer" data-component="state">
                    <div class="text-5xl mb-4">⚙️</div>
                    <h4 class="text-xl font-semibold mb-2">State Management</h4>
                    <p class="text-sm text-gray-500">Ensures signal integrity and prevents "repainting" of historical signals.</p>
                </div>
            </div>
            <div id="logic-details" class="mt-8 p-6 bg-gray-50 rounded-lg border border-gray-200 min-h-[150px] flex items-center justify-center">
                <p class="text-gray-500">Click a component above to see details.</p>
            </div>
        </section>

        <section id="weaknesses" class="mb-20 pt-16 -mt-16 bg-[#F0EAD2]/30 py-12 rounded-2xl">
            <h3 class="text-3xl font-bold text-center mb-4 text-[#3D405B]">Identified Weaknesses</h3>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">The analysis uncovered critical flaws that could undermine profitability. These issues suggest a lack of a unified strategic vision and adaptability.</p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div class="p-6">
                    <div class="text-5xl mb-4">⚖️</div>
                    <h4 class="text-xl font-semibold mb-2">Static Component Weighting</h4>
                    <p class="text-gray-600">Assumes the predictive power of each indicator (RSI, MACD, etc.) is constant in all market conditions, which is a major flaw in the dynamic crypto market.</p>
                </div>
                <div class="p-6">
                    <div class="text-5xl mb-4">⚔️</div>
                    <h4 class="text-xl font-semibold mb-2">Conflicting Filter Logic</h4>
                    <p class="text-gray-600">Some filters are redundant, while others have opposing goals (e.g., one prevents trades too far from the mean, another prevents them too close), creating strategic confusion.</p>
                </div>
                <div class="p-6">
                    <div class="text-5xl mb-4">⛓️</div>
                    <h4 class="text-xl font-semibold mb-2">Architectural Complexity</h4>
                    <p class="text-gray-600">A complex web of flags in the state management system makes the indicator brittle and difficult to maintain or extend without introducing bugs.</p>
                </div>
            </div>
        </section>

        <section id="enhancements" class="mb-20 pt-16 -mt-16">
            <h3 class="text-3xl font-bold text-center mb-4 text-[#3D405B]">Proposed Strategic Enhancements</h3>
            <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">To elevate the indicator to a professional, adaptive system, four key enhancements are proposed to improve its intelligence and context-awareness.</p>
            <div class="flex justify-center mb-8">
                <div class="flex space-x-1 p-1 bg-gray-200 rounded-lg">
                    <button class="tab active px-4 py-2 text-sm font-medium rounded-md" data-tab="supertrend">Dynamic SuperTrend</button>
                    <button class="tab px-4 py-2 text-sm font-medium rounded-md" data-tab="vwap">VWAP Filter</button>
                    <button class="tab px-4 py-2 text-sm font-medium rounded-md" data-tab="risk">Advanced Risk</button>
                    <button class="tab px-4 py-2 text-sm font-medium rounded-md" data-tab="session">Session Filter</button>
                </div>
            </div>
            <div id="enhancement-content" class="section-card p-8 min-h-[250px]">
            </div>
        </section>

        <section id="performance" class="mb-20 pt-16 -mt-16">
            <h3 class="text-3xl font-bold text-center mb-4 text-[#3D405B]">Performance Under the Microscope</h3>
            <p class="text-center text-gray-600 mb-12 max-w-3xl mx-auto">This chart displays the results of a rigorous Walk-Forward Analysis, simulating how the strategy would perform in a live environment. Hover or click on a data point to explore the detailed performance metrics and optimal parameters for each out-of-sample period.</p>
            <div class="chart-container">
                <canvas id="wfaChart"></canvas>
            </div>
            <div id="performance-details" class="mt-8 section-card p-6 min-h-[180px]">
                <p class="text-center text-gray-500">Hover or click a point on the chart for details.</p>
            </div>
        </section>

        <section id="blueprint" class="pt-16 -mt-16 bg-[#F0EAD2]/30 py-12 rounded-2xl">
             <h3 class="text-3xl font-bold text-center mb-4 text-[#3D405B]">Blueprint for Long-Term Success</h3>
            <p class="text-center text-gray-600 mb-12 max-w-3xl mx-auto">A successful strategy is not a static tool but a living system. This blueprint outlines the professional cycle of maintenance and deployment required for sustained profitability.</p>
            <div class="flex flex-col md:flex-row items-center justify-center space-y-8 md:space-y-0 md:space-x-8">
                <div class="text-center max-w-xs">
                    <div class="text-5xl mb-4">🔄</div>
                    <h4 class="text-xl font-semibold">1. Re-Optimization Cycle</h4>
                    <p class="text-gray-600 mt-2">Periodically (quarterly/semi-annually) re-run the Walk-Forward Analysis on new data to adapt parameters to evolving market conditions.</p>
                </div>
                <div class="arrow transform rotate-90 md:rotate-0">&rarr;</div>
                <div class="text-center max-w-xs">
                    <div class="text-5xl mb-4">🤖</div>
                    <h4 class="text-xl font-semibold">2. Webhook Automation</h4>
                    <p class="text-gray-600 mt-2">Use TradingView alerts and a bridge service to automate trade execution, removing emotional error and improving efficiency.</p>
                </div>
                <div class="arrow transform rotate-90 md:rotate-0">&rarr;</div>
                <div class="text-center max-w-xs">
                    <div class="text-5xl mb-4">📈</div>
                    <h4 class="text-xl font-semibold">3. Deploy & Monitor</h4>
                    <p class="text-gray-600 mt-2">Begin with paper trading to validate live performance against backtests, then proceed with live capital and continuous monitoring.</p>
                </div>
            </div>
        </section>

    </main>

    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="container mx-auto py-6 px-4 sm:px-6 lg:px-8 text-center text-gray-500">
            <p>&copy; 2024 Interactive Report. Generated from source analysis.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            
            const logicData = {
                scoring: {
                    title: 'Confluence Scoring Engine',
                    description: 'The core of the indicator. It calculates a buy and sell score based on a confluence of signals from different domains: Trend (200 EMAs), Momentum (MACD), Oscillators (adaptive RSI), Volatility (ADX, Volume Surges), and Market Structure (pivot analysis). A signal is only considered if the score difference and total confluence meet minimum thresholds. Its main weakness is that the weight of each component is static and does not adapt to market regimes.'
                },
                filters: {
                    title: 'The Filter Gauntlet',
                    description: 'A seven-layer defense mechanism to improve signal quality. It includes a Chop Filter, Volatility Filter, Exhaustion Filter, Momentum Gate, and more. A critical weakness is the presence of redundant and conflicting filters. For example, the Exhaustion Filter prevents trades far from the 200 EMA, while another filter prevents trades too close to it, creating logical inconsistency and an unresolved strategic identity.'
                },
                state: {
                    title: 'State Management & Execution',
                    description: 'Uses a system of flags and `barstate` checks to ensure signals are non-repainting and based only on confirmed data from the previous bar close. This guarantees signal integrity. However, the system is highly complex and brittle, relying on many interdependent flags. This "technical debt" makes the indicator difficult to maintain or extend without risking the introduction of subtle bugs.'
                }
            };

            const enhancementData = {
                supertrend: {
                    title: 'Evolving the SuperTrend from Static to Dynamic',
                    description: 'The original SuperTrend uses fixed parameters (ATR Length 10, Factor 4), making it rigid. The enhancement proposes making the ATR factor adaptive to the market\'s volatility regime. In high volatility, the factor increases to prevent whipsaws. In low volatility, it decreases to improve sensitivity. This embeds a self-regulating risk mechanism directly into the core trend logic.',
                    icon: '⚡'
                },
                vwap: {
                    title: 'Leveraging VWAP as a Regime and Value Filter',
                    description: 'The Volume-Weighted Average Price (VWAP) is an institutional benchmark. Integrating a daily VWAP with standard deviation bands provides two powerful filters: 1) A Regime Filter (only take longs above VWAP, shorts below). 2) A Value Filter (look for entries on pullbacks to the VWAP bands). This aligns the retail-indicator-heavy system with institutional flow.',
                    icon: '🏦'
                },
                risk: {
                    title: 'Advanced Risk & Trade Management',
                    description: 'The original indicator lacks defined profit targets. This enhancement introduces dynamic, ATR-based take-profit levels and a multi-stage exit strategy. For example: exit 50% of the position at 1.5R (Risk unit) profit, then move the stop-loss on the remainder to breakeven. This locks in gains, reduces risk, and smooths the equity curve.',
                    icon: '🎯'
                },
                session: {
                    title: 'Introducing a Market Session Volatility Filter',
                    description: 'The crypto market has distinct volatility periods. This enhancement adds a time-based filter to restrict trades to the most liquid sessions (e.g., London/New York overlap) and avoid low-volume periods like weekends. This is a proactive filter that avoids unfavorable conditions before they are confirmed by lagging indicators.',
                    icon: '⏰'
                }
            };

            const wfaData = {
                labels: ['2022 Q1', '2022 Q2', '2022 Q3/Q4', '2023 Q1', '2023 Q2', '2023 Q3/Q4'],
                datasets: [{
                    label: 'Out-of-Sample Net Profit ($)',
                    data: [8230, -1550, 4110, 11890, 2740, 6920],
                    borderColor: '#81B29A',
                    backgroundColor: 'rgba(129, 178, 154, 0.2)',
                    fill: true,
                    tension: 0.1,
                    pointRadius: 6,
                    pointHoverRadius: 10,
                    pointBackgroundColor: '#81B29A',
                    pointBorderColor: '#fff'
                }],
                details: [
                    { period: '2022 Q1', profit: '$8,230', sortino: '1.85', drawdown: '-4.1%', factor: '1.92', params: 'conf=5, adx=22, st_f=4.5, tsl_act=2.5' },
                    { period: '2022 Q2', profit: '-$1,550', sortino: '-0.31', drawdown: '-5.8%', factor: '0.88', params: 'conf=4, adx=25, st_f=5.0, tsl_act=2.0' },
                    { period: '2022 Q3/Q4', profit: '$4,110', sortino: '1.15', drawdown: '-3.5%', factor: '1.65', params: 'conf=5, adx=20, st_f=4.0, tsl_act=3.0' },
                    { period: '2023 Q1', profit: '$11,890', sortino: '2.54', drawdown: '-2.9%', factor: '2.33', params: 'conf=4, adx=22, st_f=4.0, tsl_act=2.5' },
                    { period: '2023 Q2', profit: '$2,740', sortino: '0.89', drawdown: '-4.9%', factor: '1.41', params: 'conf=6, adx=18, st_f=3.5, tsl_act=3.5' },
                    { period: '2023 Q3/Q4', profit: '$6,920', sortino: '1.67', drawdown: '-3.8%', factor: '1.81', params: 'conf=5, adx=20, st_f=4.5, tsl_act=2.5' }
                ]
            };

            const logicDetailContainer = document.getElementById('logic-details');
            document.querySelectorAll('.logic-component').forEach(function(el) {
                el.addEventListener('click', function(event) {
                    const componentKey = event.currentTarget.dataset.component;
                    const data = logicData[componentKey];
                    if (data) {
                        logicDetailContainer.innerHTML = `
                            <div class="text-left">
                                <h5 class="text-xl font-bold text-[#3D405B] mb-2">${data.title}</h5>
                                <p class="text-gray-600">${data.description}</p>
                            </div>
                        `;
                    }
                });
            });

            const enhancementContentContainer = document.getElementById('enhancement-content');
            const enhancementTabs = document.querySelectorAll('.tab');
            
            function updateEnhancementContent(tabKey) {
                const data = enhancementData[tabKey];
                 if (data) {
                    enhancementContentContainer.innerHTML = `
                        <div class="flex items-start space-x-6">
                            <div class="text-4xl">${data.icon}</div>
                            <div>
                                <h5 class="text-2xl font-bold text-[#3D405B] mb-2">${data.title}</h5>
                                <p class="text-gray-600 leading-relaxed">${data.description}</p>
                            </div>
                        </div>
                    `;
                }
            }
            
            enhancementTabs.forEach(function(tab) {
                tab.addEventListener('click', function(event) {
                    enhancementTabs.forEach(t => t.classList.remove('active'));
                    event.currentTarget.classList.add('active');
                    updateEnhancementContent(event.currentTarget.dataset.tab);
                });
            });
            updateEnhancementContent('supertrend');

            const performanceDetailContainer = document.getElementById('performance-details');
            function updatePerformanceDetails(index) {
                if (index === null || index === undefined) {
                    performanceDetailContainer.innerHTML = `<p class="text-center text-gray-500">Hover or click a point on the chart for details.</p>`;
                    return;
                }
                const detail = wfaData.details[index];
                if (detail) {
                    performanceDetailContainer.innerHTML = `
                        <h5 class="text-xl font-bold text-center mb-4 text-[#3D405B]">Details for ${detail.period}</h5>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                            <div>
                                <p class="text-sm text-gray-500">Net Profit</p>
                                <p class="text-2xl font-semibold ${detail.profit.startsWith('-') ? 'text-red-500' : 'text-green-600'}">${detail.profit}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Sortino Ratio</p>
                                <p class="text-2xl font-semibold">${detail.sortino}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Max Drawdown</p>
                                <p class="text-2xl font-semibold">${detail.drawdown}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Profit Factor</p>
                                <p class="text-2xl font-semibold">${detail.factor}</p>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <p class="text-sm text-center text-gray-500">Optimal Parameters Used:</p>
                            <p class="text-center font-mono text-sm text-[#E07A5F] mt-1">${detail.params}</p>
                        </div>
                    `;
                }
            }

            const ctx = document.getElementById('wfaChart').getContext('2d');
            const wfaChart = new Chart(ctx, {
                type: 'line',
                data: wfaData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            grid: {
                                color: '#e5e7eb'
                            },
                            ticks: {
                                callback: function(value, index, values) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        },
                        x: {
                             grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: true,
                            mode: 'index',
                            intersect: false,
                            backgroundColor: '#3D405B',
                            titleFont: { weight: 'bold' },
                            bodyFont: { size: 14 },
                            padding: 12,
                            callbacks: {
                                label: function(context) {
                                    const detail = wfaData.details[context.dataIndex];
                                    return `Profit: ${detail.profit}`;
                                },
                                afterBody: function(context) {
                                    const detail = wfaData.details[context[0].dataIndex];
                                    return `Sortino: ${detail.sortino}\nDrawdown: ${detail.drawdown}\nFactor: ${detail.factor}`;
                                }
                            }
                        }
                    },
                    onHover: (event, chartElement) => {
                        if (chartElement.length > 0) {
                            updatePerformanceDetails(chartElement[0].index);
                        } else {
                            updatePerformanceDetails(null);
                        }
                    },
                    onClick: (event, chartElement) => {
                         if (chartElement.length > 0) {
                            updatePerformanceDetails(chartElement[0].index);
                        }
                    }
                }
            });

            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
        });
    </script>
</body>
</html>
