#!/usr/bin/env python3
"""Test the simplified workflow with proper image"""

import logging
logging.basicConfig(level=logging.INFO)

def test_with_real_chart():
    try:
        # Add Agent_Trading to path
        import sys
        import os
        sys.path.insert(0, os.path.join(os.getcwd(), 'Agent_Trading'))
        
        from helpers.simplified_gemini_workflow import generate_analysis_simplified
        from PIL import Image
        import io
        
        # Create a simple test image
        img = Image.new('RGB', (800, 600), color='white')
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        chart_data = img_bytes.getvalue()
        
        print("🧪 Testing simplified workflow with real image data...")
        
        result = generate_analysis_simplified(
            chart_images=[chart_data], 
            analysis_mode='scalp', 
            user_query='Test BTC scalping analysis',
            market_specialization='Crypto'
        )
        
        print('✅ Test Results:')
        print('Success:', result.get('success'))
        if result.get('success'):
            print('Analysis preview:', result.get('analysis', '')[:200] + '...')
            print('Tool results keys:', list(result.get('tool_results', {}).keys()))
        else:
            print('Error:', result.get('error'))
        
        return result
        
    except Exception as e:
        print(f'❌ Test failed: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_with_real_chart()
