# 🚦 GEMINI API RATE LIMITS - OFFICIAL REFERENCE

**Source**: [Google AI Gemini API Documentation](https://ai.google.dev/gemini-api/docs/rate-limits)  
**Last Updated**: August 3, 2025

## 📊 RATE LIMIT DIMENSIONS

Rate limits are measured across **three dimensions**:

- **RPM**: Requests per minute
- **TPM**: Tokens per minute (input + output)
- **RPD**: Requests per day

**Important**: Exceeding ANY limit triggers a rate limit error.

## 🎯 USAGE TIERS

### Free Tier
- **Qualification**: Users in eligible countries
- **Cost**: Free
- **Upgrade**: Link billing account → Tier 1

### Tier 1
- **Qualification**: Billing account linked to project
- **Cost**: Pay-per-use
- **Upgrade**: Spend >$250 + 30 days → Tier 2

### Tier 2
- **Qualification**: Total spend >$250 + 30 days since payment
- **Cost**: Pay-per-use
- **Upgrade**: Spend >$1,000 + 30 days → Tier 3

### Tier 3
- **Qualification**: Total spend >$1,000 + 30 days since payment
- **Cost**: Pay-per-use
- **Benefits**: Maximum rate limits

## 📈 DETAILED RATE LIMITS BY MODEL

### 🤖 GEMINI 2.5 PRO

| Tier | RPM | TPM | RPD | Batch Tokens |
|------|-----|-----|-----|--------------|
| Free | 5 | 250,000 | 100 | - |
| Tier 1 | 150 | 2,000,000 | 10,000 | 5,000,000 |
| Tier 2 | 1,000 | 5,000,000 | 50,000 | 500,000,000 |
| Tier 3 | 2,000 | 8,000,000 | No limit | 1,000,000,000 |

### ⚡ GEMINI 2.5 FLASH

| Tier | RPM | TPM | RPD | Batch Tokens |
|------|-----|-----|-----|--------------|
| Free | 10 | 250,000 | 250 | - |
| Tier 1 | 1,000 | 1,000,000 | 10,000 | 3,000,000 |
| Tier 2 | 2,000 | 3,000,000 | 100,000 | 400,000,000 |
| Tier 3 | 10,000 | 8,000,000 | No limit | 1,000,000,000 |

### 🚀 GEMINI 2.0 FLASH

| Tier | RPM | TPM | RPD | Batch Tokens |
|------|-----|-----|-----|--------------|
| Free | 15 | 1,000,000 | 200 | - |
| Tier 1 | 2,000 | 4,000,000 | No limit | 10,000,000 |
| Tier 2 | 10,000 | 10,000,000 | No limit | 1,000,000,000 |
| Tier 3 | 30,000 | 30,000,000 | No limit | 5,000,000,000 |

### 💨 GEMINI 2.5 FLASH-LITE

| Tier | RPM | TPM | RPD | Batch Tokens |
|------|-----|-----|-----|--------------|
| Free | 15 | 250,000 | 1,000 | - |
| Tier 1 | 4,000 | 4,000,000 | No limit | 10,000,000 |
| Tier 2 | 10,000 | 10,000,000 | No limit | 500,000,000 |
| Tier 3 | 30,000 | 30,000,000 | No limit | 1,000,000,000 |

### 🔥 GEMINI 2.0 FLASH-LITE

| Tier | RPM | TPM | RPD | Batch Tokens |
|------|-----|-----|-----|--------------|
| Free | 30 | 1,000,000 | 200 | - |
| Tier 1 | 4,000 | 4,000,000 | No limit | 10,000,000 |
| Tier 2 | 20,000 | 10,000,000 | No limit | 1,000,000,000 |
| Tier 3 | 30,000 | 30,000,000 | No limit | 5,000,000,000 |

## ⏰ RESET SCHEDULES

- **RPM & TPM**: Reset every 60 seconds
- **RPD**: Reset at midnight Pacific time
- **Batch Tokens**: Ongoing limit across all active batch jobs

## 🎯 TRADING ANALYSIS RECOMMENDATIONS

### For Your AI Trading Tool:

**Free Tier** (Current):
- Use **Gemini 2.0 Flash** (15 RPM, 1M TPM, 200 RPD)
- Best balance of speed and limits for free tier
- Suitable for 3-4 analyses per hour

**Tier 1** (Recommended):
- Use **Gemini 2.5 Flash** (1,000 RPM, 1M TPM, 10K RPD)
- Perfect for active trading analysis
- Supports 100+ analyses per day

**Tier 2** (Professional):
- Use **Gemini 2.5 Pro** (1,000 RPM, 5M TPM, 50K RPD)
- Best quality analysis with high limits
- Supports enterprise-level usage

## 🚀 OPTIMIZATION STRATEGIES

### 1. **Model Selection**
```python
# Free Tier: Use 2.0 Flash for best limits
model = "gemini-2.0-flash-exp"

# Tier 1+: Use 2.5 Flash for speed
model = "gemini-2.5-flash"

# Tier 2+: Use 2.5 Pro for quality
model = "gemini-2.5-pro"
```

### 2. **Request Batching**
- Combine multiple analyses in single request
- Use batch mode for historical analysis
- Implement request queuing

### 3. **Token Optimization**
- Compress images to 800px max
- Use intelligent prompt compression
- Implement response caching

### 4. **Error Handling**
```python
# Exponential backoff for rate limits
import time
import random

def retry_with_backoff(func, max_retries=3):
    for attempt in range(max_retries):
        try:
            return func()
        except RateLimitError:
            wait_time = (2 ** attempt) + random.uniform(0, 1)
            time.sleep(wait_time)
    raise Exception("Max retries exceeded")
```

## 📊 MONITORING & TRACKING

Our rate limiter tracks:
- ✅ Real-time RPM/TPM/RPD usage
- ✅ Usage percentages and warnings
- ✅ Historical usage patterns
- ✅ Tier upgrade recommendations
- ✅ Cost optimization insights

## 🔄 UPGRADE PROCESS

1. **Free → Tier 1**: Link billing account in Google Cloud Console
2. **Tier 1 → Tier 2**: Spend $250+ and wait 30 days
3. **Tier 2 → Tier 3**: Spend $1,000+ and wait 30 days

**Upgrade Request**: Use [AI Studio API keys page](https://aistudio.google.com/app/apikey)

## ⚠️ IMPORTANT NOTES

- Rate limits are **per project**, not per API key
- Experimental models have more restrictive limits
- Batch mode has separate token limits
- Live API has session-based limits
- Specified limits are not guaranteed (actual capacity may vary)

## 🎯 FOR YOUR TRADING SYSTEM

**Current Status**: Free Tier
**Recommended**: Upgrade to Tier 1 for serious trading
**Model**: Use `gemini-2.0-flash-exp` for best free tier performance
**Monitoring**: Real-time tracking implemented ✅
