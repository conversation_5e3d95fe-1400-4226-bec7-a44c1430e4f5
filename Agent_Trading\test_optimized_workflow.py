#!/usr/bin/env python3
"""
Test the optimized workflow with sophisticated prompts and compression.
"""

import os
import sys
import logging
from PIL import Image

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from helpers.complete_langgraph_workflow import CompleteLangGraphTradingWorkflow
from helpers.utils import load_google_api_key

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_optimized_workflow():
    """Test the optimized workflow with sophisticated prompts."""
    
    print("=== TESTING OPTIMIZED WORKFLOW ===")
    
    # Load API key
    google_api_key = load_google_api_key()
    if not google_api_key:
        print("❌ No Google API key found")
        return
    
    # Initialize workflow
    workflow = CompleteLangGraphTradingWorkflow(google_api_key)
    
    # Check prompts loading
    print(f"Prompts loaded: {list(workflow.prompts.keys())}")
    
    # Check sophisticated prompt sizes
    for prompt_name, prompt_content in workflow.prompts.items():
        if isinstance(prompt_content, str):
            print(f"{prompt_name} prompt length: {len(prompt_content)} chars")
            if len(prompt_content) > 8000:
                print(f"  ⚠️  {prompt_name} prompt is large - will use compression")
            else:
                print(f"  ✅ {prompt_name} prompt size is optimal")
    
    # Test with real chart
    print("\n=== TESTING WITH REAL CHART ===")
    
    # Load a real chart image
    chart_path = "memory/images/0ca45d52c38eda9f9249d88c3084414c.png"
    if os.path.exists(chart_path):
        try:
            with open(chart_path, 'rb') as f:
                chart_data = f.read()
            
            chart_image = Image.open(chart_path)
            print(f"✅ Loaded real chart: {chart_path} ({len(chart_data)} bytes)")
            
            # Test the workflow
            result = workflow.analyze_trading_chart([chart_image], analysis_mode="positional")
            
            print(f"Analysis result keys: {list(result.keys())}")
            print(f"Success: {result.get('success')}")
            
            if result.get('error'):
                print(f"Error: {result['error']}")
            
            if result.get('analysis'):
                print(f"Analysis length: {len(result['analysis'])}")
            
            # Check if sophisticated prompts were used
            if result.get('success'):
                print("✅ Sophisticated prompts working correctly!")
            else:
                print("❌ Workflow failed - check logs for details")
                
        except Exception as e:
            print(f"❌ Chart loading failed: {e}")
    else:
        print(f"❌ Chart not found: {chart_path}")

def test_compression_methods():
    """Test the new compression methods."""
    
    print("\n=== TESTING COMPRESSION METHODS ===")
    
    # Load API key
    google_api_key = load_google_api_key()
    if not google_api_key:
        print("❌ No Google API key found")
        return
    
    # Initialize workflow
    workflow = CompleteLangGraphTradingWorkflow(google_api_key)
    
    # Test tool summary compression
    mock_tool_summaries = {
        "summarized_tools": {
            "market_data": {
                "summary": "BTC/USDT is trading at $45,000 with strong bullish momentum. Volume is increasing and RSI shows overbought conditions. Support at $44,500 and resistance at $46,000. News sentiment is positive with institutional buying."
            },
            "news_analysis": {
                "summary": "Recent news shows positive sentiment for crypto markets. Bitcoin ETF approval rumors are driving institutional interest. Federal Reserve policy remains accommodative which is bullish for risk assets."
            }
        }
    }
    
    try:
        compressed_tools = workflow._create_compressed_tool_summary(
            mock_tool_summaries, "BTC/USDT", "crypto"
        )
        print(f"✅ Tool compression test:")
        print(f"   Original: ~400 chars")
        print(f"   Compressed: {len(compressed_tools)} chars")
        print(f"   Result: {compressed_tools[:100]}...")
        
    except Exception as e:
        print(f"❌ Tool compression failed: {e}")
    
    # Test RAG summary compression
    mock_rag_context = {
        "relevant_memories": [
            {
                "content": "Previous BTC analysis showed strong support at $44,000 level. When price held above this level, it typically rallied to $46,000-$47,000 range. Risk management suggested 2% stop loss."
            },
            {
                "content": "Historical pattern: BTC tends to consolidate before major moves. Volume expansion usually precedes breakouts. Best entry timing is on pullbacks to key support levels."
            }
        ]
    }
    
    try:
        compressed_rag = workflow._create_compressed_rag_summary(
            mock_rag_context, "BTC/USDT", "crypto", "positional"
        )
        print(f"✅ RAG compression test:")
        print(f"   Original: ~300 chars")
        print(f"   Compressed: {len(compressed_rag)} chars")
        print(f"   Result: {compressed_rag[:100]}...")
        
    except Exception as e:
        print(f"❌ RAG compression failed: {e}")

if __name__ == "__main__":
    test_optimized_workflow()
    test_compression_methods()
