#!/usr/bin/env python3
"""
Comprehensive LLM Logging System for Trading Analysis
Logs all prompts, responses, tool calls, and debugging information.
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

class LLMLogger:
    """Comprehensive logger for LLM interactions, tool calls, and debugging."""
    
    def __init__(self, log_dir: str = "logs"):
        """Initialize the LLM logger."""
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Create separate log files for different types
        self.llm_log_file = self.log_dir / "llm_interactions.jsonl"
        self.tool_log_file = self.log_dir / "tool_calls.jsonl"
        self.error_log_file = self.log_dir / "errors.jsonl"
        self.debug_log_file = self.log_dir / "debug.jsonl"
        
        # Setup standard logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"LLM Logger initialized. Logs will be saved to: {self.log_dir}")
    
    def log_llm_interaction(self, 
                           prompt: str, 
                           response: str, 
                           model: str = "gemini",
                           context: Optional[Dict[str, Any]] = None,
                           execution_time: Optional[float] = None,
                           token_usage: Optional[Dict[str, int]] = None) -> str:
        """Log LLM prompt and response interaction."""
        
        interaction_id = f"llm_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        log_entry = {
            "interaction_id": interaction_id,
            "timestamp": datetime.now().isoformat(),
            "type": "llm_interaction",
            "model": model,
            "prompt": prompt,
            "response": response,
            "context": context or {},
            "execution_time_seconds": execution_time,
            "token_usage": token_usage,
            "prompt_length": len(prompt),
            "response_length": len(response)
        }
        
        self._write_log_entry(self.llm_log_file, log_entry)
        self.logger.info(f"Logged LLM interaction: {interaction_id}")
        
        return interaction_id
    
    def log_tool_call(self,
                     tool_name: str,
                     tool_args: Dict[str, Any],
                     tool_response: Any,
                     success: bool = True,
                     error: Optional[str] = None,
                     execution_time: Optional[float] = None,
                     context: Optional[Dict[str, Any]] = None) -> str:
        """Log tool call and response."""
        
        call_id = f"tool_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        log_entry = {
            "call_id": call_id,
            "timestamp": datetime.now().isoformat(),
            "type": "tool_call",
            "tool_name": tool_name,
            "tool_args": tool_args,
            "tool_response": tool_response,
            "success": success,
            "error": error,
            "execution_time_seconds": execution_time,
            "context": context or {}
        }
        
        self._write_log_entry(self.tool_log_file, log_entry)
        self.logger.info(f"Logged tool call: {tool_name} ({call_id})")
        
        return call_id
    
    def log_error(self,
                  error_type: str,
                  error_message: str,
                  context: Optional[Dict[str, Any]] = None,
                  stack_trace: Optional[str] = None) -> str:
        """Log error with context."""
        
        error_id = f"error_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        log_entry = {
            "error_id": error_id,
            "timestamp": datetime.now().isoformat(),
            "type": "error",
            "error_type": error_type,
            "error_message": error_message,
            "context": context or {},
            "stack_trace": stack_trace
        }
        
        self._write_log_entry(self.error_log_file, log_entry)
        self.logger.error(f"Logged error: {error_type} ({error_id})")
        
        return error_id
    
    def log_debug(self,
                  debug_type: str,
                  message: str,
                  data: Optional[Dict[str, Any]] = None,
                  context: Optional[Dict[str, Any]] = None) -> str:
        """Log debug information."""
        
        debug_id = f"debug_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        log_entry = {
            "debug_id": debug_id,
            "timestamp": datetime.now().isoformat(),
            "type": "debug",
            "debug_type": debug_type,
            "message": message,
            "data": data or {},
            "context": context or {}
        }
        
        self._write_log_entry(self.debug_log_file, log_entry)
        self.logger.debug(f"Logged debug info: {debug_type} ({debug_id})")
        
        return debug_id
    
    def log_workflow_step(self,
                         step_name: str,
                         step_data: Dict[str, Any],
                         context: Optional[Dict[str, Any]] = None) -> str:
        """Log workflow step for debugging."""
        
        return self.log_debug(
            debug_type="workflow_step",
            message=f"Workflow step: {step_name}",
            data=step_data,
            context=context
        )
    
    def _write_log_entry(self, log_file: Path, entry: Dict[str, Any]):
        """Write log entry to JSONL file."""
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(entry, ensure_ascii=False) + '\n')
        except Exception as e:
            self.logger.error(f"Failed to write log entry: {e}")
    
    def get_recent_logs(self, log_type: str = "llm", limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent log entries for debugging."""
        
        log_file_map = {
            "llm": self.llm_log_file,
            "tool": self.tool_log_file,
            "error": self.error_log_file,
            "debug": self.debug_log_file
        }
        
        log_file = log_file_map.get(log_type)
        if not log_file or not log_file.exists():
            return []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_lines = lines[-limit:] if len(lines) > limit else lines
                return [json.loads(line.strip()) for line in recent_lines if line.strip()]
        except Exception as e:
            self.logger.error(f"Failed to read recent logs: {e}")
            return []
    
    def get_logs_by_context(self, 
                           context_key: str, 
                           context_value: str, 
                           log_type: str = "llm") -> List[Dict[str, Any]]:
        """Get logs filtered by context."""
        
        all_logs = self.get_recent_logs(log_type, limit=1000)  # Get more for filtering
        
        filtered_logs = []
        for log_entry in all_logs:
            context = log_entry.get("context", {})
            if context.get(context_key) == context_value:
                filtered_logs.append(log_entry)
        
        return filtered_logs
    
    def create_debug_summary(self, symbol: str = None) -> Dict[str, Any]:
        """Create a debug summary for troubleshooting."""
        
        summary = {
            "timestamp": datetime.now().isoformat(),
            "symbol_filter": symbol,
            "recent_llm_interactions": len(self.get_recent_logs("llm", 50)),
            "recent_tool_calls": len(self.get_recent_logs("tool", 50)),
            "recent_errors": len(self.get_recent_logs("error", 50)),
            "log_files": {
                "llm_log": str(self.llm_log_file),
                "tool_log": str(self.tool_log_file),
                "error_log": str(self.error_log_file),
                "debug_log": str(self.debug_log_file)
            }
        }
        
        if symbol:
            summary["symbol_specific"] = {
                "llm_interactions": len(self.get_logs_by_context("symbol", symbol, "llm")),
                "tool_calls": len(self.get_logs_by_context("symbol", symbol, "tool")),
                "errors": len(self.get_logs_by_context("symbol", symbol, "error"))
            }
        
        return summary

# Global logger instance
llm_logger = LLMLogger()

def get_llm_logger() -> LLMLogger:
    """Get the global LLM logger instance."""
    return llm_logger
