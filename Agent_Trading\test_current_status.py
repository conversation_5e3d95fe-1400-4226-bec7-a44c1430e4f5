#!/usr/bin/env python3
"""
Quick test to check current system status
"""

import sys
import os
sys.path.append('.')

def test_rate_limiter():
    """Test rate limiter status"""
    try:
        from helpers.gemini_rate_limiter import get_rate_limiter

        rate_limiter = get_rate_limiter('free')
        usage = rate_limiter.get_usage_summary()

        print("🚦 RATE LIMITER STATUS:")
        print("=" * 50)

        models = usage.get('models', {})
        for model, stats in models.items():
            # Parse requests_today from string format "X/Y"
            requests_str = stats.get('requests_today', '0/0')
            if '/' in requests_str:
                requests_today, daily_limit = requests_str.split('/')
                requests_today = int(requests_today)
                daily_limit = int(daily_limit) if daily_limit != '∞' else 999999
            else:
                requests_today = 0
                daily_limit = 0

            remaining = daily_limit - requests_today
            status = "✅ Available" if remaining > 0 else "❌ Exhausted"
            print(f"{model:20} | {requests_today:3}/{daily_limit:3} | {remaining:3} remaining | {status}")

        return True
    except Exception as e:
        print(f"❌ Rate limiter test failed: {e}")
        return False

def test_trading_economics():
    """Test TradingEconomics authentication"""
    try:
        from helpers.economic_calendar_tool import EconomicCalendarTool
        
        tool = EconomicCalendarTool()
        print(f"\n💰 TRADING ECONOMICS STATUS:")
        print("=" * 50)
        print(f"Authenticated: {'✅ Yes' if tool.api_authenticated else '❌ No'}")
        
        return tool.api_authenticated
    except Exception as e:
        print(f"❌ TradingEconomics test failed: {e}")
        return False

def test_basic_functionality():
    """Test basic system components"""
    try:
        from helpers.complete_langgraph_workflow import CompleteLangGraphTradingWorkflow
        from helpers.utils import load_google_api_key
        
        print(f"\n🤖 SYSTEM COMPONENTS:")
        print("=" * 50)
        
        # Test API key loading
        api_key = load_google_api_key()
        print(f"Google API Key: {'✅ Loaded' if api_key else '❌ Missing'}")
        
        # Test workflow initialization
        workflow = CompleteLangGraphTradingWorkflow(
            google_api_key=api_key,
            tier='free',
            preferred_model='gemini-2.5-pro'
        )
        print(f"Workflow Init: ✅ Success")
        print(f"Selected Model: {workflow.model.model_name}")
        
        return True
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔍 AGENT TRADING SYSTEM STATUS CHECK")
    print("=" * 60)
    
    # Run all tests
    rate_limiter_ok = test_rate_limiter()
    trading_economics_ok = test_trading_economics()
    system_ok = test_basic_functionality()
    
    print(f"\n📊 OVERALL STATUS:")
    print("=" * 50)
    print(f"Rate Limiter:      {'✅ OK' if rate_limiter_ok else '❌ FAILED'}")
    print(f"TradingEconomics:  {'✅ OK' if trading_economics_ok else '❌ FAILED'}")
    print(f"System Components: {'✅ OK' if system_ok else '❌ FAILED'}")
    
    if rate_limiter_ok and trading_economics_ok and system_ok:
        print(f"\n🎉 SYSTEM READY FOR TRADING ANALYSIS!")
    else:
        print(f"\n⚠️  SYSTEM NEEDS ATTENTION - CHECK ERRORS ABOVE")
