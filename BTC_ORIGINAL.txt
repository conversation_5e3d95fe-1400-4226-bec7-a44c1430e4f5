//@version=6
indicator("BTC Big Move Hunter v2.3 - OPTIMIZED", shorttitle="BTC_SNIPER_V3_OPT", overlay=true, max_boxes_count=50, max_labels_count=50)

// =================== SETTINGS ===================
// Big Move Settings - OPTIMIZED
use_aggressive_entry = input.bool(false, "Use Aggressive Entry Mode", group="Big Move Settings", tooltip="If true, enters on the first SuperTrend flip instead of waiting for 3-bar stability. Captures trends earlier but may have more false signals.")
min_confluence = input.int(4, "Minimum Confluence", minval=3, maxval=7, group="Big Move Settings", tooltip="Minimum number of confirming conditions required for a signal.")
trend_strength_required = input.int(20, "Minimum ADX for Strong Trend", minval=15, maxval=40, group="Big Move Settings", tooltip="Minimum ADX value on 1H timeframe to consider the trend strong.")
signal_cooldown = input.int(10, "Signal Cooldown (bars)", minval=5, maxval=50, group="Big Move Settings", tooltip="Minimum number of bars to wait before a new signal in the same direction can appear.")

// IMPROVED: Volume surge multiplier as input
volume_multiplier = input.float(1.5, "Volume Surge Multiplier", minval=1.2, maxval=2.5, step=0.1, group="Big Move Settings", tooltip="Multiplier for average volume to detect a volume surge.")

// Entry Timeframe Settings
entry_timeframe = input.string("15", "Entry Timeframe RSI", options=["1", "3", "5", "15"], group="Entry Settings", tooltip="Timeframe used for Entry RSI and primary SuperTrend.")

// NEW: Entry confirmation settings
use_entry_confirmation = input.bool(true, "Use Entry Confirmation", group="Entry Settings", tooltip="If true, an additional confirmation condition must be met for the signal.")
confirm_type = input.string("Structure Break", "Confirmation Type", options=["Structure Break", "RSI Cross", "Engulfing"], group="Entry Settings", tooltip="Type of confirmation: Market structure break, RSI crossing 50, or Engulfing candle pattern.")

// Visual Settings
show_signals = input.bool(true, "Show Entry Signals", group="Display", tooltip="Show/Hide Buy/Sell signal arrows on the chart.")
show_table = input.bool(false, "Show Information Table", group="Display", tooltip="Show/Hide the debug information table.")
show_structure = input.bool(true, "Show Market Structure", group="Display", tooltip="Show/Hide SuperTrend line, EMA 200, and background colors.")
show_signal_strength = input.bool(true, "Show Signal Strength", group="Display", tooltip="Show/Hide signal strength text with signals.")
show_tsl_details = input.bool(true, "Show TSL Details on Chart", group="Display", tooltip="Show/Hide ATR TSL line, ATR TSL exit markers, and SuperTrend TSL exit markers/lines.")
show_chop_filter = input.bool(true, "Show Chop Filter Overlay", group="Display", tooltip="Colors the background gray when trades are blocked by the chop filter.")

// NEW: Chop Filter Settings
use_chop_filter = input.bool(true, "Use Chop Filter", group="Chop Filter", tooltip="Enable the chop filter to avoid trading in sideways markets.")
chop_score_threshold = input.int(2, "Chop Score Threshold", group="Chop Filter", minval=1, maxval=4, tooltip="Block trades if the number of active chop conditions meets or exceeds this value. Default of 2 is more aggressive.")
chop_filter_threshold = input.float(61.8, "Choppiness Index Threshold", group="Chop Filter", minval=0, maxval=100, step=0.1)
adx_chop_threshold = input.int(15, "ADX Chop Threshold", group="Chop Filter", minval=10, maxval=25)
flat_ma_threshold = input.float(0.0005, "Flat MA Threshold (%)", group="Chop Filter", minval=0.0001, maxval=0.01, step=0.0001)
bbw_atr_ratio_threshold = input.float(1.0, "BBW/ATR Ratio Threshold", group="Chop Filter", minval=0.1, maxval=3.0, step=0.1)
// In "Chop Filter" settings group
use_volatility_filter = input.bool(true, "Use Volatility Filter", group="Chop Filter", tooltip="If true, blocks signals that occur in very low volatility conditions.")
vol_filter_period = input.int(50, "Volatility Filter Period", group="Chop Filter", tooltip="The lookback period for the volatility's moving average.")
vol_filter_mult = input.float(1.1, "Volatility Multiplier", group="Chop Filter", tooltip="The current volatility (ATR) must be this many times its average to allow a trade. >1.0 is stricter.")

// NEW: Trailing stop settings
use_trailing_stop = input.bool(true, "Use Trailing Stop", group="Risk Management", tooltip="Enable ATR-based Trailing Stop Loss.")
trailing_activation = input.float(3.0, "Trailing Activation (R:R)", minval=1.5, maxval=5.0, step=0.5, group="Risk Management", tooltip="R:R ratio (based on initial SL) at which the ATR Trailing Stop activates.")
atr_tsl_multiplier = input.float(2.5, "ATR TSL Multiplier (Standard)", minval=1.0, maxval=5.0, step=0.1, group="Risk Management", tooltip="ATR multiplier for TSL in normal conditions. Higher values make it looser.")
initial_risk_atr_multiplier = input.float(2.5, "Initial Risk ATR Multiplier", minval=1.0, maxval=5.0, step=0.1, group="Risk Management", tooltip="ATR multiplier for calculating the initial conceptual stop distance (risk amount).")
atr_tsl_period = input.int(40, "ATR TSL Period", minval=10, maxval=100, group="Risk Management", tooltip="ATR period for calculating the ATR trailing stop distance. Longer periods are less sensitive.")
tsl_exit_on_close = input.bool(false, "TSL Exit on Candle Close", group="Risk Management", tooltip="If true, ATR TSL requires a candle close beyond the TSL to trigger an exit. If false, exits on wick touch.")
atr_tsl_strong_trend_multiplier = input.float(3.5, "ATR TSL Multiplier (Strong Trend)", minval=1.5, maxval=7.0, step=0.1, group="Risk Management", tooltip="ATR multiplier for TSL when a strong trend is detected. Should be >= Standard Multiplier.")

// --- Enhanced Inputs for Dynamic Filtering and Momentum Prevention ---
stoch_k = input.int(5, "Stochastic K Period", minval=3, maxval=14, group="Momentum Filter")
stoch_d = input.int(3, "Stochastic D Period", minval=2, maxval=5, group="Momentum Filter")

// V1 Scoring System Constants
V1_SCORE_DIFF_THRESHOLD = 1.5 // Minimum score difference for a V1 style signal

// NEW: Higher-Timeframe Filter Settings
filter_type = input.string("None", "Higher Timeframe Filter", options=["None", "DailyEMA", "WeeklyEMA", "Donchian"], group="Confluence Extras", tooltip="Apply an additional filter based on higher timeframe analysis.")
daily_ema_len = input.int(21, "Daily EMA Length", minval=5, group="Confluence Extras", tooltip="Length for Daily EMA filter.")
weekly_ema_len = input.int(10, "Weekly EMA Length", minval=3, group="Confluence Extras", tooltip="Length for Weekly EMA filter.")
donchian_len = input.int(20, "Donchian Channel Length", minval=5, group="Confluence Extras", tooltip="Length for Donchian Channel breakout filter.")

// Add a new group after the "Momentum Filter" group
// --- Exhaustion Filter Settings ---
use_exhaustion_filter = input.bool(true, "Use Exhaustion Filter", group="Exhaustion Filter", tooltip="If true, blocks signals that occur when the price is already too far from its moving average baseline.")
exhaustion_ema_len = input.int(200, "Exhaustion EMA Length", group="Exhaustion Filter")
max_distance_from_ema = input.float(4.0, "Max Distance from EMA (%)", group="Exhaustion Filter", tooltip="Block signals if price is more than this % away from the Exhaustion EMA.")

// --- Momentum Gate Filter ---
use_momentum_gate = input.bool(true, "Use Momentum Gate Filter", group="Momentum Gate", tooltip="If true, requires the medium-term MACD to confirm momentum before allowing a signal.")
gate_tf = input.string("15", "Momentum Gate Timeframe", options=["5", "15", "30"], group="Momentum Gate")

// =================== QUICK WIN #1: CACHED SECURITY CALLS ===================
// OPTIMIZATION: Define functions that calculate everything for each timeframe

// ADX calculation function
adx_calc(len) =>
    up = ta.change(high)
    down = -ta.change(low)
    plusDM_raw = up > down and up > 0 ? up : 0
    minusDM_raw = down > up and down > 0 ? down : 0
    plusDM = na(up) ? na : plusDM_raw
    minusDM = na(down) ? na : minusDM_raw
    truerange = ta.rma(ta.tr, len)
    smoothed_plusDM = ta.rma(plusDM, len)
    smoothed_minusDM = ta.rma(minusDM, len)
    plus = fixnan(100 * smoothed_plusDM / truerange)
    minus = fixnan(100 * smoothed_minusDM / truerange)
    sum = plus + minus
    dx = sum == 0 ? 0 : math.abs(plus - minus) / sum
    adx = 100 * ta.rma(dx, len)
    adx

// Function to get all Daily timeframe data in ONE call
f_getDailyData() =>
    ema_daily = ta.ema(close, daily_ema_len)
    donchian_upper = ta.highest(high, donchian_len)
    donchian_lower = ta.lowest(low, donchian_len)
    [ema_daily, donchian_upper, donchian_lower]

// Function to get all Weekly timeframe data in ONE call
f_getWeeklyData() =>
    ema_weekly = ta.ema(close, weekly_ema_len)
    [ema_weekly]

// Function to get all 1-hour timeframe data in ONE call
f_get1HourData() =>
    ema50 = ta.ema(close, 50)
    ema200 = ta.ema(close, 200)
    exhaustion_ema = ta.ema(close, exhaustion_ema_len) // <-- ADD THIS LINE
    rsi_1h = ta.rsi(close, 14)
    [macd_line, signal_line, hist] = ta.macd(close, 12, 26, 9)
    adx_1h = adx_calc(14)
    [st_1h, direction_1h] = ta.supertrend(3, 10)
    [ema50, ema200, exhaustion_ema, rsi_1h, macd_line, signal_line, hist, adx_1h, st_1h, direction_1h] // <-- ADD the new variable here

// Function to get all 15-minute timeframe data in ONE call
f_get15MinData() =>
    rsi_15m = ta.rsi(close, 14)
    [st_15m, direction_15m] = ta.supertrend(3, 10)
    [rsi_15m, st_15m, direction_15m]

// Function to get all 4-hour timeframe data in ONE call
f_get4HourData() =>
    ema200_4h = ta.ema(close, 200)
    [ema200_4h]

// Function to get entry timeframe data in ONE call
f_getEntryTimeframeData() =>
    rsi_entry = ta.rsi(close, 14)
    [st_entry, direction_entry] = ta.supertrend(3, 10)
    [rsi_entry, st_entry, direction_entry]

// Function to get MACD data for the Momentum Gate timeframe
f_getGateMACD() =>
    [gate_macd, gate_signal, _] = ta.macd(close, 12, 26, 9)
    [gate_macd, gate_signal]

// OPTIMIZATION: Make only ONE request.security() call per timeframe
[daily_ema_val, daily_donchian_upper, daily_donchian_lower] = request.security(syminfo.tickerid, "D", f_getDailyData(), lookahead=barmerge.lookahead_off)
[weekly_ema_val] = request.security(syminfo.tickerid, "W", f_getWeeklyData(), lookahead=barmerge.lookahead_off)
[ema_50_1h, ema_200_1h, exhaustion_ema_1h, rsi_1h, macd_line_1h, signal_line_1h, hist_1h, adx_1h, st_1h, direction_1h] = request.security(syminfo.tickerid, "60", f_get1HourData(), lookahead=barmerge.lookahead_off)
[rsi_15m, st_15m, direction_15m] = request.security(syminfo.tickerid, "15", f_get15MinData(), lookahead=barmerge.lookahead_off)
[ema_200_4h] = request.security(syminfo.tickerid, "240", f_get4HourData(), lookahead=barmerge.lookahead_off)
// Fetch MACD data specifically for the Momentum Gate timeframe
[gate_macd, gate_signal] = request.security(syminfo.tickerid, gate_tf, f_getGateMACD(), lookahead=barmerge.lookahead_off)


// QUICK WIN #2: Avoid recomputing SuperTrend 3x
// Check if entry_timeframe matches existing timeframes to avoid redundant calculations
[rsi_entry, st_entry, direction_entry] = if entry_timeframe == "15"
    [rsi_15m, st_15m, direction_15m]  // Reuse 15m data
else if entry_timeframe == "60"
    [rsi_1h, st_1h, direction_1h]     // Reuse 1h data
else
    request.security(syminfo.tickerid, entry_timeframe, f_getEntryTimeframeData(), lookahead=barmerge.lookahead_off)

daily_bullish = close > daily_ema_val
weekly_bullish = close > weekly_ema_val
donch_break_long = close > daily_donchian_upper[1]
donch_break_short = close < daily_donchian_lower[1]

// =================== TREND ANALYSIS ===================
trend_1h = close > ema_200_1h ? 1 : -1
trend_4h = close > ema_200_4h ? 1 : -1

trend_alignment = math.abs(trend_1h + trend_4h)
major_trend_direction = (trend_1h + trend_4h) > 0 ? 1 : -1

strong_trend_condition = trend_alignment >= 2

// =================== MOMENTUM INDICATORS ===================
rsi_cross_up = ta.crossover(rsi_entry, 50)
rsi_cross_down = ta.crossunder(rsi_entry, 50)

k = ta.stoch(close, high, low, stoch_k)
d = ta.sma(k, stoch_d)
momentum_bullish = k > 20
momentum_bearish = k < 80

st_bullish = direction_entry == -1
st_bearish = direction_entry == 1

st_15m_bullish = direction_15m == -1
st_1h_bullish = direction_1h == -1

st_stable_bullish = st_bullish and st_bullish[1] and st_bullish[2]
st_stable_bearish = st_bearish and st_bearish[1] and st_bearish[2]

st_alignment_bull = (st_bullish ? 1 : 0) + (st_15m_bullish ? 1 : 0) + (st_1h_bullish ? 1 : 0)
st_alignment_bear = (st_bearish ? 1 : 0) + (not st_15m_bullish ? 1 : 0) + (not st_1h_bullish ? 1 : 0)

// =================== VOLUME ANALYSIS ===================
volume_ma = ta.sma(volume, 20)
volume_surge = volume > volume_ma * volume_multiplier

// =================== PERFORMANCE OPTIMIZATION ===================
should_calculate = bar_index > 50

// =================== LONGER-TERM REFACTOR: HELPER FUNCTIONS ===================

// OPTIMIZATION: Trend Check Helper Function - eliminates repeated trend condition blocks
f_checkTrendCondition(direction, target_direction, score_increment) =>
    condition_met = direction == target_direction
    score_add = condition_met ? score_increment : 0.0
    confluence_add = condition_met ? 1 : 0
    [condition_met, score_add, confluence_add]

// OPTIMIZATION: RSI Condition Helper Function - eliminates repeated RSI blocks
f_checkRSICondition(rsi_main, threshold, is_buy_signal, rsi_15m_val, rsi_1h_val) =>
    rsi_condition = if is_buy_signal
        rsi_main < threshold and rsi_15m_val < 85 and rsi_1h_val < 80
    else
        rsi_main > threshold and rsi_15m_val > 15 and rsi_1h_val > 20

    condition_met = not na(rsi_main) and not na(rsi_15m_val) and not na(rsi_1h_val) and rsi_condition
    score_add = condition_met ? 2.0 : 0.0
    confluence_add = condition_met ? 1 : 0
    [condition_met, score_add, confluence_add]

// OPTIMIZATION: MACD Condition Helper Function
f_checkMACDCondition(macd_line, signal_line, is_buy_signal) =>
    condition_met = not na(macd_line) and not na(signal_line) and
                   (is_buy_signal ? macd_line > signal_line : macd_line < signal_line)
    score_add = condition_met ? 1.0 : 0.0
    confluence_add = condition_met ? 1 : 0
    [condition_met, score_add, confluence_add]

// OPTIMIZATION: SuperTrend Alignment Helper Function - FIXED TO PREVENT FREEZING
f_checkSuperTrendAlignment(stable_condition, regular_condition, alignment_count, use_aggressive) =>
    // Calculate the conditions directly without complex logic
    stable_aligned = stable_condition and alignment_count >= 2
    regular_aligned = regular_condition and alignment_count >= 2

    // Initialize return values with simple assignments
    condition_met = false
    score_add = 0.0
    confluence_add = 0

    // Use simple if statements without nested conditions
    if stable_aligned
        condition_met := true
        score_add := 2.0
        confluence_add := 1
    else if regular_aligned
        condition_met := true
        score_add := 1.0
        confluence_add := 1

    [condition_met, score_add, confluence_add]

// OPTIMIZATION: Price Action Helper Function
f_checkPriceAction(is_buy_signal) =>
    condition_met = is_buy_signal ? (close > open and close > high[1]) : (close < open and close < low[1])
    score_add = condition_met ? 0.5 : 0.0
    confluence_add = condition_met ? 1 : 0
    [condition_met, score_add, confluence_add]

// OPTIMIZATION: Market Structure Helper Function
f_checkMarketStructure(market_structure, is_buy_signal) =>
    target_structure = is_buy_signal ? "BULLISH" : "BEARISH"
    condition_met = market_structure == target_structure
    score_add = condition_met ? 1.0 : 0.0
    confluence_add = condition_met ? 1 : 0
    [condition_met, score_add, confluence_add]

// OPTIMIZATION: Shared Conditions Helper Function - returns values instead of modifying globals
f_getSharedConditions() =>
    condition_adx_strong = not na(adx_1h) and adx_1h >= trend_strength_required
    condition_volume_surge = volume_surge
    [condition_adx_strong, condition_volume_surge]

// =================== V1 PIVOT-BASED MARKET STRUCTURE ===================
v1_pivot_high_val = ta.pivothigh(high, 15, 15)
v1_pivot_low_val = ta.pivotlow(low, 15, 15)

var v1_recent_highs = array.new<float>()
var v1_recent_lows = array.new<float>()

if not na(v1_pivot_high_val)
    array.push(v1_recent_highs, v1_pivot_high_val)
    if array.size(v1_recent_highs) > 3
        array.shift(v1_recent_highs)

if not na(v1_pivot_low_val)
    array.push(v1_recent_lows, v1_pivot_low_val)
    if array.size(v1_recent_lows) > 3
        array.shift(v1_recent_lows)

string v1_market_structure = "RANGING" // Added var
int v1_structure_strength_adjust = 0 // Added var

if array.size(v1_recent_highs) >= 2 and array.size(v1_recent_lows) >= 2
    v1_hh_count = 0
    v1_hl_count = 0
    v1_lh_count = 0
    v1_ll_count = 0

    if array.size(v1_recent_highs) > 1
        for i = 1 to array.size(v1_recent_highs) - 1
            if array.get(v1_recent_highs, i) > array.get(v1_recent_highs, i - 1)
                v1_hh_count += 1
            else
                v1_lh_count += 1

    if array.size(v1_recent_lows) > 1
        for i = 1 to array.size(v1_recent_lows) - 1
            if array.get(v1_recent_lows, i) > array.get(v1_recent_lows, i - 1)
                v1_hl_count += 1
            else
                v1_ll_count += 1

    v1_bullish_signals = v1_hh_count + v1_hl_count
    v1_bearish_signals = v1_lh_count + v1_ll_count

    if v1_bullish_signals > v1_bearish_signals + 1
        v1_market_structure := "BULLISH"
        v1_structure_strength_adjust := 5
    else if v1_bearish_signals > v1_bullish_signals + 1
        v1_market_structure := "BEARISH"
        v1_structure_strength_adjust := -5

// =================== V1 VOLATILITY REGIME & DYNAMIC RSI ===================
atr_short = ta.atr(10)
atr_long = ta.atr(40)
volatility_ratio = atr_short / atr_long

volatility_regime = switch
    volatility_ratio < 0.8 => "LOW"
    volatility_ratio < 1.2 => "MEDIUM"
    volatility_ratio < 1.5 => "HIGH"
    => "EXTREME"

min_confluence_dynamic = switch volatility_regime
    "LOW" => min_confluence + 1
    "MEDIUM" => min_confluence
    "HIGH" => min_confluence - 1
    "EXTREME" => min_confluence - 1

string v1_volatility_regime = "MEDIUM" // Added var
float v1_rsi_buy_threshold = 25 // Added var
float v1_rsi_sell_threshold = 75 // Added var
int v1_vol_adjustment = 0 // Added var
float v1_vol_percentile = na // Added var

if should_calculate
    v1_returns = math.log(close / close[1])
    v1_long_vol = ta.stdev(v1_returns, 200)
    v1_vol_percentile := ta.percentrank(v1_long_vol, 200)

    v1_volatility_regime := switch
        v1_vol_percentile < 25 => "LOW"
        v1_vol_percentile < 50 => "MEDIUM"
        v1_vol_percentile < 80 => "HIGH"
        => "EXTREME"

    v1_base_buy_rsi = 30
    v1_base_sell_rsi = 70

    v1_vol_adjustment := switch v1_volatility_regime
        "LOW" => 5
        "HIGH" => -5
        "EXTREME" => -10
        => 0

    v1_rsi_buy_threshold := math.max(15, math.min(45, v1_base_buy_rsi + v1_vol_adjustment + v1_structure_strength_adjust))
    v1_rsi_sell_threshold := math.min(85, math.max(55, v1_base_sell_rsi + v1_vol_adjustment + v1_structure_strength_adjust))

// =================== CHOP / SIDEWAYS MARKET FILTERS (MATCH ORIGINAL) ===================
chopIdx = math.log10(math.sum(ta.tr, 14) / (ta.highest(high, 14) - ta.lowest(low, 14))) * 100
flatMA_val = ema_200_1h > 0 ? math.abs(ta.change(ema_200_1h, 10) / ema_200_1h) : 0
[bb_middle, bb_upper, bb_lower] = ta.bb(close, 20, 2)
bb_width = bb_upper - bb_lower
bbw_atr_ratio = atr_short > 0 ? (bb_width / atr_short) : 0

is_choppy_idx = chopIdx > chop_filter_threshold
is_adx_weak = adx_1h < adx_chop_threshold
is_ma_flat = flatMA_val < flat_ma_threshold
is_bbw_tight = bbw_atr_ratio < bbw_atr_ratio_threshold

chop_score = (is_choppy_idx ? 1 : 0) + (is_adx_weak ? 1 : 0) + (is_ma_flat ? 1 : 0) + (is_bbw_tight ? 1 : 0)
trade_block = use_chop_filter and (chop_score >= chop_score_threshold)

// At the end of the "CHOP / SIDEWAYS MARKET FILTERS" section
atr_vol_ma = ta.sma(atr_short, vol_filter_period)
is_volatility_sufficient = not use_volatility_filter or (atr_short > atr_vol_ma * vol_filter_mult)

// After the volatility filter logic
distance_pct = not na(exhaustion_ema_1h) and exhaustion_ema_1h != 0 ? (close - exhaustion_ema_1h) / exhaustion_ema_1h * 100 : 0.0
is_not_exhausted = not use_exhaustion_filter or (math.abs(distance_pct) < max_distance_from_ema)

// =================== STATE AND LOCKING VARIABLES (Declare together) ===================
var string atr_tsl_active_direction = "NONE"
var bool plot_confirmed_signal_now = false
var bool final_signal = false
var bool signal_fired_in_current_trend = false

var string confirmed_signal_direction = "NONE"
var string confirmed_signal_strength_text = ""
var int confirmed_max_confluence = 0
var float confirmed_risk_for_tsl = na

var int bars_since_signal = 0

// Variables for holding calculation results of the current bar
string signal_direction = "NONE"
string current_signal_strength_text = "WEAK"
int current_max_confluence = 0
float risk = 0.0

// TSL State Variables (Moved here)
var float atr_trailing_stop_level = na
var float atr_tsl_entry_price = na
var float atr_tsl_initial_risk = na
var float atr_tsl_highest_rr_achieved = 0.0
var bool atr_tsl_is_activated_for_trade = false
var bool atr_tsl_hit_on_current_bar = false

var string st_tsl_active_direction = ""
var float st_tsl_entry_price = na
var line st_tsl_display_line = na
var label st_tsl_display_label = na
var bool supertrend_exit = false


// =================== 1. STATE UPDATE (This runs first!) ===================
if barstate.isnew
    plot_confirmed_signal_now := final_signal[1]
    if plot_confirmed_signal_now
        signal_fired_in_current_trend := true
        bars_since_signal := 0
        confirmed_signal_direction := signal_direction[1]
        confirmed_signal_strength_text := current_signal_strength_text[1]
        confirmed_max_confluence := current_max_confluence[1]
        confirmed_risk_for_tsl := risk[1]
        atr_tsl_active_direction := confirmed_signal_direction
        st_tsl_active_direction := confirmed_signal_direction

st_changed = st_bullish != st_bullish[1]
if st_changed
    signal_fired_in_current_trend := false // UNLOCK on SuperTrend flip

bars_since_signal += 1

// =================== 2. POTENTIAL SIGNAL CALCULATION (This runs second) ===================
// Declare variables needed for scoring within this section
float v1_buy_score = 0.0
int v1_actual_buy_confluence_count = 0
bool condition_st_stable_bullish_aligned = false
bool condition_st_bullish_aligned = false

float v1_sell_score = 0.0
int v1_actual_sell_confluence_count = 0
bool condition_st_stable_bearish_aligned = false
bool condition_st_bearish_aligned = false

bool shared_condition_adx_strong = not na(adx_1h) and adx_1h >= trend_strength_required
bool shared_condition_volume_surge = volume_surge

float v1_score_diff = 0.0
int current_signal_strength_score = 0

if should_calculate
    // --- Scoring Logic ---
    v1_buy_score := 0.0
    v1_actual_buy_confluence_count := 0
    v1_sell_score := 0.0
    v1_actual_sell_confluence_count := 0

    // BUY LOGIC (inline, as in original)
    if major_trend_direction == 1
        v1_buy_score += 1.5
        v1_actual_buy_confluence_count += 1
    if not na(rsi_entry) and not na(rsi_15m) and not na(rsi_1h) and rsi_entry < v1_rsi_buy_threshold and rsi_15m < 85 and rsi_1h < 80
        v1_buy_score += 2.0
        v1_actual_buy_confluence_count += 1
    if not na(macd_line_1h) and not na(signal_line_1h) and macd_line_1h > signal_line_1h
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1
    condition_st_stable_bullish_aligned := st_stable_bullish and st_alignment_bull >= 2
    condition_st_bullish_aligned := st_bullish and st_alignment_bull >= 2
    if condition_st_stable_bullish_aligned
        v1_buy_score += 2.0
        v1_actual_buy_confluence_count += 1
    else if condition_st_bullish_aligned
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1
    if shared_condition_adx_strong
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1
    if shared_condition_volume_surge
        v1_buy_score += 0.5
        v1_actual_buy_confluence_count += 1
    if close > open and close > high[1]
        v1_buy_score += 0.5
        v1_actual_buy_confluence_count += 1
    if v1_market_structure == "BULLISH"
        v1_buy_score += 1.0
        v1_actual_buy_confluence_count += 1

    // SELL LOGIC (inline, as in original)
    if major_trend_direction == -1
        v1_sell_score += 1.5
        v1_actual_sell_confluence_count += 1
    if not na(rsi_entry) and not na(rsi_15m) and not na(rsi_1h) and rsi_entry > v1_rsi_sell_threshold and rsi_15m > 15 and rsi_1h > 20
        v1_sell_score += 2.0
        v1_actual_sell_confluence_count += 1
    if not na(macd_line_1h) and not na(signal_line_1h) and macd_line_1h < signal_line_1h
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1
    condition_st_stable_bearish_aligned := st_stable_bearish and st_alignment_bear >= 2
    condition_st_bearish_aligned := st_bearish and st_alignment_bear >= 2
    if condition_st_stable_bearish_aligned
        v1_sell_score += 2.0
        v1_actual_sell_confluence_count += 1
    else if condition_st_bearish_aligned
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1
    if shared_condition_adx_strong
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1
    if shared_condition_volume_surge
        v1_sell_score += 0.5
        v1_actual_sell_confluence_count += 1
    if close < open and close < low[1]
        v1_sell_score += 0.5
        v1_actual_sell_confluence_count += 1
    if v1_market_structure == "BEARISH"
        v1_sell_score += 1.0
        v1_actual_sell_confluence_count += 1

    // --- Determine Potential Signal Direction ---
    v1_score_diff := math.abs(v1_buy_score - v1_sell_score)

    // IMPORTANT: Use the original logic for determining SuperTrend conditions
    // This is the key part that was causing signal differences
    bool st_buy_condition_met = use_aggressive_entry ? st_bullish : st_stable_bullish
    bool st_sell_condition_met = use_aggressive_entry ? st_bearish : st_stable_bearish

    if v1_buy_score > v1_sell_score
        current_max_confluence := v1_actual_buy_confluence_count
        current_signal_strength_text := switch
            current_max_confluence >= 6 and v1_score_diff >= 4.0 => "ULTRA"
            current_max_confluence >= 5 and v1_score_diff >= 3.0 => "STRONG"
            current_max_confluence >= 4 and v1_score_diff >= V1_SCORE_DIFF_THRESHOLD => "MODERATE"
            => "WEAK"

        // Use the same condition as the original
        if current_max_confluence >= min_confluence and v1_score_diff >= V1_SCORE_DIFF_THRESHOLD and current_signal_strength_text != "WEAK" and st_buy_condition_met
            signal_direction := "BUY"

    else if v1_sell_score > v1_buy_score
        current_max_confluence := v1_actual_sell_confluence_count
        current_signal_strength_text := switch
            current_max_confluence >= 6 and v1_score_diff >= 4.0 => "ULTRA"
            current_max_confluence >= 5 and v1_score_diff >= 3.0 => "STRONG"
            current_max_confluence >= 4 and v1_score_diff >= V1_SCORE_DIFF_THRESHOLD => "MODERATE"
            => "WEAK"

        // MODIFICATION: Use the dynamic SuperTrend condition
        if current_max_confluence >= min_confluence and v1_score_diff >= V1_SCORE_DIFF_THRESHOLD and current_signal_strength_text != "WEAK" and st_sell_condition_met
            signal_direction := "SELL"

    current_signal_strength_score := current_signal_strength_text == "ULTRA" ? 4 : current_signal_strength_text == "STRONG" ? 3 : current_signal_strength_text == "MODERATE" ? 2 : 1

if signal_direction == "NONE" and should_calculate
    current_max_confluence := math.max(v1_actual_buy_confluence_count, v1_actual_sell_confluence_count)

// --- Quality Filters Calculation ---
// is_volatility_sufficient and is_not_exhausted are calculated before the signal logic block

// --- Adaptive Momentum Gate ---
histogram_gate = gate_macd - gate_signal
bull_score = (gate_macd > gate_signal ? 1 : 0) + (gate_macd > 0 ? 1 : 0) + (histogram_gate > histogram_gate[1] ? 1 : 0)
bear_score = (gate_macd < gate_signal ? 1 : 0) + (gate_macd < 0 ? 1 : 0) + (histogram_gate < histogram_gate[1] ? 1 : 0)
required_momentum_score = switch v1_volatility_regime // Use V1 volatility regime
    "LOW" => 3 // Corrected syntax
    "MEDIUM" => 3 // Corrected syntax
    "HIGH"          => 2
    "EXTREME"       => 1
momentum_gate_passed = not use_momentum_gate or (signal_direction == "BUY" and bull_score >= required_momentum_score) or (signal_direction == "SELL" and bear_score >= required_momentum_score)

// --- Entry Confirmation Calculation ---
var bool entry_confirmed = true // Declare here as it's used in validation

if use_entry_confirmation and should_calculate and signal_direction != "NONE"
    entry_confirmed := true // Initialize to true before checking conditions
    if confirm_type == "Structure Break"
        entry_confirmed := signal_direction == "BUY" ? close > high[1] : close < low[1]
    else if confirm_type == "RSI Cross"
        entry_confirmed := signal_direction == "BUY" ? rsi_cross_up : rsi_cross_down
    else if confirm_type == "Engulfing"
        entry_confirmed := signal_direction == "BUY" ? close > open and close > high[1] : close < open and close < low[1]

// --- HTF Filter Check Calculation ---
daily_filter_active = filter_type == "DailyEMA"
weekly_filter_active = filter_type == "WeeklyEMA"
donchian_filter_active = filter_type == "Donchian"

buy_htf_conditions_met = (not daily_filter_active or daily_bullish) and
                         (not weekly_filter_active or weekly_bullish) and
                         (not donchian_filter_active or donch_break_long)

sell_htf_conditions_met = (not daily_filter_active or not daily_bullish) and
                          (not weekly_filter_active or not weekly_bullish) and
                          (not donchian_filter_active or donch_break_short)

htf_filter_check = if filter_type == "None"
    true
else if signal_direction == "BUY"
    buy_htf_conditions_met
else if signal_direction == "SELL"
    sell_htf_conditions_met
else
    true

// --- SuperTrend Check Calculation ---
st_check_passed = (signal_direction == "BUY" and (condition_st_stable_bullish_aligned or condition_st_bullish_aligned)) or
                  (signal_direction == "SELL" and (condition_st_stable_bearish_aligned or condition_st_bearish_aligned))

// --- Ranging Market Filter Check Calculation ---
ranging_market_filter_passed = v1_market_structure != "RANGING"

// =================== 3. FINAL SIGNAL VALIDATION (This runs third) ===================
big_move_signal = should_calculate and
                  signal_direction != "NONE" and
                  bars_since_signal >= signal_cooldown and
                  not signal_fired_in_current_trend and // THE LOCK
                  momentum_gate_passed and
                  is_volatility_sufficient and
                  is_not_exhausted and
                  entry_confirmed and
                  htf_filter_check and
                  st_check_passed and
                  ranging_market_filter_passed and
                  not trade_block

final_buy_signal = big_move_signal and momentum_bullish and (current_max_confluence >= min_confluence_dynamic)
final_sell_signal = big_move_signal and momentum_bearish and (current_max_confluence >= min_confluence_dynamic)

// Update the final_signal state for the NEXT bar to read
final_signal := final_buy_signal or final_sell_signal

// Update the risk for the NEXT bar to read
atr_current = ta.atr(atr_tsl_period)
risk := final_signal ? (atr_current * initial_risk_atr_multiplier) : 0.0

// =================== 4. TRADE MANAGEMENT & VISUALS (This runs last) ===================
// Initialize TSL when a confirmed signal starts (using state from previous bar)
if plot_confirmed_signal_now
    // atr_tsl_active_direction := confirmed_signal_direction // Moved to State Update section
    atr_tsl_entry_price := close[1]
    atr_tsl_initial_risk := confirmed_risk_for_tsl
    atr_trailing_stop_level := na
    atr_tsl_highest_rr_achieved := 0.0
    atr_tsl_is_activated_for_trade := false

    // st_tsl_active_direction := confirmed_signal_direction // Moved to State Update section
    st_tsl_entry_price := close[1]

// Calculate variables needed for plotting based on confirmed signal state
is_confirmed_buy_signal_event = plot_confirmed_signal_now and confirmed_signal_direction == "BUY"
plot_main_buy_arrow = is_confirmed_buy_signal_event and not is_confirmed_buy_signal_event[1]

is_confirmed_sell_signal_event = plot_confirmed_signal_now and confirmed_signal_direction == "SELL"
plot_main_sell_arrow = is_confirmed_sell_signal_event and not is_confirmed_sell_signal_event[1]

// Update signal strength text/score for display based on confirmed signal
string signal_strength_text_display = plot_confirmed_signal_now ? confirmed_signal_strength_text : ""
int signal_strength_score_display = plot_confirmed_signal_now ? confirmed_max_confluence : 0


// --- TRAILING STOP LOGIC & UPDATES ---
atr_tsl_hit_on_current_bar := false
if use_trailing_stop and atr_tsl_active_direction != "NONE" and not na(atr_tsl_entry_price) and not na(atr_tsl_initial_risk) and atr_tsl_initial_risk > 0
    current_profit_for_atr_tsl = atr_tsl_active_direction == "BUY" ? (close - atr_tsl_entry_price) : (atr_tsl_entry_price - close)
    current_rr_for_atr_tsl = atr_tsl_initial_risk > 0 ? current_profit_for_atr_tsl / atr_tsl_initial_risk : 0.0

    if current_rr_for_atr_tsl > atr_tsl_highest_rr_achieved
        atr_tsl_highest_rr_achieved := current_rr_for_atr_tsl

    var float effective_tsl_multiplier = atr_tsl_multiplier

    is_strong_trend_for_tsl = (atr_tsl_active_direction == "BUY" and major_trend_direction == 1 and shared_condition_adx_strong) or
                               (atr_tsl_active_direction == "SELL" and major_trend_direction == -1 and shared_condition_adx_strong)

    if is_strong_trend_for_tsl
        effective_tsl_multiplier := atr_tsl_strong_trend_multiplier

    if atr_tsl_highest_rr_achieved >= trailing_activation
        if not atr_tsl_is_activated_for_trade
            atr_tsl_is_activated_for_trade := true
            atr_trail_val_initial = atr_current * effective_tsl_multiplier
            if atr_tsl_active_direction == "BUY"
                atr_trailing_stop_level := close - atr_trail_val_initial
            else
                atr_trailing_stop_level := close + atr_trail_val_initial

        atr_trail_val_trailing = atr_current * effective_tsl_multiplier
        new_potential_tsl_level = atr_tsl_active_direction == "BUY" ? close - atr_trail_val_trailing : close + atr_trail_val_trailing

        if atr_tsl_active_direction == "BUY"
            atr_trailing_stop_level := na(atr_trailing_stop_level) ? new_potential_tsl_level : math.max(atr_trailing_stop_level, new_potential_tsl_level)
        else
            atr_trailing_stop_level := na(atr_trailing_stop_level) ? new_potential_tsl_level : math.min(atr_trailing_stop_level, new_potential_tsl_level)

    if atr_tsl_is_activated_for_trade and not na(atr_trailing_stop_level)
        can_exit = atr_tsl_active_direction == "BUY" ? (tsl_exit_on_close ? close <= atr_trailing_stop_level : low  <= atr_trailing_stop_level) : (tsl_exit_on_close ? close >= atr_trailing_stop_level : high >= atr_trailing_stop_level)
        if can_exit
            atr_tsl_hit_on_current_bar := true
            atr_tsl_active_direction := "NONE"
            atr_tsl_entry_price := na
            atr_tsl_initial_risk := na
            atr_tsl_highest_rr_achieved := 0.0
            atr_tsl_is_activated_for_trade := false
            st_tsl_active_direction := "" // Reset ST TSL state as well on ATR exit
            st_tsl_entry_price := na

if atr_tsl_hit_on_current_bar
    // This alert is fine as atr_tsl_hit_on_current_bar is only true for one bar
    atr_hit_alert_message = "🔵 ATR Trailing Stop HIT for " + atr_tsl_active_direction[1] + " trade! Exited near: " + str.tostring(atr_trailing_stop_level, format.mintick)
    alert(atr_hit_alert_message, alert.freq_once_per_bar)

// Calculate supertrend_exit based on current conditions
supertrend_exit := false
if st_tsl_active_direction == "BUY"
    // Use shared_condition_adx_strong (calculated earlier)
    if major_trend_direction == 1 and shared_condition_adx_strong
        if st_stable_bearish
            supertrend_exit := true
    else
        if st_bearish
            supertrend_exit := true
else if st_tsl_active_direction == "SELL"
    // Use shared_condition_adx_strong (calculated earlier)
    if major_trend_direction == -1 and shared_condition_adx_strong
        if st_stable_bullish
            supertrend_exit := true
    else
        if st_bullish
            supertrend_exit := true

// Detect the *first* bar of the SuperTrend exit event
st_tsl_exit_event = supertrend_exit and not supertrend_exit[1]

// Create the line and label ONLY on the exit event bar
if st_tsl_exit_event
    // Delete previous objects before creating new ones
    if not na(st_tsl_display_line)
        line.delete(st_tsl_display_line)
    if not na(st_tsl_display_label)
        label.delete(st_tsl_display_label)

    if show_tsl_details
        st_tsl_display_line := line.new(bar_index, st_entry, bar_index + 10, st_entry, color=color.new(color.purple, 0), width=3, style=line.style_dashed) // removed xloc
        st_tsl_display_label := label.new(bar_index + 5, st_entry, "ST TSL Exit", color=color.new(color.purple, 0), textcolor=color.white, style=label.style_label_left, size=size.normal) // removed xloc
    else
        st_tsl_display_line := na
        st_tsl_display_label := na

    // Reset TSL state variables when the exit event occurs
    st_tsl_active_direction := ""
    st_tsl_entry_price := na
    atr_tsl_active_direction := "NONE"
    atr_trailing_stop_level := na
    atr_tsl_entry_price := na
    atr_tsl_initial_risk := na
    atr_tsl_highest_rr_achieved := 0.0
    atr_tsl_is_activated_for_trade := false


// =================== VISUAL ELEMENTS ===================
plot(show_structure ? st_entry : na, "SuperTrend", color=st_bullish ? color.new(color.green, 0) : color.new(color.red, 0), linewidth=2, style=plot.style_line)

// st_changed logic moved to State Update section

st_bg_color = st_bullish ? (st_changed ? color.new(color.blue, 90) : color.new(color.green, 95)) : (st_changed ? color.new(color.blue, 90) : color.new(color.red, 95))

bgcolor(show_structure ? st_bg_color : na, title="SuperTrend Background")
bgcolor(show_chop_filter and trade_block ? color.new(color.gray, 85) : na, title="Chop Block")
bgcolor(not ranging_market_filter_passed ? color.new(color.yellow, 92) : na, title="Ranging Market Block")

plot(show_structure ? ema_200_1h : na, "EMA 200", color=color.new(color.orange, 20), linewidth=2)

plotshape(plot_main_buy_arrow and show_signals, title="BUY Signal", style=shape.triangleup, location=location.belowbar, color=color.new(color.lime,0), size=size.large)
plotshape(plot_main_sell_arrow and show_signals, title="SELL Signal", style=shape.triangledown, location=location.abovebar, color=color.new(color.red,0), size=size.large)

plot(show_tsl_details ? atr_trailing_stop_level : na, "ATR Trailing SL", color=color.new(color.blue, 50), style=plot.style_cross, linewidth=2, display=display.all, trackprice=true)

// Remove xloc argument from plotshape calls below:
plotshape(show_tsl_details and atr_tsl_hit_on_current_bar and atr_tsl_active_direction[1] == "BUY", title="ATR TSL Exit BUY", style=shape.diamond, location=location.belowbar, color=color.new(color.blue, 0), size=size.normal, text="ATR EXIT")
plotshape(show_tsl_details and atr_tsl_hit_on_current_bar and atr_tsl_active_direction[1] == "SELL", title="ATR TSL Exit SELL", style=shape.diamond, location=location.abovebar, color=color.new(color.blue, 0), size=size.normal, text="ATR EXIT")

// Modify ST exit shapes to trigger only on the exit event bar
plotshape(show_tsl_details and st_tsl_exit_event and st_bearish[1], title="ST Exit BUY", style=shape.labeldown, location=location.abovebar, color=color.new(color.orange, 0), size=size.normal, text="ST EXIT", textcolor=color.white) // Use st_tsl_exit_event and [1] for direction
plotshape(show_tsl_details and st_tsl_exit_event and st_bullish[1], title="ST Exit SELL", style=shape.labelup, location=location.belowbar, color=color.new(color.orange, 0), size=size.normal, text="ST EXIT", textcolor=color.white) // Use st_tsl_exit_event and [1] for direction

// Plot signal strength text using confirmed signal state
// plotchar(show_signal_strength and plot_main_buy_arrow, title="Buy Signal Strength", char="", location=location.belowbar, color=color.new(color.lime, 0), text=confirmed_signal_strength_text, textcolor=color.white, size=size.normal) // Removed plotchar
// plotchar(show_signal_strength and plot_main_sell_arrow, title="Sell Signal Strength", char="", location=location.abovebar, color=color.new(color.red, 0), text=confirmed_signal_strength_text, textcolor=color.white, size=size.normal) // Removed plotchar

// Use label.new for signal strength text as it supports series string
if show_signal_strength and plot_main_buy_arrow
    label.new(x=bar_index, y=low, text=confirmed_signal_strength_text, color=color.new(color.lime, 0), textcolor=color.white, style=label.style_label_up, size=size.normal, yloc=yloc.belowbar)

if show_signal_strength and plot_main_sell_arrow
    label.new(x=bar_index, y=high, text=confirmed_signal_strength_text, color=color.new(color.red, 0), textcolor=color.white, style=label.style_label_down, size=size.normal, yloc=yloc.abovebar)


// =================== DEBUG TABLE ===================
if show_table and barstate.islast
    var table debug_table = table.new(position.top_right, 2, 25, bgcolor=color.white, border_width=1) // Increased rows for new filters

    if barstate.islast
        table.cell(debug_table, 0, 0, "BTC SNIPER v2.3 - OPTIMIZED", text_color=color.white, bgcolor=color.navy)
        table.cell(debug_table, 1, 0, "Status", text_color=color.white, bgcolor=color.navy)

        table.cell(debug_table, 0, 1, "ST Entry", text_color=color.black)
        table.cell(debug_table, 1, 1, st_bullish ? "BULL" : "BEAR", text_color=st_bullish ? color.green : color.red)

        table.cell(debug_table, 0, 2, "ST Stable", text_color=color.black)
        table.cell(debug_table, 1, 2, st_stable_bullish ? "BULL" : (st_stable_bearish ? "BEAR" : "MIXED"), text_color=st_stable_bullish ? color.green : (st_stable_bearish ? color.red : color.orange))

        table.cell(debug_table, 0, 3, "ST Alignment", text_color=color.black)
        table.cell(debug_table, 1, 3, str.tostring(math.max(st_alignment_bull, st_alignment_bear)) + "/3", text_color=math.max(st_alignment_bull, st_alignment_bear) >= 2 ? color.green : color.orange)

        table.cell(debug_table, 0, 4, "Major Trend", text_color=color.black)
        table.cell(debug_table, 1, 4, major_trend_direction == 1 ? "BULL" : "BEAR", text_color=major_trend_direction == 1 ? color.green : color.red)

        table.cell(debug_table, 0, 5, "RSI Entry", text_color=color.black)
        table.cell(debug_table, 1, 5, str.tostring(math.round(rsi_entry)), text_color=rsi_entry > 50 ? color.green : color.red)

        table.cell(debug_table, 0, 6, "MACD 1H", text_color=color.black)
        table.cell(debug_table, 1, 6, macd_line_1h > signal_line_1h ? "BULL" : "BEAR", text_color=macd_line_1h > signal_line_1h ? color.green : color.red)

        table.cell(debug_table, 0, 7, "ADX 1H", text_color=color.black)
        table.cell(debug_table, 1, 7, str.tostring(math.round(adx_1h)), text_color=adx_1h >= trend_strength_required ? color.green : color.gray)

        table.cell(debug_table, 0, 8, "Volume", text_color=color.black)
        table.cell(debug_table, 1, 8, volume_surge ? "SURGE" : "NORMAL", text_color=volume_surge ? color.green : color.gray)

        table.cell(debug_table, 0, 9, "Buy Conf", text_color=color.black)
        table.cell(debug_table, 1, 9, str.tostring(v1_actual_buy_confluence_count) + "/" + str.tostring(min_confluence), text_color=v1_actual_buy_confluence_count >= min_confluence ? color.green : color.gray)

        table.cell(debug_table, 0, 10, "Sell Conf", text_color=color.black)
        table.cell(debug_table, 1, 10, str.tostring(v1_actual_sell_confluence_count) + "/" + str.tostring(min_confluence), text_color=v1_actual_sell_confluence_count >= min_confluence ? color.red : color.gray)

        table.cell(debug_table, 0, 11, "Signal Dir", text_color=color.black)
        table.cell(debug_table, 1, 11, signal_direction, text_color=signal_direction != "NONE" ? color.blue : color.gray)

        table.cell(debug_table, 0, 12, "Entry Confirm", text_color=color.black)
        table.cell(debug_table, 1, 12, entry_confirmed ? "YES" : "NO", text_color=entry_confirmed ? color.green : color.red)

        table.cell(debug_table, 0, 13, "Final Signal", text_color=color.black)
        table.cell(debug_table, 1, 13, final_signal ? signal_direction : "WAITING", text_color=final_signal ? color.green : color.gray)

        table.cell(debug_table, 0, 14, "Chop Score", text_color=color.black)
        table.cell(debug_table, 1, 14, str.tostring(chop_score) + "/" + str.tostring(chop_score_threshold) + (trade_block ? " (BLOCK)" : ""), text_color=trade_block ? color.red : color.green)

        table.cell(debug_table, 0, 15, "V1 Pivot Str.", text_color=color.black, bgcolor=v1_market_structure == "RANGING" and not ranging_market_filter_passed ? color.new(color.yellow, 70) : color.white)
        table.cell(debug_table, 1, 15, v1_market_structure + (not ranging_market_filter_passed ? " (BLOCK)" : ""), text_color=v1_market_structure == "BULLISH" ? color.green : (v1_market_structure == "BEARISH" ? color.red : color.orange))

        table.cell(debug_table, 0, 16, "V1 Vol Regime", text_color=color.black)
        table.cell(debug_table, 1, 16, v1_volatility_regime, text_color=color.black)

        table.cell(debug_table, 0, 17, "V1 RSI Buy Thresh", text_color=color.black)
        table.cell(debug_table, 1, 17, str.tostring(v1_rsi_buy_threshold, "#.##"), text_color=color.green)

        table.cell(debug_table, 0, 18, "V1 RSI Sell Thresh", text_color=color.black)
        table.cell(debug_table, 1, 18, str.tostring(v1_rsi_sell_threshold, "#.##"), text_color=color.red)

        table.cell(debug_table, 0, 19, "V1 Buy Score", text_color=color.black)
        table.cell(debug_table, 1, 19, str.tostring(v1_buy_score, "#.#"), text_color=color.green)

        table.cell(debug_table, 0, 20, "V1 Sell Score", text_color=color.black)
        table.cell(debug_table, 1, 20, str.tostring(v1_sell_score, "#.#"), text_color=color.red)

        table.cell(debug_table, 0, 21, "V1 Score Diff", text_color=color.black)
        table.cell(debug_table, 1, 21, str.tostring(v1_score_diff, "#.#"), text_color=color.blue)

        table.cell(debug_table, 0, 22, "Volatility Filter", text_color=color.black)
        table.cell(debug_table, 1, 22, is_volatility_sufficient ? "PASS" : "BLOCK", text_color=is_volatility_sufficient ? color.green : color.red)

        table.cell(debug_table, 0, 23, "Exhaustion Filter", text_color=color.black)
        table.cell(debug_table, 1, 23, is_not_exhausted ? "PASS" : "BLOCK", text_color=is_not_exhausted ? color.green : color.red)

        table.cell(debug_table, 0, 24, "Momentum Gate", text_color=color.black)
        table.cell(debug_table, 1, 24, momentum_gate_passed ? "PASS" : "BLOCK", text_color=momentum_gate_passed ? color.green : color.red)


// =================== ALERTS ===================
if plot_confirmed_signal_now and confirmed_signal_direction == "BUY"
    alert("🚀 BTC BUY Signal!", alert.freq_once_per_bar_close)

if plot_confirmed_signal_now and confirmed_signal_direction == "SELL"
    alert("🔻 BTC SELL Signal!", alert.freq_once_per_bar_close)

if use_trailing_stop and atr_tsl_is_activated_for_trade and not atr_tsl_is_activated_for_trade[1] and atr_tsl_active_direction != "NONE"
    trail_alert_message = "🔄 ATR Trailing Stop ACTIVATED for " + atr_tsl_active_direction + " trade. Initial R:R target " + str.tostring(trailing_activation, "#.#") + ":1 met. Current TSL: " + str.tostring(atr_trailing_stop_level, format.mintick)
    alert(trail_alert_message, alert.freq_once_per_bar)

// Modify ST exit alert to trigger only on the event bar
if st_tsl_exit_event // Use the event variable
    string exited_trade_direction = st_bearish[1] ? "BUY" : "SELL" // Use [1] because the exit condition was true on the previous bar
    st_exit_alert_message = "⚠️ SuperTrend TSL EXIT for " + exited_trade_direction + " trade! Closed at SuperTrend value: " + str.tostring(st_entry, format.mintick) // st_entry is current bar value
    alert(st_exit_alert_message, alert.freq_once_per_bar)
    