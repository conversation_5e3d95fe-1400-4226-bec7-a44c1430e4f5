{"workflow_id": "trading_analysis_1754237971", "start_time": "2025-08-03T21:49:31.431978", "context": {"analysis_mode": "positional", "market_specialization": "indian market", "chart_count": 1}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T21:49:31.431978", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T21:49:43.705277", "execution_time": 12.273298501968384, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'basic_patterns', 'success']"}, {"step_number": 3, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-08-03T21:49:43.706282", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: crypto, Symbol: BTCUSD"}, {"step_number": 4, "step_name": "Step 2: Tool Selection", "step_type": "api_calls", "timestamp": "2025-08-03T21:49:45.854403", "execution_time": 2.1481211185455322, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 5, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-08-03T21:49:45.854403", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 6, "step_name": "Step 3: Flash Summarization", "step_type": "llm_call", "timestamp": "2025-08-03T21:49:56.454943", "execution_time": 10.600539684295654, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count']"}, {"step_number": 7, "step_name": "Step 4: Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T21:49:57.275389", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG tool available"}, {"step_number": 8, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T21:50:40.993357", "execution_time": 43.71744155883789, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis', 'analysis_notes', 'trading_signals', 'detected_symbol']..."}], "llm_calls": [], "summary": {"total_steps": 8, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 69.56137919425964}, "end_time": "2025-08-03T21:50:40.993357", "status": "success", "error": null}