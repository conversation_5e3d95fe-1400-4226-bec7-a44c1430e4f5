#!/usr/bin/env python3
"""
Trade Result Storage System
Handles persistent storage of analysis results and trade outcomes
"""

import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import uuid

logger = logging.getLogger(__name__)

@dataclass
class AnalysisResult:
    """Analysis result data structure."""
    id: str
    timestamp: datetime
    symbol: str
    market_type: str
    analysis_mode: str
    chart_path: str
    analysis_notes: str
    trading_signals: Dict[str, Any]
    tool_results: Dict[str, Any]
    confidence_score: int
    entry_levels: List[float]
    stop_loss: float
    take_profit: List[float]
    risk_reward_ratio: float
    status: str = "analyzed"  # analyzed, trade_taken, completed

@dataclass
class TradeExecution:
    """Trade execution data structure."""
    id: str
    analysis_id: str
    timestamp: datetime
    symbol: str
    entry_price: float
    quantity: float
    trade_type: str  # "long" or "short"
    status: str = "open"  # open, closed

@dataclass
class TradeOutcome:
    """Trade outcome data structure."""
    id: str
    analysis_id: str
    execution_id: str
    timestamp: datetime
    symbol: str
    entry_price: float
    exit_price: float
    quantity: float
    pnl_amount: float
    pnl_percentage: float
    outcome: str  # "win", "loss", "breakeven"
    hold_duration_hours: float
    exit_reason: str
    lessons_learned: str

class TradeResultStorage:
    """Persistent storage system for trading analysis and results."""
    
    def __init__(self, db_path: str = "trading_results.db"):
        self.db_path = db_path
        self.logger = logger
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize SQLite database with required tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Analysis results table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS analysis_results (
                        id TEXT PRIMARY KEY,
                        timestamp TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        market_type TEXT NOT NULL,
                        analysis_mode TEXT NOT NULL,
                        chart_path TEXT,
                        analysis_notes TEXT,
                        trading_signals TEXT,
                        tool_results TEXT,
                        confidence_score INTEGER,
                        entry_levels TEXT,
                        stop_loss REAL,
                        take_profit TEXT,
                        risk_reward_ratio REAL,
                        status TEXT DEFAULT 'analyzed'
                    )
                """)
                
                # Trade executions table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS trade_executions (
                        id TEXT PRIMARY KEY,
                        analysis_id TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        entry_price REAL NOT NULL,
                        quantity REAL NOT NULL,
                        trade_type TEXT NOT NULL,
                        status TEXT DEFAULT 'open',
                        FOREIGN KEY (analysis_id) REFERENCES analysis_results (id)
                    )
                """)
                
                # Trade outcomes table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS trade_outcomes (
                        id TEXT PRIMARY KEY,
                        analysis_id TEXT NOT NULL,
                        execution_id TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        entry_price REAL NOT NULL,
                        exit_price REAL NOT NULL,
                        quantity REAL NOT NULL,
                        pnl_amount REAL NOT NULL,
                        pnl_percentage REAL NOT NULL,
                        outcome TEXT NOT NULL,
                        hold_duration_hours REAL,
                        exit_reason TEXT,
                        lessons_learned TEXT,
                        FOREIGN KEY (analysis_id) REFERENCES analysis_results (id),
                        FOREIGN KEY (execution_id) REFERENCES trade_executions (id)
                    )
                """)
                
                conn.commit()
                logger.info("Trade result storage database initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def store_analysis_result(self, analysis_data: Dict[str, Any]) -> str:
        """Store analysis result and return analysis ID."""
        try:
            analysis_id = str(uuid.uuid4())
            
            # Create AnalysisResult object
            analysis = AnalysisResult(
                id=analysis_id,
                timestamp=datetime.now(),
                symbol=analysis_data.get("symbol", ""),
                market_type=analysis_data.get("market_type", ""),
                analysis_mode=analysis_data.get("analysis_mode", ""),
                chart_path=analysis_data.get("chart_path", ""),
                analysis_notes=analysis_data.get("analysis_notes", ""),
                trading_signals=analysis_data.get("trading_signals", {}),
                tool_results=analysis_data.get("tool_results", {}),
                confidence_score=analysis_data.get("confidence_score", 0),
                entry_levels=analysis_data.get("entry_levels", []),
                stop_loss=analysis_data.get("stop_loss", 0.0),
                take_profit=analysis_data.get("take_profit", []),
                risk_reward_ratio=analysis_data.get("risk_reward_ratio", 0.0)
            )
            
            # Store in database
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO analysis_results 
                    (id, timestamp, symbol, market_type, analysis_mode, chart_path, 
                     analysis_notes, trading_signals, tool_results, confidence_score,
                     entry_levels, stop_loss, take_profit, risk_reward_ratio, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    analysis.id,
                    analysis.timestamp.isoformat(),
                    analysis.symbol,
                    analysis.market_type,
                    analysis.analysis_mode,
                    analysis.chart_path,
                    analysis.analysis_notes,
                    json.dumps(analysis.trading_signals),
                    json.dumps(analysis.tool_results),
                    analysis.confidence_score,
                    json.dumps(analysis.entry_levels),
                    analysis.stop_loss,
                    json.dumps(analysis.take_profit),
                    analysis.risk_reward_ratio,
                    analysis.status
                ))
                conn.commit()
            
            logger.info(f"Stored analysis result: {analysis_id}")
            return analysis_id
            
        except Exception as e:
            logger.error(f"Failed to store analysis result: {e}")
            return ""
    
    def get_recent_analyses(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent analysis results for dashboard display."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM analysis_results 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                """, (limit,))
                
                rows = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]
                
                analyses = []
                for row in rows:
                    analysis = dict(zip(columns, row))
                    # Parse JSON fields
                    analysis['trading_signals'] = json.loads(analysis['trading_signals'] or '{}')
                    analysis['tool_results'] = json.loads(analysis['tool_results'] or '{}')
                    analysis['entry_levels'] = json.loads(analysis['entry_levels'] or '[]')
                    analysis['take_profit'] = json.loads(analysis['take_profit'] or '[]')
                    analyses.append(analysis)
                
                return analyses
                
        except Exception as e:
            logger.error(f"Failed to get recent analyses: {e}")
            return []
    
    def record_trade_execution(self, analysis_id: str, execution_data: Dict[str, Any]) -> str:
        """Record when user actually takes a trade based on analysis."""
        try:
            execution_id = str(uuid.uuid4())
            
            execution = TradeExecution(
                id=execution_id,
                analysis_id=analysis_id,
                timestamp=datetime.now(),
                symbol=execution_data.get("symbol", ""),
                entry_price=execution_data.get("entry_price", 0.0),
                quantity=execution_data.get("quantity", 0.0),
                trade_type=execution_data.get("trade_type", "long")
            )
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO trade_executions 
                    (id, analysis_id, timestamp, symbol, entry_price, quantity, trade_type, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    execution.id,
                    execution.analysis_id,
                    execution.timestamp.isoformat(),
                    execution.symbol,
                    execution.entry_price,
                    execution.quantity,
                    execution.trade_type,
                    execution.status
                ))
                
                # Update analysis status
                cursor.execute("""
                    UPDATE analysis_results 
                    SET status = 'trade_taken' 
                    WHERE id = ?
                """, (analysis_id,))
                
                conn.commit()
            
            logger.info(f"Recorded trade execution: {execution_id}")
            return execution_id
            
        except Exception as e:
            logger.error(f"Failed to record trade execution: {e}")
            return ""
    
    def record_trade_outcome(self, execution_id: str, outcome_data: Dict[str, Any]) -> str:
        """Record the final outcome of a trade."""
        try:
            outcome_id = str(uuid.uuid4())
            
            # Get execution details
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT analysis_id, symbol, entry_price, quantity, timestamp
                    FROM trade_executions 
                    WHERE id = ?
                """, (execution_id,))
                
                execution_row = cursor.fetchone()
                if not execution_row:
                    raise ValueError(f"Trade execution {execution_id} not found")
                
                analysis_id, symbol, entry_price, quantity, entry_timestamp = execution_row
                entry_time = datetime.fromisoformat(entry_timestamp)
                exit_time = datetime.now()
                hold_duration = (exit_time - entry_time).total_seconds() / 3600  # hours
                
                # Calculate P&L
                exit_price = outcome_data.get("exit_price", 0.0)
                pnl_amount = (exit_price - entry_price) * quantity
                pnl_percentage = ((exit_price - entry_price) / entry_price) * 100
                
                # Determine outcome
                if pnl_percentage > 0.5:
                    outcome = "win"
                elif pnl_percentage < -0.5:
                    outcome = "loss"
                else:
                    outcome = "breakeven"
                
                trade_outcome = TradeOutcome(
                    id=outcome_id,
                    analysis_id=analysis_id,
                    execution_id=execution_id,
                    timestamp=exit_time,
                    symbol=symbol,
                    entry_price=entry_price,
                    exit_price=exit_price,
                    quantity=quantity,
                    pnl_amount=pnl_amount,
                    pnl_percentage=pnl_percentage,
                    outcome=outcome,
                    hold_duration_hours=hold_duration,
                    exit_reason=outcome_data.get("exit_reason", ""),
                    lessons_learned=outcome_data.get("lessons_learned", "")
                )
                
                # Store outcome
                cursor.execute("""
                    INSERT INTO trade_outcomes 
                    (id, analysis_id, execution_id, timestamp, symbol, entry_price, 
                     exit_price, quantity, pnl_amount, pnl_percentage, outcome,
                     hold_duration_hours, exit_reason, lessons_learned)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    trade_outcome.id,
                    trade_outcome.analysis_id,
                    trade_outcome.execution_id,
                    trade_outcome.timestamp.isoformat(),
                    trade_outcome.symbol,
                    trade_outcome.entry_price,
                    trade_outcome.exit_price,
                    trade_outcome.quantity,
                    trade_outcome.pnl_amount,
                    trade_outcome.pnl_percentage,
                    trade_outcome.outcome,
                    trade_outcome.hold_duration_hours,
                    trade_outcome.exit_reason,
                    trade_outcome.lessons_learned
                ))
                
                # Update execution and analysis status
                cursor.execute("UPDATE trade_executions SET status = 'closed' WHERE id = ?", (execution_id,))
                cursor.execute("UPDATE analysis_results SET status = 'completed' WHERE id = ?", (analysis_id,))
                
                conn.commit()
            
            logger.info(f"Recorded trade outcome: {outcome_id} ({outcome})")
            return outcome_id
            
        except Exception as e:
            logger.error(f"Failed to record trade outcome: {e}")
            return ""

    def get_analysis_by_id(self, analysis_id: str) -> Dict[str, Any]:
        """Get specific analysis by ID."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM analysis_results WHERE id = ?
                """, (analysis_id,))

                row = cursor.fetchone()
                if row:
                    columns = [desc[0] for desc in cursor.description]
                    result = dict(zip(columns, row))
                    # Parse JSON fields
                    result['trading_signals'] = json.loads(result['trading_signals'] or '{}')
                    result['tool_results'] = json.loads(result['tool_results'] or '{}')
                    result['entry_levels'] = json.loads(result['entry_levels'] or '[]')
                    result['take_profit'] = json.loads(result['take_profit'] or '[]')
                    return result

                return None

        except Exception as e:
            logger.error(f"Failed to get analysis by ID: {e}")
            return None

    def get_trade_execution_by_analysis_id(self, analysis_id: str) -> Dict[str, Any]:
        """Get trade execution for specific analysis."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM trade_executions WHERE analysis_id = ?
                """, (analysis_id,))

                row = cursor.fetchone()
                if row:
                    columns = [desc[0] for desc in cursor.description]
                    return dict(zip(columns, row))

                return None

        except Exception as e:
            logger.error(f"Failed to get trade execution: {e}")
            return None

    def get_trading_statistics(self) -> Dict[str, Any]:
        """Get comprehensive trading statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Total analyses
                cursor.execute("SELECT COUNT(*) FROM analysis_results")
                total_analyses = cursor.fetchone()[0]

                # Trades taken
                cursor.execute("SELECT COUNT(*) FROM trade_executions")
                trades_taken = cursor.fetchone()[0]

                # Completed trades (with outcomes)
                cursor.execute("SELECT COUNT(*) FROM trade_outcomes")
                completed_trades = cursor.fetchone()[0]

                # Win rate and P&L
                cursor.execute("""
                    SELECT
                        COUNT(CASE WHEN outcome = 'win' THEN 1 END) as wins,
                        COUNT(*) as total_outcomes,
                        AVG(pnl_percentage) as avg_return,
                        SUM(pnl_amount) as total_pnl
                    FROM trade_outcomes
                """)

                outcome_stats = cursor.fetchone()
                wins = outcome_stats[0] or 0
                total_outcomes = outcome_stats[1] or 0
                avg_return = outcome_stats[2] or 0.0
                total_pnl = outcome_stats[3] or 0.0

                win_rate = (wins / total_outcomes * 100) if total_outcomes > 0 else 0.0

                return {
                    "total_analyses": total_analyses,
                    "trades_taken": trades_taken,
                    "completed_trades": completed_trades,
                    "win_rate": win_rate,
                    "avg_return": avg_return,
                    "total_pnl": total_pnl
                }

        except Exception as e:
            logger.error(f"Failed to get trading statistics: {e}")
            return {
                "total_analyses": 0,
                "trades_taken": 0,
                "completed_trades": 0,
                "win_rate": 0.0,
                "avg_return": 0.0,
                "total_pnl": 0.0
            }


# Global instance
trade_storage = TradeResultStorage()

def get_trade_storage() -> TradeResultStorage:
    """Get the global trade storage instance."""
    return trade_storage
