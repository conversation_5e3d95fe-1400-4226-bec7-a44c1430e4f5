#!/usr/bin/env python3
"""
Enhanced Economic Calendar Tool using TradingEconomics API
Provides real economic calendar data for trading analysis
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
from dataclasses import dataclass

logger = logging.getLogger(__name__)

# Try to import tradingeconomics
try:
    import tradingeconomics as te
    TRADING_ECONOMICS_AVAILABLE = True
except ImportError:
    TRADING_ECONOMICS_AVAILABLE = False
    logger.warning("TradingEconomics package not available. Install with: pip install tradingeconomics")

@dataclass
class EconomicEvent:
    """Economic event data structure."""
    event_name: str
    date: datetime
    impact: str  # "high", "medium", "low"
    currency: str
    actual: Optional[str] = None
    forecast: Optional[str] = None
    previous: Optional[str] = None

class EconomicCalendarTool:
    """Enhanced economic calendar tool using TradingEconomics API."""

    def __init__(self):
        self.logger = logger
        self.api_authenticated = False

        # Initialize TradingEconomics API if available
        if TRADING_ECONOMICS_AVAILABLE:
            self._initialize_trading_economics_api()

        # High impact events that affect different markets
        self.high_impact_events = {
            "crypto": [
                "Interest Rate Decision", "FOMC", "CPI", "NFP", "GDP",
                "Powell Speech", "PPI", "Unemployment Rate"
            ],
            "indian": [
                "Interest Rate Decision", "CPI", "GDP", "Manufacturing PMI",
                "Services PMI", "Trade Balance", "Industrial Production"
            ],
            "us": [
                "Interest Rate Decision", "FOMC", "CPI", "NFP", "GDP",
                "PPI", "Unemployment Rate", "Retail Sales"
            ]
        }

    def _initialize_trading_economics_api(self):
        """Initialize TradingEconomics API with credentials."""
        try:
            # Try to load API credentials from config
            config_path = os.path.join(os.path.dirname(__file__), '..', 'config.json')
            with open(config_path, 'r') as f:
                config = json.load(f)

            te_key = config.get('trading_economics_key', '')
            te_secret = config.get('trading_economics_secret', '')

            if te_key and te_secret and te_key != "your_trading_economics_api_key":
                te.login(f'{te_key}:{te_secret}')
                self.api_authenticated = True
                logger.info("TradingEconomics API authenticated successfully")
            else:
                logger.warning("TradingEconomics API credentials not configured - using fallback data")
                self.api_authenticated = False

        except Exception as e:
            logger.warning(f"Failed to authenticate TradingEconomics API: {e}")
            self.api_authenticated = False
    
    def is_major_event_today(self, symbol: str, market_type: str = "crypto") -> Dict[str, Any]:
        """
        Check if there are major economic events today that could impact trading.

        Args:
            symbol: Trading symbol (e.g., "BTC/USD", "NIFTY")
            market_type: Market type ("crypto", "indian", "us")

        Returns:
            Dict with event information and risk assessment
        """
        try:
            today = datetime.now()

            # Get events using TradingEconomics API if available
            if TRADING_ECONOMICS_AVAILABLE and self.api_authenticated:
                events_today = self._get_real_events_today(market_type)
            else:
                # Fallback to simulated events
                events_today = self._get_simulated_events_for_date(today, market_type)

            # Analyze risk level
            risk_level = self._assess_risk_level(events_today)

            # Create trading advisory
            advisory = self._create_trading_advisory(events_today, symbol, market_type)

            return {
                "success": True,
                "date": today.strftime("%Y-%m-%d"),
                "symbol": symbol,
                "market_type": market_type,
                "events_today": [self._event_to_dict(event) for event in events_today],
                "risk_level": risk_level,
                "trading_advisory": advisory,
                "event_count": len(events_today),
                "data_source": "TradingEconomics API" if (TRADING_ECONOMICS_AVAILABLE and self.api_authenticated) else "Simulated"
            }

        except Exception as e:
            self.logger.error(f"Economic calendar check failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "symbol": symbol,
                "risk_level": "unknown"
            }
    
    def _get_real_events_today(self, market_type: str) -> List[EconomicEvent]:
        """Get real economic events for today using TradingEconomics API."""
        try:
            # Check if authenticated
            if not self.api_authenticated:
                logger.warning("TradingEconomics API not authenticated - using fallback data")
                return self._get_simulated_events_for_date(datetime.now(), market_type)

            # Get today's calendar data
            calendar_data = te.getCalendarData()

            if not calendar_data:
                return []

            events = []
            today = datetime.now().date()

            # Convert to DataFrame if it's not already
            if hasattr(calendar_data, 'iterrows'):
                df = calendar_data
            else:
                import pandas as pd
                df = pd.DataFrame(calendar_data)

            # Filter for today's events
            for _, row in df.iterrows():
                try:
                    # Parse event date
                    event_date = pd.to_datetime(row.get('Date', '')).date()

                    if event_date != today:
                        continue

                    # Check if event is relevant for the market type
                    event_name = str(row.get('Event', ''))
                    country = str(row.get('Country', ''))
                    importance = str(row.get('Importance', 'Low'))

                    # Filter by market relevance
                    if not self._is_event_relevant(event_name, country, market_type):
                        continue

                    # Create EconomicEvent
                    event = EconomicEvent(
                        event_name=event_name,
                        date=datetime.combine(event_date, datetime.min.time()),
                        impact=self._map_importance_to_impact(importance),
                        currency=self._get_currency_for_country(country),
                        actual=str(row.get('Actual', '')),
                        forecast=str(row.get('Forecast', '')),
                        previous=str(row.get('Previous', ''))
                    )

                    events.append(event)

                except Exception as e:
                    logger.warning(f"Failed to parse calendar event: {e}")
                    continue

            return events

        except Exception as e:
            logger.error(f"Failed to get real calendar data: {e}")
            return []

    def _is_event_relevant(self, event_name: str, country: str, market_type: str) -> bool:
        """Check if an economic event is relevant for the given market type."""
        event_lower = event_name.lower()
        country_lower = country.lower()

        if market_type == "crypto":
            # Crypto is affected by major US/global events
            relevant_countries = ["united states", "usa", "us", "china", "european union", "eu"]
            relevant_events = self.high_impact_events["crypto"]

            return (any(country_name in country_lower for country_name in relevant_countries) and
                    any(event_keyword.lower() in event_lower for event_keyword in relevant_events))

        elif market_type == "indian":
            # Indian markets affected by India-specific and major global events
            relevant_countries = ["india", "united states", "usa", "us"]
            relevant_events = self.high_impact_events["indian"]

            return (any(country_name in country_lower for country_name in relevant_countries) and
                    any(event_keyword.lower() in event_lower for event_keyword in relevant_events))

        elif market_type == "us":
            # US markets affected by US events
            relevant_countries = ["united states", "usa", "us"]
            relevant_events = self.high_impact_events["us"]

            return (any(country_name in country_lower for country_name in relevant_countries) and
                    any(event_keyword.lower() in event_lower for event_keyword in relevant_events))

        return False

    def _map_importance_to_impact(self, importance: str) -> str:
        """Map TradingEconomics importance to our impact levels."""
        importance_lower = importance.lower()
        if "high" in importance_lower or "3" in importance:
            return "high"
        elif "medium" in importance_lower or "2" in importance:
            return "medium"
        else:
            return "low"

    def _get_currency_for_country(self, country: str) -> str:
        """Get currency code for a country."""
        country_lower = country.lower()
        currency_map = {
            "united states": "USD", "usa": "USD", "us": "USD",
            "india": "INR",
            "european union": "EUR", "eu": "EUR", "eurozone": "EUR",
            "china": "CNY",
            "japan": "JPY",
            "united kingdom": "GBP", "uk": "GBP"
        }

        for country_name, currency in currency_map.items():
            if country_name in country_lower:
                return currency

        return "USD"  # Default

    def _event_to_dict(self, event: EconomicEvent) -> Dict[str, Any]:
        """Convert EconomicEvent to dictionary for JSON serialization."""
        return {
            "event_name": event.event_name,
            "date": event.date.isoformat(),
            "impact": event.impact,
            "currency": event.currency,
            "actual": event.actual,
            "forecast": event.forecast,
            "previous": event.previous
        }

    def _get_simulated_events_for_date(self, date: datetime, market_type: str) -> List[EconomicEvent]:
        """Get economic events for a specific date."""
        # This is a simplified implementation
        # In production, you'd integrate with APIs like:
        # - ForexFactory API
        # - TradingEconomics API
        # - Alpha Vantage Economic Indicators
        # - RBI/Fed official calendars
        
        events = []
        
        # Simulate some common events based on date patterns
        weekday = date.weekday()  # 0=Monday, 6=Sunday
        day_of_month = date.day
        
        # Fed meetings (typically every 6-8 weeks, Wednesdays)
        if market_type in ["crypto", "us"] and weekday == 2 and day_of_month in [15, 16, 17]:
            events.append(EconomicEvent(
                event_name="FOMC Meeting Decision",
                date=date,
                impact="high",
                currency="USD"
            ))
        
        # CPI releases (typically mid-month)
        if weekday == 2 and day_of_month in [12, 13, 14, 15]:  # Tuesday mid-month
            if market_type in ["crypto", "us"]:
                events.append(EconomicEvent(
                    event_name="US CPI Release",
                    date=date,
                    impact="high",
                    currency="USD"
                ))
            elif market_type == "indian":
                events.append(EconomicEvent(
                    event_name="India CPI Release",
                    date=date,
                    impact="medium",
                    currency="INR"
                ))
        
        # NFP (First Friday of month)
        if market_type in ["crypto", "us"] and weekday == 4 and 1 <= day_of_month <= 7:
            events.append(EconomicEvent(
                event_name="Non-Farm Payrolls (NFP)",
                date=date,
                impact="high",
                currency="USD"
            ))
        
        # RBI meetings (typically every 2 months)
        if market_type == "indian" and weekday in [1, 2] and day_of_month in [4, 5, 6, 7]:
            events.append(EconomicEvent(
                event_name="RBI Monetary Policy Decision",
                date=date,
                impact="high",
                currency="INR"
            ))
        
        return events
    
    def _assess_risk_level(self, events: List[EconomicEvent]) -> str:
        """Assess overall risk level based on events."""
        if not events:
            return "low"
        
        high_impact_count = sum(1 for event in events if event.impact == "high")
        medium_impact_count = sum(1 for event in events if event.impact == "medium")
        
        if high_impact_count >= 2:
            return "very_high"
        elif high_impact_count >= 1:
            return "high"
        elif medium_impact_count >= 2:
            return "medium"
        elif medium_impact_count >= 1 or len(events) >= 3:
            return "medium"
        else:
            return "low"
    
    def _create_trading_advisory(self, events: List[EconomicEvent], symbol: str, market_type: str) -> str:
        """Create trading advisory based on events."""
        if not events:
            return f"No major economic events today. Normal trading conditions for {symbol}."
        
        advisory_parts = []
        
        # Risk warning
        risk_level = self._assess_risk_level(events)
        if risk_level in ["high", "very_high"]:
            advisory_parts.append(f"⚠️ ELEVATED RISK: {len(events)} major event(s) today affecting {symbol}")
        elif risk_level == "medium":
            advisory_parts.append(f"⚡ MODERATE RISK: {len(events)} event(s) may impact {symbol} volatility")
        
        # Event details
        for event in events:
            time_str = event.date.strftime("%H:%M") if event.date.hour != 0 else "TBD"
            advisory_parts.append(f"• {event.event_name} ({event.impact} impact) - {time_str}")
        
        # Trading recommendations
        if risk_level in ["high", "very_high"]:
            advisory_parts.append("📋 RECOMMENDATION: Reduce position sizes, widen stops, avoid new entries near event times")
        elif risk_level == "medium":
            advisory_parts.append("📋 RECOMMENDATION: Monitor closely, consider tighter risk management")
        
        return " | ".join(advisory_parts)

# Tool function for integration
def get_economic_calendar_risk(symbol: str, market_type: str = "crypto") -> Dict[str, Any]:
    """
    Get economic calendar risk assessment for trading decisions.
    
    Args:
        symbol: Trading symbol
        market_type: Market type ("crypto", "indian", "us")
        
    Returns:
        Economic calendar risk assessment
    """
    tool = EconomicCalendarTool()
    return tool.is_major_event_today(symbol, market_type)
