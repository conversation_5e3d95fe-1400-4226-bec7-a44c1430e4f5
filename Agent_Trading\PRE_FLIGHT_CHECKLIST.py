#!/usr/bin/env python3
"""
🚀 PRE-FLIGHT CHECKLIST FOR TOMORROW'S CHART ANALYSIS
Comprehensive system verification to ensure smooth operation
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, List

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies() -> Dict[str, bool]:
    """Check all critical dependencies."""
    print("🔍 CHECKING DEPENDENCIES...")
    
    dependencies = {}
    
    # Core dependencies
    try:
        import streamlit
        dependencies["streamlit"] = True
        print("  ✅ Streamlit available")
    except ImportError:
        dependencies["streamlit"] = False
        print("  ❌ Streamlit missing")
    
    try:
        import google.generativeai
        dependencies["google-generativeai"] = True
        print("  ✅ Google Generative AI available")
    except ImportError:
        dependencies["google-generativeai"] = False
        print("  ❌ Google Generative AI missing")
    
    try:
        import chromadb
        dependencies["chromadb"] = True
        print("  ✅ ChromaDB available")
    except ImportError:
        dependencies["chromadb"] = False
        print("  ❌ ChromaDB missing")
    
    try:
        import langgraph
        dependencies["langgraph"] = True
        print("  ✅ LangGraph available")
    except ImportError:
        dependencies["langgraph"] = False
        print("  ❌ LangGraph missing")
    
    try:
        from PIL import Image
        dependencies["pillow"] = True
        print("  ✅ Pillow available")
    except ImportError:
        dependencies["pillow"] = False
        print("  ❌ Pillow missing")
    
    try:
        import pandas
        dependencies["pandas"] = True
        print("  ✅ Pandas available")
    except ImportError:
        dependencies["pandas"] = False
        print("  ❌ Pandas missing")
    
    return dependencies

def check_configuration() -> Dict[str, Any]:
    """Check configuration files and API keys."""
    print("\n🔧 CHECKING CONFIGURATION...")
    
    config_status = {}
    
    # Check config.json
    config_path = "config.json"
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # Check Google API key
            google_api_key = config.get("google_api_key")
            if google_api_key and google_api_key != "YOUR_GEMINI_API_KEY":
                config_status["google_api_key"] = True
                print("  ✅ Google API key configured")
            else:
                config_status["google_api_key"] = False
                print("  ❌ Google API key missing or placeholder")
            
            # Check other configurations
            config_status["langgraph_enabled"] = config.get("langgraph", {}).get("enabled", False)
            print(f"  ✅ LangGraph enabled: {config_status['langgraph_enabled']}")
            
        except Exception as e:
            config_status["config_file"] = False
            print(f"  ❌ Config file error: {e}")
    else:
        config_status["config_file"] = False
        print("  ❌ config.json not found")
    
    return config_status

def check_helper_modules() -> Dict[str, bool]:
    """Check all critical helper modules."""
    print("\n📦 CHECKING HELPER MODULES...")
    
    modules = {}
    
    critical_modules = [
        "helpers.complete_langgraph_workflow",
        "helpers.parallel_utils", 
        "helpers.prompts",
        "helpers.utils",
        "helpers.chromadb_rag_system",
        "helpers.clean_tool_manager",
        "helpers.duckduckgo_news",
        "helpers.yfinance_integration",
        "helpers.indian_market_tools",
        "helpers.llm_logger",
        "helpers.workflow_logger",
        "helpers.trade_result_storage"
    ]
    
    for module_name in critical_modules:
        try:
            __import__(module_name)
            modules[module_name] = True
            print(f"  ✅ {module_name}")
        except ImportError as e:
            modules[module_name] = False
            print(f"  ❌ {module_name}: {e}")
    
    return modules

def check_workflow_structure() -> Dict[str, bool]:
    """Check workflow structure and initialization."""
    print("\n🔄 CHECKING WORKFLOW STRUCTURE...")
    
    workflow_status = {}
    
    try:
        from helpers.complete_langgraph_workflow import CompleteLangGraphTradingWorkflow
        from helpers.utils import load_google_api_key
        
        # Try to load API key
        api_key = load_google_api_key()
        if api_key:
            workflow_status["api_key_loading"] = True
            print("  ✅ API key loading works")
            
            # Try to initialize workflow (without making API calls)
            try:
                workflow = CompleteLangGraphTradingWorkflow(api_key)
                workflow_status["workflow_initialization"] = True
                print("  ✅ Workflow initialization works")
                
                # Check if workflow has required methods
                required_methods = ["analyze_chart", "comprehensive_analysis_node", "_analyze_chart_for_symbol_detection"]
                for method in required_methods:
                    if hasattr(workflow, method):
                        workflow_status[f"method_{method}"] = True
                        print(f"  ✅ Method {method} available")
                    else:
                        workflow_status[f"method_{method}"] = False
                        print(f"  ❌ Method {method} missing")
                
            except Exception as e:
                workflow_status["workflow_initialization"] = False
                print(f"  ❌ Workflow initialization failed: {e}")
        else:
            workflow_status["api_key_loading"] = False
            print("  ❌ API key loading failed")
            
    except Exception as e:
        workflow_status["import_error"] = str(e)
        print(f"  ❌ Workflow import failed: {e}")
    
    return workflow_status

def check_gui_integration() -> Dict[str, bool]:
    """Check GUI integration points."""
    print("\n🖥️ CHECKING GUI INTEGRATION...")
    
    gui_status = {}
    
    try:
        # Check if GUI app can be imported
        sys.path.insert(0, "GUI")
        
        # Check critical GUI functions
        gui_file = "GUI/app.py"
        if os.path.exists(gui_file):
            with open(gui_file, 'r') as f:
                gui_content = f.read()
            
            # Check for critical functions
            critical_functions = [
                "display_beautiful_tool_results",
                "display_single_tool_result", 
                "load_google_api_key",
                "CompleteLangGraphTradingWorkflow"
            ]
            
            for func in critical_functions:
                if func in gui_content:
                    gui_status[f"function_{func}"] = True
                    print(f"  ✅ Function {func} found")
                else:
                    gui_status[f"function_{func}"] = False
                    print(f"  ❌ Function {func} missing")
            
            # Check for tool_usage handling
            if "tool_usage" in gui_content and "display_beautiful_tool_results" in gui_content:
                gui_status["tool_display_integration"] = True
                print("  ✅ Tool display integration ready")
            else:
                gui_status["tool_display_integration"] = False
                print("  ❌ Tool display integration missing")
                
        else:
            gui_status["gui_file"] = False
            print("  ❌ GUI app.py not found")
            
    except Exception as e:
        gui_status["gui_error"] = str(e)
        print(f"  ❌ GUI check failed: {e}")
    
    return gui_status

def check_memory_system() -> Dict[str, bool]:
    """Check memory and RAG system."""
    print("\n🧠 CHECKING MEMORY SYSTEM...")
    
    memory_status = {}
    
    try:
        from helpers.chromadb_rag_system import ChromaDBTradingRAGSystem
        
        # Try to initialize (will create directory if needed)
        rag_system = ChromaDBTradingRAGSystem()
        memory_status["rag_initialization"] = True
        print("  ✅ RAG system initialization works")
        
        # Check if basic methods exist
        required_methods = ["ingest_analysis_summary", "get_contextual_memory", "query_similar_analyses"]
        for method in required_methods:
            if hasattr(rag_system, method):
                memory_status[f"rag_method_{method}"] = True
                print(f"  ✅ RAG method {method} available")
            else:
                memory_status[f"rag_method_{method}"] = False
                print(f"  ❌ RAG method {method} missing")
        
    except Exception as e:
        memory_status["rag_error"] = str(e)
        print(f"  ❌ RAG system check failed: {e}")
    
    return memory_status

def run_comprehensive_check() -> Dict[str, Any]:
    """Run all checks and return comprehensive status."""
    print("🚀 COMPREHENSIVE PRE-FLIGHT CHECK")
    print("=" * 50)
    
    results = {
        "dependencies": check_dependencies(),
        "configuration": check_configuration(), 
        "helper_modules": check_helper_modules(),
        "workflow_structure": check_workflow_structure(),
        "gui_integration": check_gui_integration(),
        "memory_system": check_memory_system()
    }
    
    return results

def generate_readiness_report(results: Dict[str, Any]) -> None:
    """Generate final readiness report."""
    print("\n" + "=" * 50)
    print("📋 READINESS REPORT")
    print("=" * 50)
    
    # Count successes and failures
    total_checks = 0
    passed_checks = 0
    
    critical_failures = []
    warnings = []
    
    for category, checks in results.items():
        print(f"\n🔍 {category.upper()}:")
        for check, status in checks.items():
            total_checks += 1
            if status is True:
                passed_checks += 1
                print(f"  ✅ {check}")
            else:
                print(f"  ❌ {check}: {status}")
                if category in ["dependencies", "configuration", "workflow_structure"]:
                    critical_failures.append(f"{category}.{check}")
                else:
                    warnings.append(f"{category}.{check}")
    
    # Calculate readiness score
    readiness_score = (passed_checks / total_checks) * 100
    
    print(f"\n📊 OVERALL READINESS: {readiness_score:.1f}% ({passed_checks}/{total_checks} checks passed)")
    
    if readiness_score >= 90:
        print("🎉 SYSTEM READY FOR TOMORROW'S TESTING!")
    elif readiness_score >= 75:
        print("⚠️ SYSTEM MOSTLY READY - Minor issues to address")
    else:
        print("🚨 SYSTEM NOT READY - Critical issues need fixing")
    
    if critical_failures:
        print(f"\n🚨 CRITICAL FAILURES ({len(critical_failures)}):")
        for failure in critical_failures:
            print(f"  - {failure}")
    
    if warnings:
        print(f"\n⚠️ WARNINGS ({len(warnings)}):")
        for warning in warnings:
            print(f"  - {warning}")
    
    print(f"\n🎯 NEXT STEPS:")
    if readiness_score >= 90:
        print("  1. Wait for API quota reset")
        print("  2. Test with real chart images")
        print("  3. Verify tool summaries display correctly")
    else:
        print("  1. Fix critical failures first")
        print("  2. Address warnings if possible")
        print("  3. Re-run this checklist")

if __name__ == "__main__":
    results = run_comprehensive_check()
    generate_readiness_report(results)
