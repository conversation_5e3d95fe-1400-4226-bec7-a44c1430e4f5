"""
ChromaDB-based RAG System for Trading Analysis
Optimized for contextual retrieval with metadata filtering
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import hashlib
import json

try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    chromadb = None

logger = logging.getLogger(__name__)

class ChromaDBTradingRAGSystem:
    """ChromaDB-based RAG system optimized for trading analysis with metadata filtering"""
    
    def __init__(self, persist_directory: str = "data/chromadb"):
        if not CHROMADB_AVAILABLE:
            raise ImportError("ChromaDB not available. Install with: pip install chromadb")
        
        self.persist_directory = persist_directory
        
        # Initialize ChromaDB client with persistence
        self.client = chromadb.PersistentClient(path=persist_directory)
        
        # Get or create collection for trading analysis
        self.collection = self.client.get_or_create_collection(
            name="trading_analysis",
            metadata={"description": "Trading analysis summaries with contextual metadata"}
        )
        
        logger.info(f"ChromaDB RAG system initialized with {self.collection.count()} existing documents")
    
    def ingest_analysis_summary(self, 
                              summary: str, 
                              symbol: str, 
                              mode: str = "general",
                              market: str = "general",
                              analysis_type: str = "market_data",
                              metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Ingest analysis summary with rich metadata for contextual retrieval
        
        Args:
            summary: The analysis summary text
            symbol: Trading symbol (BTC, NIFTY, etc.)
            mode: Trading mode (positional, scalp)
            market: Market type (crypto, indian_market)
            analysis_type: Type of analysis (market_data, news, technical)
            metadata: Additional metadata
        
        Returns:
            Document ID
        """
        try:
            # Create unique document ID
            doc_id = hashlib.md5(f"{symbol}_{mode}_{datetime.now().isoformat()}_{summary[:50]}".encode()).hexdigest()
            
            # Prepare metadata
            doc_metadata = {
                "symbol": symbol.upper(),
                "mode": mode.lower(),
                "market": market.lower(),
                "analysis_type": analysis_type,
                "timestamp": datetime.now().isoformat(),
                "date": datetime.now().strftime("%Y-%m-%d"),
                "month": datetime.now().strftime("%Y-%m"),
                **(metadata or {})
            }
            
            # Add to ChromaDB collection
            self.collection.add(
                documents=[summary],
                metadatas=[doc_metadata],
                ids=[doc_id]
            )
            
            logger.info(f"Ingested {analysis_type} summary for {symbol} {mode} analysis")
            return doc_id
            
        except Exception as e:
            logger.error(f"Failed to ingest analysis summary: {e}")
            return ""
    
    def ingest_market_data(self, market_data: Dict[str, Any], symbol: str, mode: str = "general", market: str = "general"):
        """Ingest market data summary"""
        try:
            # Create summary from market data
            summary_parts = []
            
            if isinstance(market_data, dict):
                if "current_price" in market_data:
                    summary_parts.append(f"{symbol} price: {market_data['current_price']}")
                if "price_change_pct" in market_data:
                    summary_parts.append(f"Change: {market_data['price_change_pct']}%")
                if "volume" in market_data:
                    summary_parts.append(f"Volume: {market_data['volume']}")
                if "trend" in market_data:
                    summary_parts.append(f"Trend: {market_data['trend']}")
                if "support_levels" in market_data:
                    summary_parts.append(f"Support: {market_data['support_levels']}")
                if "resistance_levels" in market_data:
                    summary_parts.append(f"Resistance: {market_data['resistance_levels']}")
            
            summary = f"{symbol} Market Data: " + ", ".join(summary_parts)
            
            # Extract price level for metadata
            price_level = market_data.get("current_price", 0) if isinstance(market_data, dict) else 0
            
            metadata = {
                "price_level": price_level,
                "data_source": "market_data_tool"
            }
            
            return self.ingest_analysis_summary(summary, symbol, mode, market, "market_data", metadata)
            
        except Exception as e:
            logger.error(f"Failed to ingest market data: {e}")
    
    def ingest_news_data(self, news_data: Dict[str, Any], symbol: str, mode: str = "general", market: str = "general"):
        """Ingest news data summary"""
        try:
            summary_parts = []
            
            if isinstance(news_data, dict) and "news_articles" in news_data:
                for article in news_data["news_articles"][:3]:  # Top 3 articles
                    title = article.get("title", "")
                    snippet = article.get("snippet", "")
                    summary_parts.append(f"{title}: {snippet}")
            
            summary = f"{symbol} News Summary: " + " | ".join(summary_parts)
            
            metadata = {
                "news_count": len(news_data.get("news_articles", [])) if isinstance(news_data, dict) else 0,
                "data_source": "news_tool"
            }
            
            return self.ingest_analysis_summary(summary, symbol, mode, market, "news", metadata)
            
        except Exception as e:
            logger.error(f"Failed to ingest news data: {e}")
    
    def get_contextual_memory(self, 
                            symbol: str, 
                            mode: str = "general",
                            market: str = "general",
                            query: str = "",
                            n_results: int = 5) -> str:
        """
        Get contextual memory with metadata filtering
        
        Args:
            symbol: Trading symbol to filter by
            mode: Trading mode to filter by
            market: Market type to filter by
            query: Semantic query text
            n_results: Number of results to return
        
        Returns:
            Formatted context string
        """
        try:
            # Build metadata filter
            where_filter = {
                "symbol": symbol.upper()
            }
            
            # Add optional filters
            if mode != "general":
                where_filter["mode"] = mode.lower()
            if market != "general":
                where_filter["market"] = market.lower()
            
            # Query with metadata filtering
            if query:
                search_query = f"{symbol} {mode} {query}"
            else:
                search_query = f"{symbol} {mode} analysis patterns and signals"

            # ChromaDB requires specific where clause format
            if len(where_filter) == 1:
                # Single filter
                key, value = next(iter(where_filter.items()))
                where_clause = {key: {"$eq": value}}
            else:
                # Multiple filters - use $and
                where_clause = {"$and": [{k: {"$eq": v}} for k, v in where_filter.items()]}

            results = self.collection.query(
                query_texts=[search_query],
                where=where_clause,
                n_results=n_results
            )
            
            if not results["documents"] or not results["documents"][0]:
                return ""
            
            # Format results
            context_parts = []
            for i, (doc, metadata) in enumerate(zip(results["documents"][0], results["metadatas"][0])):
                timestamp = metadata.get("date", "Unknown date")
                analysis_type = metadata.get("analysis_type", "analysis")
                context_parts.append(f"[{timestamp} {analysis_type.title()}] {doc}")
            
            context = "\n".join(context_parts)
            logger.info(f"Retrieved {len(context_parts)} contextual memories for {symbol} {mode}")
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to retrieve contextual memory: {e}")
            return ""
    
    def get_relevant_context(self, query: str, n_results: int = 5) -> str:
        """
        Get relevant context using semantic search (backward compatibility)
        """
        try:
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results
            )
            
            if not results["documents"] or not results["documents"][0]:
                return ""
            
            # Format results
            context_parts = []
            for doc, metadata in zip(results["documents"][0], results["metadatas"][0]):
                symbol = metadata.get("symbol", "")
                mode = metadata.get("mode", "")
                date = metadata.get("date", "")
                context_parts.append(f"[{symbol} {mode} - {date}] {doc}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Failed to get relevant context: {e}")
            return ""
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the RAG collection"""
        try:
            count = self.collection.count()
            
            # Get sample of metadata to analyze
            if count > 0:
                sample = self.collection.get(limit=min(100, count))
                symbols = set()
                modes = set()
                markets = set()
                
                for metadata in sample["metadatas"]:
                    symbols.add(metadata.get("symbol", ""))
                    modes.add(metadata.get("mode", ""))
                    markets.add(metadata.get("market", ""))
                
                return {
                    "total_documents": count,
                    "unique_symbols": len(symbols),
                    "symbols": list(symbols),
                    "modes": list(modes),
                    "markets": list(markets)
                }
            else:
                return {"total_documents": 0}
                
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {"error": str(e)}

    def get_analysis_stats(self) -> Dict[str, Any]:
        """Get analysis statistics for dashboard display"""
        try:
            stats = self.get_collection_stats()
            return {
                'total_analyses': stats.get('total_documents', 0),
                'unique_symbols': stats.get('unique_symbols', 0),
                'analysis_modes': stats.get('modes', []),
                'markets_covered': stats.get('markets', []),
                'status': 'active' if stats.get('total_documents', 0) > 0 else 'empty'
            }
        except Exception as e:
            logger.error(f"Failed to get analysis stats: {e}")
            return {
                'total_analyses': 0,
                'unique_symbols': 0,
                'analysis_modes': [],
                'markets_covered': [],
                'status': 'error',
                'error': str(e)
            }

    def store_trading_outcome(self,
                            setup_data: Dict[str, Any],
                            outcome_data: Dict[str, Any],
                            symbol: str,
                            mode: str = "positional",
                            market: str = "crypto") -> str:
        """
        Store trading setup and outcome for learning purposes.

        Args:
            setup_data: Original analysis setup (RSI, MACD, news, levels)
            outcome_data: Trade outcome (entry, exit, profit/loss, reason)
            symbol: Trading symbol
            mode: Analysis mode (positional/scalp)
            market: Market type (crypto/indian/us)

        Returns:
            Document ID for the stored outcome
        """
        try:
            # Create comprehensive outcome summary
            outcome_summary = f"""
TRADING OUTCOME: {symbol} {mode} analysis

SETUP CONTEXT:
- Entry Signal: {setup_data.get('entry_signal', 'N/A')}
- Technical Setup: {setup_data.get('technical_setup', 'N/A')}
- Market Context: {setup_data.get('market_context', 'N/A')}
- Risk Level: {setup_data.get('risk_level', 'N/A')}
- Confidence Score: {setup_data.get('confidence', 'N/A')}/10

TRADE EXECUTION:
- Entry Price: {outcome_data.get('entry_price', 'N/A')}
- Exit Price: {outcome_data.get('exit_price', 'N/A')}
- Outcome: {outcome_data.get('outcome', 'N/A')} ({outcome_data.get('pnl_percentage', 'N/A')}%)
- Risk-Reward: {outcome_data.get('risk_reward_achieved', 'N/A')}
- Hold Duration: {outcome_data.get('hold_duration', 'N/A')}

OUTCOME ANALYSIS:
- Success Reason: {outcome_data.get('success_reason', 'N/A')}
- Failure Reason: {outcome_data.get('failure_reason', 'N/A')}
- Market Condition: {outcome_data.get('market_condition_during_trade', 'N/A')}
- Lessons Learned: {outcome_data.get('lessons_learned', 'N/A')}
"""

            # Create metadata for outcome tracking
            metadata = {
                "symbol": symbol,
                "mode": mode,
                "market": market,
                "outcome": outcome_data.get('outcome', 'unknown'),
                "pnl_percentage": outcome_data.get('pnl_percentage', 0),
                "confidence_score": setup_data.get('confidence', 0),
                "risk_reward": outcome_data.get('risk_reward_achieved', 0),
                "setup_type": setup_data.get('setup_type', 'unknown'),
                "timestamp": datetime.now().isoformat(),
                "document_type": "trading_outcome"
            }

            # Generate unique document ID
            doc_id = f"outcome_{symbol}_{mode}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(outcome_summary) % 10000}"

            # Store in ChromaDB
            self.collection.add(
                documents=[outcome_summary],
                metadatas=[metadata],
                ids=[doc_id]
            )

            logger.info(f"Stored trading outcome for {symbol} {mode}: {outcome_data.get('outcome', 'unknown')}")
            return doc_id

        except Exception as e:
            logger.error(f"Failed to store trading outcome: {e}")
            return ""

    def query_historical_outcomes(self,
                                symbol: str,
                                setup_type: str = None,
                                mode: str = "positional",
                                limit: int = 10) -> Dict[str, Any]:
        """
        Query historical trading outcomes for pattern learning.

        Args:
            symbol: Trading symbol to query
            setup_type: Type of setup (e.g., "RSI_divergence", "breakout")
            mode: Analysis mode (positional/scalp)
            limit: Maximum number of results

        Returns:
            Historical outcomes with success rate and insights
        """
        try:
            # Build query for historical outcomes
            query_text = f"{symbol} {mode} trading outcome"
            if setup_type:
                query_text += f" {setup_type}"

            # Query ChromaDB for similar outcomes
            results = self.collection.query(
                query_texts=[query_text],
                n_results=limit,
                where={"$and": [{"document_type": "trading_outcome"}, {"symbol": symbol}]}
            )

            if not results['documents'] or not results['documents'][0]:
                return {
                    "success": True,
                    "symbol": symbol,
                    "historical_outcomes": [],
                    "success_rate": 0,
                    "average_rr": 0,
                    "insights": f"No historical outcomes found for {symbol} {mode} analysis"
                }

            # Analyze outcomes
            outcomes = []
            wins = 0
            total_rr = 0

            for i, doc in enumerate(results['documents'][0]):
                metadata = results['metadatas'][0][i]
                outcome = {
                    "outcome": metadata.get('outcome', 'unknown'),
                    "pnl_percentage": metadata.get('pnl_percentage', 0),
                    "confidence_score": metadata.get('confidence_score', 0),
                    "risk_reward": metadata.get('risk_reward', 0),
                    "setup_type": metadata.get('setup_type', 'unknown'),
                    "timestamp": metadata.get('timestamp', ''),
                    "summary": doc[:200] + "..." if len(doc) > 200 else doc
                }
                outcomes.append(outcome)

                if metadata.get('outcome') == 'win':
                    wins += 1
                    total_rr += metadata.get('risk_reward', 0)

            # Calculate statistics
            total_trades = len(outcomes)
            success_rate = (wins / total_trades * 100) if total_trades > 0 else 0
            average_rr = (total_rr / wins) if wins > 0 else 0

            # Generate insights
            insights = self._generate_outcome_insights(outcomes, success_rate, average_rr, symbol)

            return {
                "success": True,
                "symbol": symbol,
                "setup_type": setup_type,
                "historical_outcomes": outcomes,
                "total_trades": total_trades,
                "wins": wins,
                "losses": total_trades - wins,
                "success_rate": round(success_rate, 1),
                "average_rr": round(average_rr, 2),
                "insights": insights
            }

        except Exception as e:
            logger.error(f"Failed to query historical outcomes: {e}")
            return {
                "success": False,
                "error": str(e),
                "symbol": symbol,
                "insights": "Failed to retrieve historical outcomes"
            }

    def _generate_outcome_insights(self, outcomes: List[Dict], success_rate: float, average_rr: float, symbol: str) -> str:
        """Generate trading insights from historical outcomes."""
        if not outcomes:
            return f"No historical data available for {symbol}"

        insights = []

        # Success rate insight
        if success_rate >= 70:
            insights.append(f"✅ HIGH SUCCESS RATE: {success_rate}% win rate indicates strong pattern reliability")
        elif success_rate >= 50:
            insights.append(f"⚡ MODERATE SUCCESS: {success_rate}% win rate - decent pattern but use tight risk management")
        else:
            insights.append(f"⚠️ LOW SUCCESS RATE: {success_rate}% win rate - consider avoiding this setup type")

        # Risk-reward insight
        if average_rr >= 2.0:
            insights.append(f"💰 GOOD RR: Average {average_rr}R profit when successful")
        elif average_rr >= 1.5:
            insights.append(f"📊 MODERATE RR: Average {average_rr}R profit - acceptable for high win rate")
        else:
            insights.append(f"📉 LOW RR: Average {average_rr}R profit - may not be worth the risk")

        # Pattern-specific insights
        setup_types = [o.get('setup_type', 'unknown') for o in outcomes]
        most_common_setup = max(set(setup_types), key=setup_types.count) if setup_types else 'unknown'

        if most_common_setup != 'unknown':
            setup_count = setup_types.count(most_common_setup)
            insights.append(f"🎯 PATTERN: '{most_common_setup}' appears in {setup_count}/{len(outcomes)} recent trades")

        return " | ".join(insights)

    def store_analysis(self, analysis_data: Dict[str, Any], symbol: str = None, mode: str = "general", market: str = "general") -> str:
        """Store analysis results in memory and return document ID"""
        try:
            # Extract key information from analysis
            if isinstance(analysis_data, dict):
                # Create a summary of the analysis
                summary_parts = []

                if 'analysis' in analysis_data:
                    summary_parts.append(f"Analysis: {str(analysis_data['analysis'])[:500]}")

                if 'trading_signals' in analysis_data:
                    signals = analysis_data['trading_signals']
                    if isinstance(signals, dict):
                        summary_parts.append(f"Trading Signals: {str(signals)[:300]}")

                if 'chart_patterns' in analysis_data:
                    patterns = analysis_data['chart_patterns']
                    if patterns:
                        summary_parts.append(f"Chart Patterns: {', '.join(patterns[:5])}")

                summary = " | ".join(summary_parts)

                if summary:
                    doc_id = self.ingest_analysis_summary(
                        summary=summary,
                        symbol=symbol or analysis_data.get('detected_symbol', 'unknown'),
                        mode=mode,
                        market=market
                    )
                    return doc_id if doc_id else ""

            return ""

        except Exception as e:
            logger.error(f"Failed to store analysis: {e}")
            return ""

# Backward compatibility wrapper
class EnhancedTradingRAGSystem:
    """Wrapper for backward compatibility - now uses ChromaDB exclusively"""

    def __init__(self, db_path: str = "data/chromadb"):
        if CHROMADB_AVAILABLE:
            self.rag_system = ChromaDBTradingRAGSystem(db_path)
        else:
            raise ImportError("ChromaDB required for best-in-class trading analysis. Install with: pip install chromadb")
    
    def ingest_market_data(self, market_data: Dict[str, Any], symbol: str):
        if hasattr(self.rag_system, 'ingest_market_data'):
            return self.rag_system.ingest_market_data(market_data, symbol)
    
    def ingest_news_data(self, news_data: Dict[str, Any], symbol: str):
        if hasattr(self.rag_system, 'ingest_news_data'):
            return self.rag_system.ingest_news_data(news_data, symbol)
    
    def get_relevant_context(self, query: str, n_results: int = 5) -> str:
        return self.rag_system.get_relevant_context(query, n_results)
    
    def get_contextual_memory(self, symbol: str, mode: str = "general", market: str = "general", query: str = "") -> str:
        if hasattr(self.rag_system, 'get_contextual_memory'):
            return self.rag_system.get_contextual_memory(symbol, mode, market, query)
        else:
            return self.rag_system.get_relevant_context(f"{symbol} {mode} {query}")
