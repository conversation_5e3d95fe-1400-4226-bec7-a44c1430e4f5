"""Delta Exchange API integration for crypto data."""

import requests
import pandas as pd
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DeltaExchangeAPI:
    """Delta Exchange API client for crypto market data."""
    
    def __init__(self, api_key: str, secret_key: str):
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = "https://api.delta.exchange"
        self.session = requests.Session()
        
        # Your main trading symbols (corrected based on Delta Exchange API)
        self.main_symbols = {
            "BTCUSDT": "BTC/USDT",
            "ETHUSDT": "ETH/USDT",
            "SOLUSDT": "SOL/USDT"
        }
        
    def _make_request(self, endpoint: str, params: Dict = None) -> Dict:
        """Make API request to Delta Exchange."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Delta Exchange API error: {e}")
            return {"error": str(e)}
    
    def get_products(self) -> Dict:
        """Get all available products/symbols."""
        return self._make_request("/v2/products")

    def list_available_symbols(self) -> List[str]:
        """List all available trading symbols."""
        products = self.get_products()
        if "error" in products:
            return []

        symbols = []
        for product in products.get("result", []):
            symbol = product.get("symbol", "")
            if symbol:
                symbols.append(symbol)

        return symbols
    
    def get_ticker(self, symbol: str) -> Dict:
        """Get current ticker data for a symbol."""
        # Use the tickers endpoint with symbol filter
        params = {"symbols": symbol}
        response = self._make_request("/v2/tickers", params)

        if "error" in response:
            return response

        # Extract the ticker for our symbol
        tickers = response.get("result", [])
        for ticker in tickers:
            if ticker.get("symbol") == symbol:
                return {"result": ticker}

        return {"error": f"Ticker for symbol {symbol} not found"}
    
    def get_candles(self, symbol: str, resolution: str = "1h", 
                   start_time: Optional[datetime] = None, 
                   end_time: Optional[datetime] = None) -> Dict:
        """
        Get candlestick data for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSD')
            resolution: Timeframe ('1m', '5m', '15m', '1h', '4h', '1d')
            start_time: Start datetime
            end_time: End datetime
        """
        # Get product ID
        products = self.get_products()
        if "error" in products:
            return products
            
        product_id = None
        for product in products.get("result", []):
            if product.get("symbol") == symbol:
                product_id = product.get("id")
                break
        
        if not product_id:
            return {"error": f"Symbol {symbol} not found"}
        
        # Set default time range if not provided
        if not end_time:
            end_time = datetime.now()
        if not start_time:
            start_time = end_time - timedelta(days=30)  # Default 30 days
        
        params = {
            "resolution": resolution,
            "start": int(start_time.timestamp()),
            "end": int(end_time.timestamp())
        }
        
        return self._make_request(f"/v2/history/candles", params={
            **params,
            "symbol": symbol
        })
    
    def get_market_data(self, symbol: str, period: str = "30d", 
                       interval: str = "1h") -> Dict:
        """
        Get comprehensive market data for analysis.
        Compatible with existing tool calling interface.
        """
        try:
            # Convert period to datetime
            if period == "1d":
                days = 1
            elif period == "7d":
                days = 7
            elif period == "30d":
                days = 30
            elif period == "90d":
                days = 90
            else:
                days = 30  # default
            
            start_time = datetime.now() - timedelta(days=days)
            end_time = datetime.now()
            
            # Get candlestick data
            candles_data = self.get_candles(symbol, interval, start_time, end_time)
            
            if "error" in candles_data:
                return candles_data
            
            # Get current ticker
            ticker_data = self.get_ticker(symbol)
            
            # Process candles data into pandas DataFrame format
            candles = candles_data.get("result", [])
            if not candles:
                return {"error": "No candle data available"}
            
            # Convert to DataFrame-like structure
            df_data = []
            for candle in candles:
                df_data.append({
                    "timestamp": datetime.fromtimestamp(candle.get("time", 0)),
                    "open": float(candle.get("open", 0)),
                    "high": float(candle.get("high", 0)),
                    "low": float(candle.get("low", 0)),
                    "close": float(candle.get("close", 0)),
                    "volume": float(candle.get("volume", 0))
                })
            
            df = pd.DataFrame(df_data)
            df.set_index("timestamp", inplace=True)
            
            # Calculate basic technical indicators
            df["sma_20"] = df["close"].rolling(window=20).mean()
            df["sma_50"] = df["close"].rolling(window=50).mean()
            df["rsi"] = self._calculate_rsi(df["close"])
            
            # Current price info
            current_price = ticker_data.get("result", {}).get("close", 0)
            price_change_24h = ticker_data.get("result", {}).get("change_24h", 0)
            
            return {
                "symbol": symbol,
                "current_price": current_price,
                "price_change_24h": price_change_24h,
                "data": df.to_dict("records"),
                "summary": {
                    "total_candles": len(df),
                    "period": period,
                    "interval": interval,
                    "latest_close": df["close"].iloc[-1] if not df.empty else 0,
                    "highest": df["high"].max() if not df.empty else 0,
                    "lowest": df["low"].min() if not df.empty else 0,
                    "avg_volume": df["volume"].mean() if not df.empty else 0
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return {"error": f"Failed to get market data: {str(e)}"}
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def get_multiple_symbols_data(self, symbols: List[str], 
                                 period: str = "7d", 
                                 interval: str = "1h") -> Dict:
        """Get data for multiple symbols."""
        results = {}
        for symbol in symbols:
            results[symbol] = self.get_market_data(symbol, period, interval)
        return results


def create_delta_client(config_path: str = None) -> Optional[DeltaExchangeAPI]:
    """Create Delta Exchange client from config file."""
    try:
        # Auto-detect config path if not provided
        if config_path is None:
            possible_paths = [
                "Agent_Trading/config.json",  # From root directory
                "config.json",  # From Agent_Trading directory
                "Agent_Trading/Agent_Trading/config.json",  # Full path from root
                os.path.join(os.path.dirname(__file__), "..", "config.json")  # Relative to this file
            ]

            config_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    break

            if config_path is None:
                logger.error("Config file not found in any expected location")
                return None

        with open(config_path, 'r') as f:
            config = json.load(f)
        
        api_key = config.get("Delta_api_key")
        secret_key = config.get("Delta_secret_key")
        
        if not api_key or not secret_key:
            logger.error("Delta Exchange API credentials not found in config")
            return None
        
        return DeltaExchangeAPI(api_key, secret_key)
    
    except Exception as e:
        logger.error(f"Error creating Delta client: {e}")
        return None


# Main symbols you trade (corrected)
MAIN_CRYPTO_SYMBOLS = ["BTCUSDT", "ETHUSDT", "SOLUSDT"]


def get_crypto_market_data(symbol: str, period: str = "30d", 
                          interval: str = "1h", reason: str = "") -> Dict:
    """
    Tool function for crypto market data - compatible with existing tool system.
    """
    client = create_delta_client()
    if not client:
        return {"error": "Could not create Delta Exchange client"}
    
    return client.get_market_data(symbol, period, interval)


def get_multiple_crypto_data(symbols: List[str] = None, 
                           period: str = "7d", 
                           interval: str = "1h", 
                           reason: str = "") -> Dict:
    """
    Tool function for multiple crypto symbols.
    """
    if not symbols:
        symbols = MAIN_CRYPTO_SYMBOLS
    
    client = create_delta_client()
    if not client:
        return {"error": "Could not create Delta Exchange client"}
    
    return client.get_multiple_symbols_data(symbols, period, interval)
