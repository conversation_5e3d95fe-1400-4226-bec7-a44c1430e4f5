#!/usr/bin/env python3
"""
Debug the quota and structure issues.
"""

import os
import sys
import json
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from helpers.utils import load_google_api_key
import google.generativeai as genai

def test_api_quota():
    """Test if we can make a simple API call."""
    print("=== TESTING API QUOTA ===")
    
    # Load API key
    google_api_key = load_google_api_key()
    if not google_api_key:
        print("❌ No Google API key found")
        return False
    
    # Configure API
    genai.configure(api_key=google_api_key)
    
    try:
        # Try a simple text generation
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        response = model.generate_content("Say 'API working' if you can respond.")
        
        if response and response.text:
            print(f"✅ API working: {response.text.strip()}")
            return True
        else:
            print("❌ API returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ API call failed: {e}")
        if "quota" in str(e).lower():
            print("🚫 QUOTA EXHAUSTED - This is why the workflow fails!")
            print("💡 Solution: Wait for quota reset or upgrade to paid plan")
        return False

def check_workflow_structure():
    """Check what structure the workflow actually returns when it fails."""
    print("\n=== CHECKING WORKFLOW STRUCTURE ===")
    
    from helpers.complete_langgraph_workflow import CompleteLangGraphTradingWorkflow
    from PIL import Image
    
    # Load API key
    google_api_key = load_google_api_key()
    if not google_api_key:
        print("❌ No Google API key found")
        return
    
    # Initialize workflow
    workflow = CompleteLangGraphTradingWorkflow(google_api_key)
    
    # Try with a simple test image (will fail due to quota)
    try:
        # Create a simple test image
        test_image = Image.new('RGB', (100, 100), color='white')
        
        # This will fail due to quota, but let's see the structure
        result = workflow.analyze_trading_chart([test_image], analysis_mode="scalp")
        
        print("Workflow result structure:")
        print(json.dumps(result, indent=2, default=str))
        
        # Check what keys are present
        print(f"\nResult keys: {list(result.keys())}")
        
        # Check if analysis_notes is present
        if 'analysis_notes' in result:
            print(f"✅ analysis_notes found: {type(result['analysis_notes'])}")
        else:
            print("❌ analysis_notes missing - this is why GUI shows the error")
            
        # Check if it's a success or failure
        if result.get('success'):
            print("✅ Workflow succeeded")
        else:
            print(f"❌ Workflow failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")

def show_expected_structure():
    """Show what structure the GUI expects vs what we get on failure."""
    print("\n=== EXPECTED VS ACTUAL STRUCTURE ===")
    
    print("✅ EXPECTED STRUCTURE (when API works):")
    expected = {
        "success": True,
        "analysis_notes": "Detailed trading analysis with Entry/SL/TP levels...",
        "trading_signals": {
            "entry_levels": ["55,600", "55,650"],
            "stop_loss": "55,850",
            "take_profit": ["55,300", "55,000"],
            "confidence": 8
        },
        "detected_symbol": "BTC/USDT",
        "market_type": "crypto"
    }
    print(json.dumps(expected, indent=2))
    
    print("\n❌ ACTUAL STRUCTURE (when quota exhausted):")
    actual = {
        "success": False,
        "error": "429 You exceeded your current quota...",
        "workflow_status": "failed"
    }
    print(json.dumps(actual, indent=2))
    
    print("\n💡 SOLUTION:")
    print("1. Wait for API quota to reset (resets daily)")
    print("2. Or upgrade to paid Google AI Studio plan")
    print("3. The workflow code is correct - it's just hitting quota limits")

if __name__ == "__main__":
    print("🔍 DEBUGGING QUOTA AND STRUCTURE ISSUES")
    print("=" * 50)
    
    # Test API quota
    api_working = test_api_quota()
    
    # Check workflow structure
    check_workflow_structure()
    
    # Show expected vs actual
    show_expected_structure()
    
    print("\n" + "=" * 50)
    if not api_working:
        print("🚫 MAIN ISSUE: API QUOTA EXHAUSTED")
        print("📅 Quota resets daily - try again tomorrow")
        print("💰 Or upgrade to paid plan for higher limits")
    else:
        print("✅ API working - workflow should function normally")
