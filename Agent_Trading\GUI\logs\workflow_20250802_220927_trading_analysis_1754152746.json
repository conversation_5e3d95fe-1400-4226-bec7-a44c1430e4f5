{"workflow_id": "trading_analysis_1754152746", "start_time": "2025-08-02T22:09:06.922846", "context": {"analysis_mode": "positional", "market_specialization": "Crypto", "chart_count": 2}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-02T22:09:06.922846", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-02T22:09:11.114160", "execution_time": 4.19131326675415, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'support_levels', 'resistance_levels']..."}, {"step_number": 3, "step_name": "Tool Execution", "step_type": "tool_execution", "timestamp": "2025-08-02T22:09:11.114160", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: crypto, Symbol: BTC/USD"}, {"step_number": 4, "step_name": "Tool Execution", "step_type": "tool_execution", "timestamp": "2025-08-02T22:09:14.323087", "execution_time": 3.2038397789001465, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 5, "step_name": "Tool Summarization", "step_type": "llm_call", "timestamp": "2025-08-02T22:09:14.323087", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 6, "step_name": "Tool Summarization", "step_type": "llm_call", "timestamp": "2025-08-02T22:09:25.880988", "execution_time": 11.55790090560913, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count']"}, {"step_number": 7, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-02T22:09:27.986670", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG context"}, {"step_number": 8, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-02T22:09:27.988162", "execution_time": 0.0014922618865966797, "status": "error", "error": "'CompleteLangGraphTradingWorkflow' object has no attribute 'logger'"}], "llm_calls": [], "summary": {"total_steps": 8, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 21.0663104057312}, "end_time": "2025-08-02T22:09:27.989157", "status": "error", "error": "'CompleteLangGraphTradingWorkflow' object has no attribute 'logger'"}