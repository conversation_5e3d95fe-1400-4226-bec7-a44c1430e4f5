#!/usr/bin/env python3
"""
Debug script to test vision API directly
"""

import sys
import os
sys.path.append('.')

import google.generativeai as genai
from PIL import Image
import json
from helpers.utils import load_google_api_key

def test_vision_api():
    """Test vision API with a simple chart"""
    try:
        # Load API key
        api_key = load_google_api_key()
        if not api_key:
            print("❌ No Google API key found")
            return False
            
        genai.configure(api_key=api_key)
        
        # Create model
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        
        # Simple vision prompt
        vision_prompt = """You are a chart analysis expert. Analyze this trading chart and return ONLY valid JSON format.

CRITICAL: Your response must be ONLY valid JSON, no other text.

Required JSON format:
{
  "detected_symbol": "symbol name (e.g., BTC/USDT, NIFTY, BANKNIFTY)",
  "market_type": "crypto/indian/us",
  "timeframe": "1m/5m/15m/1h/4h/1d",
  "support_levels": [price_numbers],
  "resistance_levels": [price_numbers]
}

If you cannot analyze the chart, return:
{
  "detected_symbol": "UNKNOWN",
  "market_type": "unknown",
  "timeframe": "unknown",
  "support_levels": [],
  "resistance_levels": []
}

Return ONLY the JSON, no explanations."""

        # Try to load a test image
        import glob
        test_image_paths = glob.glob("memory/images/*.png") + glob.glob("memory/images/*.jpg")
        
        test_image = None
        for path in test_image_paths:
            if os.path.exists(path):
                try:
                    test_image = Image.open(path)
                    print(f"✅ Loaded test image: {path}")
                    print(f"   Image size: {test_image.size}")
                    print(f"   Image mode: {test_image.mode}")
                    break
                except Exception as e:
                    print(f"❌ Failed to load {path}: {e}")
                    continue
        
        if not test_image:
            print("❌ No test image found. Please upload a chart to memory/images/")
            return False
        
        # Resize image if needed (same as workflow)
        if test_image.size[0] > 800 or test_image.size[1] > 800:
            test_image.thumbnail((800, 800), Image.Resampling.LANCZOS)
            print(f"   Resized to: {test_image.size}")
        
        # Convert to RGB if needed
        if test_image.mode != 'RGB':
            test_image = test_image.convert('RGB')
            print(f"   Converted to RGB mode")
        
        print("\n🚀 Making vision API call...")
        
        # Make API call
        response = model.generate_content(
            [vision_prompt, test_image],
            generation_config={
                "temperature": 0.1,
                "max_output_tokens": 1000,
            }
        )
        
        print("✅ API call successful!")
        print(f"\n📝 Raw Response:")
        print("=" * 50)
        print(response.text if response and response.text else "NO RESPONSE TEXT")
        print("=" * 50)
        
        # Try to parse JSON
        if response and response.text:
            try:
                result = json.loads(response.text.strip())
                print(f"\n✅ JSON Parsing Successful:")
                print(json.dumps(result, indent=2))
                return True
            except json.JSONDecodeError as e:
                print(f"\n❌ JSON Parsing Failed: {e}")
                
                # Try to extract from markdown
                import re
                json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
                matches = re.findall(json_pattern, response.text, re.DOTALL)
                
                if matches:
                    print(f"\n🔍 Found JSON in markdown blocks:")
                    for i, match in enumerate(matches):
                        print(f"Match {i+1}:")
                        print(match)
                        try:
                            result = json.loads(match)
                            print(f"✅ Successfully parsed match {i+1}:")
                            print(json.dumps(result, indent=2))
                            return True
                        except json.JSONDecodeError:
                            print(f"❌ Failed to parse match {i+1}")
                            continue
                
                return False
        else:
            print("❌ No response text received")
            return False
            
    except Exception as e:
        print(f"❌ Vision API test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔍 VISION API DEBUG TEST")
    print("=" * 50)
    
    success = test_vision_api()
    
    print(f"\n📊 RESULT: {'✅ SUCCESS' if success else '❌ FAILED'}")
