#!/usr/bin/env python3
"""
YFinance Integration for LangGraph Trading Workflow
Comprehensive market data, technical indicators, and financial metrics.
"""

import yfinance as yf
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class YFinanceDataProvider:
    """Enhanced YFinance data provider for trading analysis."""
    
    def __init__(self):
        """Initialize the YFinance data provider."""
        self.symbol_mapping = {
            # Crypto symbols
            "BTC": "BTC-USD",
            "BTC/USDT": "BTC-USD", 
            "BTCUSDT": "BTC-USD",
            "ETH": "ETH-USD",
            "ETH/USDT": "ETH-USD",
            "ETHUSDT": "ETH-USD",
            "SOL": "SOL-USD",
            "SOL/USDT": "SOL-USD",
            "SOLUSDT": "SOL-USD",
            
            # Indian market symbols
            "NIFTY": "^NSEI",
            "NIFTY50": "^NSEI",
            "BANKNIFTY": "^NSEBANK",
            "BANK NIFTY": "^NSEBANK",
            
            # US market symbols
            "SPY": "SPY",
            "QQQ": "QQQ",
            "AAPL": "AAPL",
            "TSLA": "TSLA",
            "NVDA": "NVDA"
        }
    
    def normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol for YFinance."""
        if not symbol:
            return None
            
        symbol_upper = symbol.upper().strip()
        
        # Check direct mapping first
        if symbol_upper in self.symbol_mapping:
            return self.symbol_mapping[symbol_upper]
        
        # For crypto pairs, try to convert to USD pairs
        if "/" in symbol_upper:
            base, quote = symbol_upper.split("/")
            if quote in ["USDT", "USD"]:
                return f"{base}-USD"
        
        # Return as-is if no mapping found
        return symbol_upper
    
    def get_market_data(self, symbol: str, period: str = "1mo", interval: str = "1d") -> Dict[str, Any]:
        """
        Get comprehensive market data for a symbol.
        
        Args:
            symbol: Trading symbol
            period: Data period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            interval: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
        
        Returns:
            Comprehensive market data dictionary
        """
        try:
            yf_symbol = self.normalize_symbol(symbol)
            if not yf_symbol:
                return {"error": "Invalid symbol", "success": False}
            
            logger.info(f"📊 Fetching YFinance data for {yf_symbol}")
            
            # Get ticker object
            ticker = yf.Ticker(yf_symbol)
            
            # Get historical data
            hist_data = ticker.history(period=period, interval=interval)
            
            if hist_data.empty:
                return {"error": "No data available", "success": False}
            
            # Get current info
            info = ticker.info
            
            # Calculate technical indicators
            technical_indicators = self._calculate_technical_indicators(hist_data)
            
            # Get recent price data
            latest = hist_data.iloc[-1]
            previous = hist_data.iloc[-2] if len(hist_data) > 1 else latest
            
            price_change = latest['Close'] - previous['Close']
            price_change_pct = (price_change / previous['Close']) * 100
            
            result = {
                "success": True,
                "symbol": symbol,
                "yf_symbol": yf_symbol,
                "timestamp": datetime.now().isoformat(),
                
                # Current price data
                "current_price": float(latest['Close']),
                "previous_close": float(previous['Close']),
                "price_change": float(price_change),
                "price_change_percent": float(price_change_pct),
                "volume": int(latest['Volume']) if not pd.isna(latest['Volume']) else 0,
                
                # OHLC data
                "open": float(latest['Open']),
                "high": float(latest['High']),
                "low": float(latest['Low']),
                "close": float(latest['Close']),
                
                # Technical indicators
                "technical_indicators": technical_indicators,
                
                # Historical data summary
                "historical_summary": {
                    "period": period,
                    "interval": interval,
                    "data_points": len(hist_data),
                    "date_range": {
                        "start": hist_data.index[0].isoformat(),
                        "end": hist_data.index[-1].isoformat()
                    }
                },
                
                # Company/Asset info
                "info": {
                    "name": info.get('longName', info.get('shortName', symbol)),
                    "sector": info.get('sector'),
                    "industry": info.get('industry'),
                    "market_cap": info.get('marketCap'),
                    "currency": info.get('currency', 'USD')
                }
            }
            
            logger.info(f"✅ YFinance data retrieved for {symbol}: ${result['current_price']:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"❌ YFinance data error for {symbol}: {e}")
            return {
                "error": str(e),
                "success": False,
                "symbol": symbol
            }
    
    def _calculate_technical_indicators(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate technical indicators from price data."""
        try:
            indicators = {}
            
            # Simple Moving Averages
            indicators['sma_20'] = float(data['Close'].rolling(window=20).mean().iloc[-1]) if len(data) >= 20 else None
            indicators['sma_50'] = float(data['Close'].rolling(window=50).mean().iloc[-1]) if len(data) >= 50 else None
            indicators['sma_200'] = float(data['Close'].rolling(window=200).mean().iloc[-1]) if len(data) >= 200 else None
            
            # Exponential Moving Averages
            indicators['ema_12'] = float(data['Close'].ewm(span=12).mean().iloc[-1]) if len(data) >= 12 else None
            indicators['ema_26'] = float(data['Close'].ewm(span=26).mean().iloc[-1]) if len(data) >= 26 else None
            
            # RSI (Relative Strength Index)
            if len(data) >= 14:
                delta = data['Close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                indicators['rsi'] = float(100 - (100 / (1 + rs.iloc[-1])))
            
            # MACD
            if len(data) >= 26:
                ema_12 = data['Close'].ewm(span=12).mean()
                ema_26 = data['Close'].ewm(span=26).mean()
                macd_line = ema_12 - ema_26
                signal_line = macd_line.ewm(span=9).mean()
                indicators['macd'] = float(macd_line.iloc[-1])
                indicators['macd_signal'] = float(signal_line.iloc[-1])
                indicators['macd_histogram'] = float(macd_line.iloc[-1] - signal_line.iloc[-1])
            
            # Bollinger Bands
            if len(data) >= 20:
                sma_20 = data['Close'].rolling(window=20).mean()
                std_20 = data['Close'].rolling(window=20).std()
                indicators['bb_upper'] = float(sma_20.iloc[-1] + (std_20.iloc[-1] * 2))
                indicators['bb_middle'] = float(sma_20.iloc[-1])
                indicators['bb_lower'] = float(sma_20.iloc[-1] - (std_20.iloc[-1] * 2))
            
            # Volume indicators
            if len(data) >= 20:
                indicators['volume_sma_20'] = float(data['Volume'].rolling(window=20).mean().iloc[-1])
                indicators['volume_ratio'] = float(data['Volume'].iloc[-1] / indicators['volume_sma_20'])
            
            # Price levels
            indicators['high_52w'] = float(data['High'].max()) if len(data) > 0 else None
            indicators['low_52w'] = float(data['Low'].min()) if len(data) > 0 else None
            
            # Volatility
            if len(data) >= 20:
                returns = data['Close'].pct_change()
                indicators['volatility_20d'] = float(returns.rolling(window=20).std() * np.sqrt(252))
            
            return indicators
            
        except Exception as e:
            logger.error(f"❌ Technical indicators calculation error: {e}")
            return {}
    
    def get_multiple_symbols(self, symbols: List[str], period: str = "1mo") -> Dict[str, Any]:
        """Get data for multiple symbols."""
        results = {}
        
        for symbol in symbols:
            results[symbol] = self.get_market_data(symbol, period)
        
        return {
            "success": True,
            "symbols": symbols,
            "data": results,
            "timestamp": datetime.now().isoformat()
        }
    
    def get_market_summary(self, market_type: str = "crypto") -> Dict[str, Any]:
        """Get market summary for specific market type."""
        try:
            if market_type == "crypto":
                symbols = ["BTC-USD", "ETH-USD", "SOL-USD"]
            elif market_type == "indian":
                symbols = ["^NSEI", "^NSEBANK"]
            elif market_type == "us":
                symbols = ["^GSPC", "^IXIC", "^DJI"]
            else:
                symbols = ["^GSPC"]  # Default to S&P 500
            
            summary_data = {}
            for symbol in symbols:
                data = self.get_market_data(symbol, period="1d")
                if data.get("success"):
                    summary_data[symbol] = {
                        "name": data["info"]["name"],
                        "price": data["current_price"],
                        "change": data["price_change"],
                        "change_percent": data["price_change_percent"]
                    }
            
            return {
                "success": True,
                "market_type": market_type,
                "summary": summary_data,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Market summary error: {e}")
            return {"error": str(e), "success": False}

# Global instance
yfinance_provider = YFinanceDataProvider()

def get_yfinance_data(symbol: str, period: str = "1mo", interval: str = "1d") -> Dict[str, Any]:
    """Get YFinance data for a symbol."""
    return yfinance_provider.get_market_data(symbol, period, interval)

def get_market_summary(market_type: str = "crypto") -> Dict[str, Any]:
    """Get market summary."""
    return yfinance_provider.get_market_summary(market_type)

def get_comprehensive_news(symbol: str, max_news: int = 10) -> Dict[str, Any]:
    """Get comprehensive news for a symbol using YFinance news API."""
    try:
        normalized_symbol = yfinance_provider.normalize_symbol(symbol)
        if not normalized_symbol:
            return {"error": f"Could not normalize symbol: {symbol}"}

        ticker = yf.Ticker(normalized_symbol)

        # Get news from YFinance
        news = ticker.news

        if not news:
            return {
                "symbol": symbol,
                "news_count": 0,
                "news": [],
                "data_source": "yfinance",
                "timestamp": datetime.now().isoformat(),
                "message": "No news available"
            }

        # Process news data
        processed_news = []
        for article in news[:max_news]:
            processed_article = {
                "title": article.get('title', 'No title'),
                "summary": article.get('summary', 'No summary'),
                "link": article.get('link', ''),
                "publisher": article.get('publisher', 'Unknown'),
                "published_time": article.get('providerPublishTime', 0),
                "type": article.get('type', 'news')
            }

            # Convert timestamp to readable format
            if processed_article['published_time']:
                try:
                    processed_article['published_date'] = datetime.fromtimestamp(
                        processed_article['published_time']
                    ).strftime('%Y-%m-%d %H:%M:%S')
                except:
                    processed_article['published_date'] = 'Unknown'

            processed_news.append(processed_article)

        return {
            "symbol": symbol,
            "normalized_symbol": normalized_symbol,
            "news_count": len(processed_news),
            "news": processed_news,
            "data_source": "yfinance",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting news for {symbol}: {e}")
        return {"error": str(e), "symbol": symbol}

def get_indian_market_context(symbol: str) -> Dict[str, Any]:
    """Get enhanced Indian market context using YFinance."""
    try:
        # For Indian indices, get constituent data
        if symbol.upper() in ["NIFTY", "NIFTY50", "BANKNIFTY", "BANK NIFTY"]:
            normalized_symbol = yfinance_provider.normalize_symbol(symbol)

            # Get main index data
            main_data = get_yfinance_data(symbol)

            # Get news for the index
            news_data = get_comprehensive_news(symbol)

            # Get related market data
            related_symbols = []
            if "NSEI" in normalized_symbol:  # Nifty 50
                related_symbols = ["^NSEBANK", "RELIANCE.NS", "TCS.NS", "INFY.NS"]
            elif "NSEBANK" in normalized_symbol:  # Bank Nifty
                related_symbols = ["HDFCBANK.NS", "ICICIBANK.NS", "SBIN.NS", "KOTAKBANK.NS"]

            related_data = {}
            for rel_symbol in related_symbols:
                try:
                    ticker = yf.Ticker(rel_symbol)
                    hist = ticker.history(period="5d")
                    if not hist.empty:
                        latest = hist.iloc[-1]
                        previous = hist.iloc[-2] if len(hist) > 1 else latest
                        related_data[rel_symbol] = {
                            "price": float(latest['Close']),
                            "change_percent": float((latest['Close'] - previous['Close']) / previous['Close'] * 100)
                        }
                except:
                    continue

            return {
                "symbol": symbol,
                "market_data": main_data,
                "news": news_data,
                "related_stocks": related_data,
                "market_type": "indian",
                "data_source": "yfinance_enhanced",
                "timestamp": datetime.now().isoformat()
            }

        else:
            # For individual stocks
            return {
                "symbol": symbol,
                "market_data": get_yfinance_data(symbol),
                "news": get_comprehensive_news(symbol),
                "market_type": "indian",
                "data_source": "yfinance",
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error getting Indian market context for {symbol}: {e}")
        return {"error": str(e), "symbol": symbol}
