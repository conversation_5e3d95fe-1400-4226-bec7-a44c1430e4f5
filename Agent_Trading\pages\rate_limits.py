#!/usr/bin/env python3
"""
🚦 RATE LIMITS PAGE
Dedicated page for comprehensive Gemini API rate limit monitoring and management.
"""

import streamlit as st
import sys
import os

# Add parent directory to path for imports
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from Agent_Trading.GUI.rate_limit_dashboard import display_rate_limit_dashboard

def main():
    """Main rate limits page."""
    st.set_page_config(
        page_title="🚦 Gemini API Rate Limits",
        page_icon="🚦",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Custom CSS for professional styling
    st.markdown("""
    <style>
    .stApp {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #f9fafb !important;
        color: #111827 !important;
    }
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: center;
    }
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #667eea;
        margin-bottom: 1rem;
    }
    .usage-bar {
        background: #e5e7eb;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
        margin: 0.5rem 0;
    }
    .usage-fill {
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
    }
    .usage-low { background: #10b981; }
    .usage-medium { background: #f59e0b; }
    .usage-high { background: #ef4444; }
    </style>
    """, unsafe_allow_html=True)
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🚦 Gemini API Rate Limits Dashboard</h1>
        <p>Real-time monitoring of your Gemini API usage based on official Google documentation</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Display the comprehensive rate limit dashboard
    display_rate_limit_dashboard()
    
    # Additional information section
    st.markdown("---")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 📚 Understanding Rate Limits
        
        **RPM (Requests Per Minute)**: Maximum number of API calls you can make per minute
        
        **TPM (Tokens Per Minute)**: Maximum number of tokens (input + output) you can process per minute
        
        **RPD (Requests Per Day)**: Maximum number of API calls you can make per day
        
        **Rate limits reset**:
        - RPM: Every 60 seconds
        - TPM: Every 60 seconds  
        - RPD: At midnight Pacific time
        """)
    
    with col2:
        st.markdown("""
        ### 🎯 Best Practices
        
        **Optimize Your Usage**:
        - Use appropriate models for each task
        - Implement request batching
        - Add exponential backoff for retries
        - Monitor usage in real-time
        
        **Upgrade Your Tier**:
        - Link billing account for Tier 1
        - Spend $250+ for Tier 2 access
        - Spend $1,000+ for Tier 3 access
        """)
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #6b7280; font-size: 0.9em;'>
        📊 Rate limits are based on official Google Gemini API documentation<br>
        🔄 Data updates in real-time as you use the API<br>
        💡 Upgrade your tier for higher limits and better performance
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
