---
applyTo: '*AI Trading Analysis Application
- User has built an AI chart analysis system and wants to enhance it into a comprehensive AI trading analysis application using modern technologies (LLMs, tool calling, RAG, memories, LangGraph) for market analysis, entry/exit signals, and profitable trading strategies.
- Core goal of visual chart analysis that can be enhanced over time, with Delta crypto integration, and memory system to store image analyses and feedback like Augment AI s memory system.
- User expects trading analysis to include: structured tables with Entry/SL/TP levels and reasons, multiple tools execution (news, FII/DII data), and chart annotations marking trading levels on images, not just text-based analysis.
- User wants complete LangGraph migration with removal of old tool-calling system after completion.

# Trading Focus & Data Sources
- Users trading focus is Bank Nifty, Nifty 50, and crypto pairs (GOLD/USDT, BTC/USDT, SOL/USDT, ETH/USDT) using Delta Exchange India API and yfinance. User primarily trades BTC/USDT, ETH/USDT, SOL/USDT on Delta Exchange and Nifty 50/Bank Nifty in Indian markets.
- User has Dhan API configured in config.json for Indian market data alongside yfinance and Delta Exchange APIs.
- User confirmed preferred data source strategy: Dhan API for Indian market real data/volumes, Delta Exchange for crypto real data/volumes, yfinance for news/analysis across all markets.
- User needs automatic Bank Nifty/Nifty 50 futures symbol detection from chart uploads, better news extraction for Indian markets, and API quota management with fallback mechanisms for trading analysis.
- User prefers DuckDuckGo for news integration.

# LLM & Tool Usage
- User uses google_api_key (not gemini_api_key) for Gemini API.
- User prefers LLM to dynamically decide ChromaDB queries as a tool rather than predefined queries.
- User wants market-specific tool selection (crypto tools for crypto analysis, not Indian market tools).
- User wants tool data sent to Gemini 2.5 Flash/2.0 for summarization first, then summarized data used for main analysis, with parallel execution and tool data summaries displayed in dashboard.
- User expects to see the main AI analysis (using intelligent prompts like indian_market_prompt, crypto_prompt for positional/scalp trading) displayed in the dashboard after tool execution, but its not currently showing.
- User wants parallel tool execution for performance.
- User wants strict JSON output schemas in prompts to prevent response format variations.
- User wants removing redundant final prompts in favor of centralized prompts.

# Project Structure & Dashboard
- User prefers clean project structure with prompts organized in dedicated prompt files rather than scattered across utility files, and wants modular architecture with proper separation of concerns.
- User expects clean dashboard without duplicate analysis information.

# Parallel Tool Execution Flow
- User expects parallel tool execution flow: upload chart → select market/mode → parallel tools (market data, news, FII/DII) → Gemini Flash summarizes each → summaries sent to main Gemini Pro + ingested into RAG → query RAG for past data → final analysis with chart + summaries + RAG context.*'
---
Provide project context and coding guidelines that AI should follow when generating code, answering questions, or reviewing changes.