"""
API Quota Management System
Handles Gemini API quota limits with intelligent fallback mechanisms
"""

import json
import os
from datetime import datetime, date
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class QuotaManager:
    """Manages API quota usage and provides fallback summarization"""
    
    def __init__(self, quota_file: str = "quota_usage.json"):
        self.quota_file = quota_file
        self.daily_quota = 50  # Gemini free tier limit
        self.load_quota_data()
    
    def load_quota_data(self):
        """Load quota usage data from file"""
        try:
            if os.path.exists(self.quota_file):
                with open(self.quota_file, 'r') as f:
                    data = json.load(f)
                    self.used_requests = data.get('used_requests', 0)
                    self.last_reset = datetime.strptime(data.get('last_reset', str(date.today())), '%Y-%m-%d').date()
            else:
                self.used_requests = 0
                self.last_reset = date.today()
        except Exception as e:
            logger.warning(f"Failed to load quota data: {e}")
            self.used_requests = 0
            self.last_reset = date.today()
    
    def save_quota_data(self):
        """Save quota usage data to file"""
        try:
            data = {
                'used_requests': self.used_requests,
                'last_reset': str(self.last_reset)
            }
            with open(self.quota_file, 'w') as f:
                json.dump(data, f)
        except Exception as e:
            logger.warning(f"Failed to save quota data: {e}")
    
    def can_make_request(self) -> bool:
        """Check if we can make an API request"""
        # Reset quota if it's a new day
        if self.last_reset < date.today():
            self.reset_quota()
        
        return self.used_requests < self.daily_quota
    
    def reset_quota(self):
        """Reset daily quota"""
        self.used_requests = 0
        self.last_reset = date.today()
        self.save_quota_data()
        logger.info("Daily quota reset")
    
    def increment_usage(self):
        """Increment API usage counter"""
        self.used_requests += 1
        self.save_quota_data()
        logger.info(f"API usage: {self.used_requests}/{self.daily_quota}")
    
    def get_quota_status(self) -> Dict[str, Any]:
        """Get current quota status"""
        return {
            'used_requests': self.used_requests,
            'daily_quota': self.daily_quota,
            'remaining': self.daily_quota - self.used_requests,
            'can_make_request': self.can_make_request(),
            'last_reset': str(self.last_reset),
            'reset_time': str(self.last_reset)  # Added for compatibility
        }
    
    def fallback_summarize(self, tool_name: str, data: Any) -> str:
        """Generate fallback summary without API calls"""
        try:
            if tool_name == "get_smart_market_data":
                return self._summarize_market_data(data)
            elif tool_name == "get_comprehensive_market_news":
                return self._summarize_news_data(data)
            elif tool_name == "get_technical_analysis":
                return self._summarize_technical_data(data)
            else:
                return self._generic_summary(tool_name, data)
        except Exception as e:
            logger.error(f"Fallback summarization failed: {e}")
            return f"📊 {tool_name.replace('_', ' ').title()}: Data retrieved successfully (Summary unavailable due to quota limits)"
    
    def _summarize_market_data(self, data: Dict) -> str:
        """Fallback summary for market data"""
        try:
            if isinstance(data, dict):
                price = data.get('current_price', data.get('price', 'N/A'))
                change = data.get('price_change_24h', data.get('change', 'N/A'))
                volume = data.get('volume', 'N/A')
                
                return f"""📊 Market Data Summary:
• Current Price: {price}
• 24h Change: {change}%
• Volume: {volume}
• Status: Live market data retrieved"""
            else:
                return "📊 Market Data: Current market information retrieved successfully"
        except:
            return "📊 Market Data: Price and volume data available"
    
    def _summarize_news_data(self, data: Dict) -> str:
        """Fallback summary for news data"""
        try:
            if isinstance(data, dict) and 'news' in data:
                news_count = len(data['news'])
                return f"""📰 News Summary:
• {news_count} recent articles found
• Sources: Multiple financial news outlets
• Coverage: Market updates and analysis
• Status: Latest news data retrieved"""
            else:
                return "📰 News: Latest market news and analysis retrieved"
        except:
            return "📰 News: Market news and updates available"
    
    def _summarize_technical_data(self, data: Dict) -> str:
        """Fallback summary for technical analysis"""
        try:
            if isinstance(data, dict):
                indicators = data.get('technical_indicators', {})
                rsi = indicators.get('rsi', 'N/A')
                trend = data.get('trend', 'N/A')
                
                return f"""📈 Technical Analysis:
• RSI: {rsi}
• Trend: {trend}
• Support/Resistance levels identified
• Status: Technical indicators calculated"""
            else:
                return "📈 Technical Analysis: Indicators and levels calculated"
        except:
            return "📈 Technical Analysis: Market indicators available"
    
    def _generic_summary(self, tool_name: str, data: Any) -> str:
        """Generic fallback summary"""
        clean_name = tool_name.replace('_', ' ').title()
        return f"🔧 {clean_name}: Analysis completed successfully (Detailed summary unavailable due to API limits)"

# Global quota manager instance
quota_manager = QuotaManager()
