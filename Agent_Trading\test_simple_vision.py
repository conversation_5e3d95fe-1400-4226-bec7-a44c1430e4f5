#!/usr/bin/env python3
"""
Simple test to isolate the vision API issue
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_simple_vision():
    """Test basic vision API functionality"""
    try:
        import google.generativeai as genai
        from PIL import Image
        
        # Load config
        config_path = project_root / "config.json"
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Configure API
        genai.configure(api_key=config['google_api_key'])
        
        # Create model with exact same config as main workflow
        model = genai.GenerativeModel(model_name="gemini-2.0-flash-exp")

        # Use minimal generation config to reduce metadata
        generation_config = {
            "temperature": 0.1,
            "max_output_tokens": 1000,  # Limit output to prevent large responses
        }
        
        # Load test image
        image_path = project_root / "memory" / "images" / "0ca45d52c38eda9f9249d88c3084414c.png"
        if not image_path.exists():
            logger.error(f"Test image not found: {image_path}")
            return False
            
        # Load and optimize image
        img = Image.open(image_path)
        logger.info(f"Original image size: {img.size}")

        # Convert to RGB if needed
        if img.mode != 'RGB':
            img = img.convert('RGB')

        # Resize to smaller dimensions to reduce metadata size
        max_size = 800  # Reduced from 1024
        if img.width > max_size or img.height > max_size:
            img.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            logger.info(f"Resized image to: {img.size}")

        # Save to bytes with compression to reduce size
        import io
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='JPEG', quality=85, optimize=True)
        img_bytes.seek(0)

        # Reload compressed image
        img = Image.open(img_bytes)
        logger.info(f"Compressed image size: {img.size}")
        
        # Simple prompt
        prompt = """
        Analyze this trading chart and identify:
        1. The trading symbol
        2. The timeframe
        3. Current price level
        
        Respond in JSON format:
        {
            "symbol": "detected symbol",
            "timeframe": "detected timeframe", 
            "price": "current price level"
        }
        """
        
        # Make API call with same config as main workflow
        logger.info("Making vision API call...")
        response = model.generate_content(
            [prompt, img],
            generation_config=generation_config
        )
        
        if response and response.text:
            logger.info(f"Response received: {len(response.text)} characters")
            logger.info(f"Response: {response.text}")
            return True
        else:
            logger.error("No response received")
            return False
            
    except Exception as e:
        logger.error(f"Vision test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== SIMPLE VISION API TEST ===")
    success = test_simple_vision()
    print(f"Test result: {'SUCCESS' if success else 'FAILED'}")
