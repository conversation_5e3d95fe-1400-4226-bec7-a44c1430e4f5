#!/usr/bin/env python3
"""Quick test to verify prompt safety filter fixes."""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'Agent_Trading'))

from helpers.clean_prompts import create_main_prompt

def test_prompt_generation():
    """Test that prompts are generated correctly with sophisticated language."""
    
    print("🧪 Testing Prompt Generation...")
    
    # Test positional + indian market (the combination that was failing)
    prompt = create_main_prompt("Positional", "Indian Market")
    
    print(f"📝 Generated prompt length: {len(prompt)} characters")
    print(f"🔍 Contains 'top-tier': {'top-tier' in prompt}")
    print(f"🔍 Contains 'mastery': {'mastery' in prompt}")
    print(f"🔍 Contains disclaimer: {'Important Disclaimer' in prompt}")
    print(f"🔍 Contains 'not financial advice': {'not financial advice' in prompt}")
    
    # Show first 200 characters
    print(f"\n📖 Prompt preview (first 200 chars):")
    print(prompt[:200] + "...")
    
    # Show disclaimer section
    if "Important Disclaimer" in prompt:
        disclaimer_start = prompt.find("## Important Disclaimer")
        print(f"\n⚠️ Disclaimer section:")
        print(prompt[disclaimer_start:disclaimer_start+200] + "...")
    
    print("\n✅ Prompt generation test completed!")

if __name__ == "__main__":
    test_prompt_generation()
