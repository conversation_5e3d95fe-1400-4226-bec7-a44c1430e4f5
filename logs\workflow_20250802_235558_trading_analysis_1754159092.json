{"workflow_id": "trading_analysis_1754159092", "start_time": "2025-08-02T23:54:52.940651", "context": {"analysis_mode": "positional", "market_specialization": "Crypto", "chart_count": 1}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-02T23:54:52.940651", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-02T23:54:57.347847", "execution_time": 4.***************, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'support_levels', 'resistance_levels']..."}, {"step_number": 3, "step_name": "Tool Execution", "step_type": "tool_execution", "timestamp": "2025-08-02T23:54:57.347847", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: indian, Symbol: BANKNIFTY"}, {"step_number": 4, "step_name": "Tool Execution", "step_type": "tool_execution", "timestamp": "2025-08-02T23:55:03.723735", "execution_time": 6.***************, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 5, "step_name": "Tool Summarization", "step_type": "llm_call", "timestamp": "2025-08-02T23:55:03.723735", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 6, "step_name": "Tool Summarization", "step_type": "llm_call", "timestamp": "2025-08-02T23:55:15.460613", "execution_time": 11.***************, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count']"}, {"step_number": 7, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-02T23:55:19.470072", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG context"}, {"step_number": 8, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-02T23:55:58.819261", "execution_time": 39.349189043045044, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis_notes', 'trading_signals', 'detected_symbol', 'market_type']..."}], "llm_calls": [], "summary": {"total_steps": 8, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 65.87860989570618}, "end_time": "2025-08-02T23:55:58.819261", "status": "success", "error": null}