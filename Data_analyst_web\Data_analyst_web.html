<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Data Analyst Interview Prep Guide</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Warm Neutrals with Accent Blue -->
    <!-- Application Structure Plan: The SPA is designed as an interactive study tool. It features a fixed sidebar for easy navigation between question categories (SQL, Excel, etc.), which is more intuitive than a long scroll. The main content area includes a dashboard-like overview chart showing the distribution of question types, a real-time search filter, and interactive question cards. New additions include: Persistent Progress Tracking (saving 'prepared' status locally), Filtering by Prepared Status (buttons for 'All', 'Prepared', 'Unprepared'), and Copy Code Functionality for code blocks. Enhanced visuals, refined typography, subtle hover/active states, and improved spacing contribute to a more professional and engaging UI/UX. This structure was chosen to transform the static report into an active learning experience, allowing users to self-assess, track progress, quickly find relevant information, and interact with code examples, which is superior for a study guide compared to a linear document. -->
    <!-- Visualization & Content Choices: 
        1. Report Info: Question Category Distribution -> Goal: Inform -> Viz/Presentation: Donut Chart -> Interaction: Hover to see details -> Justification: Provides a quick overview of interview topic weights to help users prioritize studying. -> Library/Method: Chart.js/Canvas.
        2. Report Info: All Interview Questions -> Goal: Organize/Inform -> Viz/Presentation: Interactive Accordion Cards -> Interaction: Click category to view, click question to reveal hint, check box to mark progress, type in search bar to filter, filter by prepared status, copy code from hint. -> Justification: Breaks down content into manageable, interactive chunks. The 'reveal hint' feature encourages active recall over passive reading, and persistent progress tracking and filtering enhance the study experience. Copy code improves utility for practical questions. -> Library/Method: HTML/Tailwind/Vanilla JS.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f7f4; /* primary-bg */
            color: #333333; /* text-dark */
        }
        .nav-link {
            transition: all 0.2s ease-in-out;
            color: #666666; /* text-medium */
        }
        .nav-link.active {
            background-color: #e2e8f0; /* light gray */
            color: #2563eb; /* accent-blue */
            font-weight: 600;
        }
        .nav-link:hover:not(.active) {
            background-color: #f0f0f0; /* slightly darker hover */
        }
        .question-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .question-card:hover {
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .question-card.prepared {
            background-color: #f0fff4; /* success-green-light */
            border-left-color: #48bb78; /* success-green */
        }
        .hint {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease-in-out;
        }
        .hint.show {
            max-height: 500px; /* Adjust as needed for content */
        }
        .chart-container {
            position: relative; 
            width: 100%; 
            max-width: 400px; 
            margin-left: auto; 
            margin-right: auto; 
            height: 300px;
            max-height: 350px;
        }
         @media (min-width: 768px) { .chart-container { height: 350px; } }
         .code-block {
            background-color: #e2e8f0; /* light gray */
            padding: 10px;
            border-radius: 5px;
            font-family: 'Fira Code', 'Cascadia Code', 'Consolas', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            word-break: break-all;
            margin-top: 8px;
            overflow-x: auto;
            position: relative; /* For copy button positioning */
         }
         .copy-button {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: #a8b3c2; /* slightly darker gray */
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 0.75rem;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
         }
         .code-block:hover .copy-button {
            opacity: 1;
         }
         .filter-button {
            padding: 8px 16px;
            border-radius: 8px;
            border: 1px solid #e0e0e0; /* border-light */
            background-color: #ffffff; /* secondary-bg */
            color: #666666; /* text-medium */
            transition: all 0.2s ease-in-out;
         }
         .filter-button.active {
            background-color: #2563eb; /* accent-blue */
            color: #ffffff;
            border-color: #2563eb;
         }
         .filter-button:hover:not(.active) {
            background-color: #f0f0f0;
         }
    </style>
</head>
<body class="flex h-screen">

    <aside id="sidebar" class="w-64 bg-white border-r border-gray-200 p-5 hidden md:block overflow-y-auto">
        <h1 class="text-xl font-bold text-blue-800 mb-8">Interview Prep</h1>
        <nav id="navigation" class="space-y-2">
        </nav>
    </aside>

    <main class="flex-1 p-4 md:p-8 overflow-y-auto">
        <div class="max-w-4xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                 <h2 id="main-header" class="text-3xl font-bold text-gray-800">Welcome</h2>
                 <button id="menu-button" class="md:hidden p-2 rounded-md bg-gray-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>
                 </button>
            </div>
           
            <div id="welcome-section" class="bg-white p-6 rounded-lg shadow-sm mb-8">
                <p class="text-gray-600 leading-relaxed text-lg mb-4">This interactive guide helps you prepare for Data Analyst interviews at top companies. The questions are sourced from a comprehensive list covering all key areas. Use the navigation to select a topic, use the chart to understand topic distribution, and use the search to find specific questions. Click on any question to reveal a hint. Good luck!</p>
                <div class="chart-container mt-4">
                    <canvas id="overviewChart"></canvas>
                </div>
            </div>

            <div class="mb-6 flex space-x-2">
                <button id="filter-all" class="filter-button active">All</button>
                <button id="filter-prepared" class="filter-button">Prepared</button>
                <button id="filter-unprepared" class="filter-button">Unprepared</button>
            </div>

            <div class="mb-6">
                <input type="search" id="search-input" placeholder="Search for questions (e.g., VLOOKUP, JOIN)..." class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition">
            </div>

            <div id="questions-container" class="space-y-4">
            </div>
             <p id="no-results" class="text-center text-gray-500 mt-8 hidden">No results found.</p>
        </div>
    </main>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const interviewData = [
                {
                    category: 'SQL Questions',
                    id: 'sql',
                    questions: [
                        { q: "Write a query to find all products with a price greater than $100.", h: "```sql\nSELECT * FROM Products WHERE Price > 100;\n```" },
                        { q: "Given `Employees` (EmployeeID, Name, DepartmentID) and `Departments` (DepartmentID, DepartmentName), write a query to list all employees and their respective department names.", h: "```sql\nSELECT E.Name, D.DepartmentName\nFROM Employees E\nJOIN Departments D ON E.DepartmentID = D.DepartmentID;\n```" },
                        { q: "Write a query to find departments that have no employees.", h: "```sql\nSELECT D.DepartmentName\nFROM Departments D\nLEFT JOIN Employees E ON D.DepartmentID = E.DepartmentID\nWHERE E.EmployeeID IS NULL;\n```" },
                        { q: "How do you calculate the total sales for each product category?", h: "```sql\nSELECT ProductCategory, SUM(SalesAmount) AS TotalSales\nFROM Sales\nGROUP BY ProductCategory;\n```" },
                        { q: "Write a query to find the average order value for customers who placed more than 5 orders.", h: "```sql\nSELECT CustomerID, AVG(OrderValue) AS AverageOrderValue\nFROM Orders\nGROUP BY CustomerID\nHAVING COUNT(OrderID) > 5;\n```" },
                        { q: "How would you calculate a running total of sales over time?", h: "```sql\nSELECT OrderDate, SalesAmount,\n       SUM(SalesAmount) OVER (ORDER BY OrderDate) AS RunningTotalSales\nFROM DailySales;\n```" },
                        { q: "Write a query to find the second highest salary from an `Employees` table.", h: "```sql\nSELECT DISTINCT Salary\nFROM Employees\nORDER BY Salary DESC\nLIMIT 1 OFFSET 1;\n\n-- Alternatively, using a Window Function (more robust for ties):\nSELECT Salary\nFROM (\n    SELECT Salary, DENSE_RANK() OVER (ORDER BY Salary DESC) as rnk\n    FROM Employees\n) AS RankedSalaries\nWHERE rnk = 2;\n```" },
                        { q: "How can you find gaps in a sequence of numbers (e.g., missing order IDs) in a table named `Orders` with `OrderID` column?", h: "```sql\nWITH RECURSIVE NumberSequence AS (\n    SELECT MIN(OrderID) AS n FROM Orders\n    UNION ALL\n    SELECT n + 1 FROM NumberSequence WHERE n < (SELECT MAX(OrderID) FROM Orders)\n)\nSELECT NS.n AS MissingOrderID\nFROM NumberSequence NS\nLEFT JOIN Orders O ON NS.n = O.OrderID\nWHERE O.OrderID IS NULL;\n```" },
                        { q: "Explain the difference between `WHERE` and `HAVING` clauses.", h: "Think about the order of operations in a query. `WHERE` filters rows before any grouping happens, while `HAVING` filters aggregated groups after `GROUP BY` is applied." },
                        { q: "How do you select all columns from a table named `Customers`?", h: "This is a basic syntax check. The asterisk (`*`) is a wildcard representing 'all columns'." },
                        { q: "How do you retrieve unique customer IDs from the `Orders` table?", h: "The `DISTINCT` keyword is used to eliminate duplicate rows from the result set." },
                        { q: "Explain different types of SQL joins (INNER, LEFT, RIGHT, FULL OUTER) and when to use each.", h: "INNER: only matching rows. LEFT: all rows from the left table, and matched from right. RIGHT: all from right, matched from left. FULL OUTER: all rows when there is a match in either table." },
                        { q: "Explain the purpose of `GROUP BY` and `ORDER BY`.", h: "`GROUP BY` is used with aggregate functions to group rows that have the same values in specified columns. `ORDER BY` is used to sort the final result set." },
                        { q: "When would you use a subquery versus a JOIN?", h: "JOINs are often more readable and performant for combining tables. Subqueries are useful for more complex filtering logic, especially when the inner query needs to be calculated independently before the outer query runs." },
                        { q: "Explain what a CTE is and provide an example of when you would use it.", h: "A Common Table Expression (CTE) provides a temporary, named result set that you can reference. They improve readability and organization for complex queries, like multi-level aggregations." },
                        { q: "What are window functions? Give an example of `ROW_NUMBER()` or `RANK()`.", h: "Window functions perform calculations across a set of table rows that are somehow related to the current row. Unlike aggregate functions, they do not collapse rows. `RANK()` is used to give a rank to each row within a partition of a result set." },
                        { q: "Explain ACID properties in the context of database transactions.", h: "ACID stands for Atomicity, Consistency, Isolation, Durability. These are properties guaranteeing that database transactions are processed reliably." },
                        { q: "What is database normalization? What are its benefits?", h: "Normalization is organizing columns and tables to minimize data redundancy and improve data integrity. Benefits include less data duplication, improved data consistency, and better query performance for certain operations." },
                        { q: "What is denormalization? When would you use it?", h: "Denormalization is intentionally introducing redundancy into a database to improve query performance. It's used in data warehousing or reporting systems where read performance is critical and write operations are less frequent." },
                        { q: "Difference between `UNION` and `UNION ALL`.", h: "`UNION` combines result sets of two or more `SELECT` statements and removes duplicate rows. `UNION ALL` combines results and includes all duplicate rows." },
                        { q: "What is an index in SQL? When should you use it?", h: "An index is a performance tuning method that allows for faster retrieval of records. Use it on columns frequently used in `WHERE` clauses, `JOIN` conditions, or `ORDER BY` clauses." },
                        { q: "How do you handle duplicate records in a table?", h: "You can find duplicates using `GROUP BY` and `COUNT(*) > 1`. To remove them, you might use `DELETE` with a subquery or `ROW_NUMBER()` to identify and keep only one instance." },
                        { q: "Explain the difference between `DELETE`, `TRUNCATE`, and `DROP`.", h: "`DELETE` removes rows, can be rolled back, and allows `WHERE` clause. `TRUNCATE` removes all rows, cannot be rolled back, and is faster. `DROP` removes the entire table structure (schema and data)." },
                        { q: "What are stored procedures and functions in SQL?", h: "Stored procedures are pre-compiled SQL code that can be executed multiple times. Functions are similar but must return a single value and can be used within SQL queries." },
                        { q: "Explain `NULL` values and how to handle them in SQL.", h: "`NULL` represents missing or unknown data. It's not the same as zero or an empty string. Use `IS NULL` or `IS NOT NULL` to check for `NULL` values, as comparison operators (`=`, `!=`) don't work with `NULL`." },
                        { q: "How would you optimize a slow SQL query?", h: "Analyze the query plan (`EXPLAIN PLAN`), ensure proper indexing, rewrite complex joins, avoid `SELECT *`, use `LIMIT` for large result sets, and consider denormalization or materialized views if appropriate." },
                        { q: "What is a View in SQL? What are its benefits?", h: "A View is a virtual table based on the result-set of a SQL query. It does not store data itself but displays data from underlying tables. Benefits include simplifying complex queries, enhancing security, and providing data independence." },
                        { q: "Explain the concept of self-join and provide a scenario.", h: "A self-join is when a table is joined to itself. Scenario: Finding employees who report to the same manager, or finding pairs of products that are often purchased together." },
                        { q: "What is a transaction in SQL? How do you manage them?", h: "A transaction is a single logical unit of work. You manage them using `BEGIN TRANSACTION`, `COMMIT`, and `ROLLBACK` to ensure data integrity." },
                        { q: "How do you handle pagination in SQL queries?", h: "Use `OFFSET` and `FETCH NEXT` (or `LIMIT` in MySQL/PostgreSQL) with an `ORDER BY` clause to retrieve a specific subset of rows." },
                        { q: "What is the difference between `COUNT(*)` and `COUNT(column_name)`?", h: "`COUNT(*)` counts all rows, including those with `NULL` values. `COUNT(column_name)` counts only non-NULL values in the specified column." },
                        { q: "How would you find the top 3 products by total sales in each region?", h: "```sql\nWITH ProductSales AS (\n    SELECT\n        Region,\n        ProductID,\n        SUM(SalesAmount) AS TotalSales\n    FROM Sales\n    GROUP BY Region, ProductID\n),\nRankedProductSales AS (\n    SELECT\n        Region,\n        ProductID,\n        TotalSales,\n        ROW_NUMBER() OVER (PARTITION BY Region ORDER BY TotalSales DESC) as rn\n    FROM ProductSales\n)\nSELECT\n    Region,\n    ProductID,\n    TotalSales\nFROM RankedProductSales\nWHERE rn <= 3;\n```" },
                        { q: "Explain the concept of a surrogate key in a data warehouse.", h: "A surrogate key is an artificial primary key assigned by the data warehouse system to each record in a dimension table. It's independent of the source system's natural keys, providing flexibility and preventing issues when source keys change or are not unique." },
                        { q: "How do you handle slowly changing dimensions (SCDs) in a data warehouse? Describe Type 2.", h: "SCDs are dimensions whose attributes change over time. Type 2 SCDs preserve the full history of changes by creating a new record for each change, typically with 'start date', 'end date', and 'current flag' columns. This allows historical analysis." },
                        { q: "What is the purpose of `COALESCE` and `NULLIF` functions?", h: "`COALESCE` returns the first non-NULL expression among its arguments. `NULLIF` returns NULL if the two arguments are equal, otherwise it returns the first argument. Both are useful for handling NULLs and conditional logic." },
                        { q: "How would you identify and remove duplicate rows based on a subset of columns (e.g., `col1`, `col2`) while keeping the first occurrence?", h: "```sql\nDELETE FROM YourTable\nWHERE (col1, col2, primary_key_col) NOT IN (\n    SELECT col1, col2, MIN(primary_key_col)\n    FROM YourTable\n    GROUP BY col1, col2\n);\n\n-- Alternatively, using CTE and ROW_NUMBER:\nWITH CTE_Duplicates AS (\n    SELECT *,\n           ROW_NUMBER() OVER (PARTITION BY col1, col2 ORDER BY (SELECT NULL)) as rn\n    FROM YourTable\n)\nDELETE FROM CTE_Duplicates WHERE rn > 1;\n```" },
                        { q: "Write a SQL query to calculate the cumulative distribution of sales for each product.", h: "```sql\nSELECT\n    ProductID,\n    SalesAmount,\n    CUME_DIST() OVER (ORDER BY SalesAmount) AS CumulativeDistribution\nFROM Sales;\n```" },
                        { q: "Given a table `Events` with `UserID` and `EventTimestamp`, write a query to find the first event for each user.", h: "```sql\nSELECT UserID, EventTimestamp, EventType\nFROM (\n    SELECT\n        UserID,\n        EventTimestamp,\n        EventType,\n        ROW_NUMBER() OVER (PARTITION BY UserID ORDER BY EventTimestamp ASC) as rn\n    FROM Events\n) AS RankedEvents\nWHERE rn = 1;\n```" },
                        { q: "How would you find the difference in days between a customer's first and last order?", h: "```sql\nSELECT\n    CustomerID,\n    DATEDIFF(day, MIN(OrderDate), MAX(OrderDate)) AS DaysBetweenFirstAndLastOrder\nFROM Orders\nGROUP BY CustomerID;\n```\n(Note: `DATEDIFF` syntax varies by SQL dialect, e.g., `JULIANDAY(MAX(OrderDate)) - JULIANDAY(MIN(OrderDate))` for SQLite or `DATE_DIFF(MAX(OrderDate), MIN(OrderDate), DAY)` for BigQuery)" },
                        { q: "Write a query to pivot data from rows to columns (e.g., sales by month for each product).", h: "```sql\nSELECT ProductID, [Jan] AS JanuarySales, [Feb] AS FebruarySales, [Mar] AS MarchSales\nFROM (\n    SELECT ProductID, DATENAME(month, OrderDate) AS SalesMonth, SalesAmount\n    FROM Sales\n) AS SourceData\nPIVOT (\n    SUM(SalesAmount) FOR SalesMonth IN ([Jan], [Feb], [Mar])\n) AS PivotTable;\n```\n(Note: `PIVOT` syntax is SQL Server specific; other databases use conditional aggregation with `CASE` statements or `CROSSTAB` functions.)" },
                        { q: "How would you use a recursive CTE to generate an organizational hierarchy?", h: "```sql\nWITH RECURSIVE OrgHierarchy AS (\n    SELECT EmployeeID, Name, ManagerID, 0 AS Level\n    FROM Employees\n    WHERE ManagerID IS NULL -- Start with top-level managers\n    UNION ALL\n    SELECT e.EmployeeID, e.Name, e.ManagerID, oh.Level + 1\n    FROM Employees e\n    JOIN OrgHierarchy oh ON e.ManagerID = oh.EmployeeID\n)\nSELECT * FROM OrgHierarchy;\n```" },
                        { q: "Explain the concept of 'materialized views' and when they are useful.", h: "A materialized view is a database object that contains the results of a query and is stored as a physical table. Unlike a regular view, its data is pre-computed and stored. It's useful for improving query performance on complex aggregations or joins, especially in data warehousing, but requires refresh mechanisms." },
                        { q: "How do you handle `NULL` values when performing aggregations (e.g., `AVG`, `SUM`)?", h: "Aggregate functions like `SUM`, `AVG`, `COUNT` (on a column), `MIN`, `MAX` typically ignore `NULL` values by default. If you want to treat `NULL`s as zero for `SUM` or `AVG`, you'd use `COALESCE(column_name, 0)`." },
                        { q: "Describe a scenario where you would use `EXISTS` vs. `IN` in a subquery.", h: "`EXISTS` checks for the existence of rows returned by a subquery and is generally more efficient for large subquery results because it stops scanning as soon as it finds a match. `IN` checks if a value is present in a list of values returned by a subquery. Use `EXISTS` when you only need to check for existence, `IN` when you need to match against a specific set of values." },
                    ]
                },
                {
                    category: 'Excel Questions',
                    id: 'excel',
                    questions: [
                        { q: "How would you use `SUMIFS` to calculate total sales for 'ProductA' in 'RegionX'?", h: "```excel\n=SUMIFS(Sales[Sales Amount], Sales[Product Name], \"ProductA\", Sales[Region], \"RegionX\")\n```" },
                        { q: "How do you extract the year from a date in Excel (cell A1)?", h: "```excel\n=YEAR(A1)\n```" },
                        { q: "How would you use `INDEX-MATCH` to find the salary of 'John Doe' from a table where names are in column A and salaries in column B?", h: "```excel\n=INDEX(B:B, MATCH(\"John Doe\", A:A, 0))\n```" },
                        { q: "How do you create a dropdown list in Excel using Data Validation?", h: "Go to Data Tab > Data Validation. In the 'Allow' dropdown, select 'List'. In the 'Source' box, type your comma-separated list items (e.g., 'Option1,Option2,Option3') or select a range containing the list." },
                        { q: "How would you use an array formula (CSE formula) to count unique values in a range (A1:A10)?", h: "```excel\n={SUM(1/COUNTIF(A1:A10,A1:A10))}\n```\n(Enter with Ctrl+Shift+Enter)" },
                        { q: "Explain `VLOOKUP`. When would you use `INDEX-MATCH` instead?", h: "`VLOOKUP` searches vertically for a value in the first column of a range and returns a value in the same row from a specified column. `INDEX-MATCH` is more flexible as it can look up values in any column and return values from any other column, and is less prone to errors if columns are inserted/deleted." },
                        { q: "How do you remove duplicate values from a dataset?", h: "Use the built-in 'Remove Duplicates' feature under the Data tab. It permanently deletes rows." },
                        { q: "Describe how you would handle missing values in a large Excel dataset.", h: "Methods include deleting the rows/columns (if data loss is acceptable), filling with a constant (0, 'N/A'), or imputing with the mean/median/mode of the column." },
                        { q: "Explain the purpose of a Pivot Table. How do you create one?", h: "A Pivot Table is a powerful tool for summarizing, analyzing, and exploring large datasets. You create one by selecting your data range and using the 'PivotTable' option under the Insert tab." },
                        { q: "What is Data Validation and how can it be used to improve data quality?", h: "Data Validation restricts the type of data or the values that users can enter into a cell. For example, you can create a dropdown list of valid options or restrict entries to whole numbers." },
                        { q: "What are `TEXT`, `LEFT`, `RIGHT`, `MID` functions used for?", h: "`TEXT` converts a value to text with specific formatting. `LEFT`/`RIGHT`/`MID` extract a specified number of characters from the left, right, or middle of a text string, respectively." },
                        { q: "Explain Conditional Formatting and give an example of its application.", h: "Conditional Formatting applies formatting (like colors, icons, data bars) to cells based on their values. Example: Highlight cells in red if sales are below a target, or green if above." },
                        { q: "Have you used Power Query or Power Pivot? If so, describe a scenario.", h: "Power Query is for data extraction, transformation, and loading. Power Pivot is for data modeling and creating relationships. Scenario: Use Power Query to combine sales data from multiple sources, then Power Pivot to build a data model for analysis." },
                        { q: "How do you create a dynamic chart in Excel that updates based on a cell value or selection?", h: "You can use named ranges with `OFFSET` or `INDEX` to define dynamic data sources for your charts, or use Slicers/Timelines with Pivot Charts." },
                        { q: "What is the 'What-If Analysis' feature in Excel? Name its tools.", h: "What-If Analysis allows you to see how changing values in formulas affects the outcome. Tools include Scenario Manager, Goal Seek, and Data Tables." },
                        { q: "How would you use Excel's 'Text to Columns' feature?", h: "It's used to split a single column of text into multiple columns based on a delimiter (e.g., comma, space) or fixed width. Useful for parsing messy data." },
                        { q: "Describe a complex data cleaning task you handled in Excel.", h: "Focus on identifying inconsistencies, using functions like `TRIM`, `CLEAN`, `PROPER`, `FIND`, `REPLACE`, and applying data validation or conditional formatting to spot issues." },
                        { q: "How would you use `XLOOKUP` (if available) compared to `VLOOKUP`?", h: "```excel\n=XLOOKUP(lookup_value, lookup_array, return_array, [if_not_found], [match_mode], [search_mode])\n```\n`XLOOKUP` is more flexible than `VLOOKUP` as it can look up values to the left, supports approximate and exact matches, and allows specifying a custom message for not found values. It's generally preferred over `VLOOKUP` and `INDEX-MATCH` in newer Excel versions." },
                        { q: "How do you protect a worksheet or workbook in Excel?", h: "Go to the 'Review' tab. Use 'Protect Sheet' to prevent changes to cells, rows, or columns (you can specify allowed actions). Use 'Protect Workbook' to prevent structural changes like adding/deleting sheets." },
                        { q: "Explain the use of `IFERROR` and `ISNA` functions.", h: "`IFERROR` allows you to specify a value or calculation to return if a formula results in an error (like #N/A, #DIV/0!). `ISNA` checks if a value is the #N/A error and returns TRUE or FALSE. `IFERROR` is generally more versatile as it catches all errors." },
                        { q: "How would you use `GETPIVOTDATA` and why might you avoid it?", h: "```excel\n=GETPIVOTDATA(\"Sales Amount\",$A$3,\"Product\",\"ProductA\")\n```\n`GETPIVOTDATA` extracts data from a PivotTable. While precise, it can be rigid. Many prefer direct cell references or `CUBEVALUE` functions for more flexible reporting that doesn't break when PivotTable layout changes." },
                        { q: "Describe a scenario where you would use Excel's Power Pivot data model instead of just a regular PivotTable.", h: "You would use Power Pivot when dealing with multiple tables that need to be related (e.g., Sales, Products, Customers) to perform analysis across them, or when you need to write complex DAX measures that regular PivotTables don't support." },
                        { q: "How would you combine data from multiple Excel sheets or workbooks into one master sheet efficiently?", h: "For simple cases, copy-pasting. For more robust and repeatable processes, use Power Query to connect to multiple files/sheets, transform, and append them. This creates a refreshable connection." },
                        { q: "Explain the purpose of 'Named Ranges' in Excel and how they can be beneficial.", h: "Named Ranges assign a descriptive name to a cell or range of cells. Benefits include making formulas easier to read and write, simplifying navigation, and creating dynamic ranges for charts or data validation." },
                        { q: "How do you use Excel's 'Solver' add-in for optimization problems?", h: "Solver is used to find the optimal (maximum or minimum) value for a formula in one cell — called the objective cell — subject to constraints on other formula cells. You define the objective, changing cells, and constraints." },
                        { q: "Describe the use of `CHOOSE` and `SWITCH` functions in Excel.", h: "`CHOOSE` returns a value from a list of values based on an index number. `SWITCH` evaluates one value (called the expression) against a list of values and returns the result corresponding to the first matching value. `SWITCH` is more modern and often cleaner for multiple conditions than nested `IF`s." },
                    ]
                },
                {
                    category: 'Power BI Questions',
                    id: 'powerbi',
                    questions: [
                        { q: "Write a DAX measure to calculate 'Total Sales'.", h: "```dax\nTotal Sales = SUM(Sales[SalesAmount])\n```" },
                        { q: "Write a DAX measure to calculate 'Sales Last Year' using `CALCULATE` and `SAMEPERIODLASTYEAR`.", h: "```dax\nSales Last Year = CALCULATE(\n    [Total Sales],\n    SAMEPERIODLASTYEAR('Date'[Date])\n)\n```" },
                        { q: "Write a DAX measure to calculate 'Sales YTD' (Year-To-Date).", h: "```dax\nSales YTD = TOTALYTD(\n    [Total Sales],\n    'Date'[Date]\n)\n```" },
                        { q: "How would you create a custom column in Power Query to categorize 'SalesAmount' into 'High', 'Medium', 'Low'?", h: "In Power Query, add a 'Custom Column' with M code:\n```powerquery-m\nif [SalesAmount] > 1000 then \"High\" \nelse if [SalesAmount] > 500 then \"Medium\" \nelse \"Low\"\n```" },
                        { q: "How would you unpivot columns 'Q1', 'Q2', 'Q3', 'Q4' from a sales table into 'Quarter' and 'Sales' columns in Power Query?", h: "Select the 'Q1', 'Q2', 'Q3', 'Q4' columns in Power Query Editor, then go to Transform tab > Unpivot Columns." },
                        { q: "What is DAX (Data Analysis Expressions)?", h: "DAX is a formula language used in Power BI, Power Pivot, and SSAS Tabular models to define custom calculations for measures, calculated columns, and calculated tables." },
                        { q: "Explain the difference between a calculated column and a measure in DAX.", h: "Calculated columns add new columns to a table, computed row by row, and are stored in the model. Measures are dynamic calculations performed at query time based on the current filter context, and their results are not stored." },
                        { q: "What is the `CALCULATE` function in DAX and why is it important?", h: "`CALCULATE` is the most powerful function in DAX. It evaluates an expression in a context modified by filters. It allows you to change the filter context of a calculation." },
                        { q: "What are context transitions in DAX?", h: "Context transition occurs when a row context (like in a calculated column or an iterator function like `SUMX`) implicitly converts to an equivalent filter context within a `CALCULATE` function, allowing row-level calculations to be affected by filters." },
                        { q: "Difference between `SUM` and `SUMX` in DAX.", h: "`SUM` is an aggregate function that sums up values in a column. `SUMX` is an iterator function that evaluates an expression for each row of a table and then sums the results. `SUMX` is used for row-by-row calculations." },
                        { q: "What is Power Query (Get & Transform Data)?", h: "Power Query is an ETL (Extract, Transform, Load) tool in Power BI (and Excel) used to connect to various data sources, clean, reshape, and transform data before loading it into the data model." },
                        { q: "Describe common steps for cleaning and transforming data in Power Query.", h: "Steps include removing columns, changing data types, handling missing values, splitting columns, merging/appending queries, unpivoting data, and creating custom columns." },
                        { q: "What is M language in Power Query?", h: "M is the functional programming language behind Power Query. Every transformation performed in the Power Query Editor is translated into M code." },
                        { q: "Difference between `Merge Queries` and `Append Queries` in Power Query.", h: "`Merge Queries` combines columns from two tables based on a common column (like a SQL JOIN). `Append Queries` stacks rows from two or more tables on top of each other (like a SQL UNION ALL)." },
                        { q: "How do you handle relationships between tables in Power BI?", h: "Relationships are created in the Model view by dragging and dropping columns between tables. Power BI automatically detects relationships, but they often need to be manually adjusted for cardinality and cross-filter direction." },
                        { q: "What are the types of relationships in Power BI?", h: "One-to-one, one-to-many, many-to-one, and many-to-many. One-to-many is the most common." },
                        { q: "Explain Row-Level Security (RLS) in Power BI.", h: "RLS restricts data access for given users. Filters are defined in Power BI Desktop, and when published to Power BI Service, users only see the data allowed by their assigned roles." },
                        { q: "What are some best practices for designing effective Power BI dashboards?", h: "Keep it simple, focus on key metrics, use appropriate visualizations, ensure consistent formatting, optimize for performance, and consider the target audience's needs." },
                        { q: "How would you optimize a Power BI report for performance?", h: "Optimize data model (remove unnecessary columns/rows, use optimal data types), reduce cardinality, use direct query sparingly, optimize DAX measures, and minimize the number of visuals on a page." },
                        { q: "Explain filter context and row context in DAX.", h: "Filter context is the set of filters applied to the data model (e.g., from slicers, visuals). Row context is the 'current row' being evaluated, typically within an iterator function or calculated column." },
                        { q: "When would you use `ALL`, `ALLEXCEPT`, and `ALLSELECTED` in DAX?", h: "These functions modify filter context. `ALL` removes all filters from a table/column. `ALLEXCEPT` removes all filters except those specified. `ALLSELECTED` removes filters from the current query but keeps filters from slicers/filters outside the query." },
                        { q: "How do you handle data refresh in Power BI?", h: "Data can be refreshed manually in Power BI Desktop, or scheduled refresh can be set up in Power BI Service for cloud sources or via an On-premises Data Gateway for on-premise sources." },
                        { q: "What is a data gateway in Power BI?", h: "An On-premises Data Gateway acts as a bridge to provide secure data transfer between on-premises data sources and Power BI (or other Microsoft cloud services)." },
                        { q: "Describe a challenging data transformation you performed in Power Query.", h: "Focus on a scenario involving complex unpivoting, merging, or custom column creation, explaining the steps and the M code logic if you remember it." },
                        { q: "What is a 'Star Schema' in the context of Power BI data modeling and why is it preferred?", h: "A Star Schema consists of a central fact table surrounded by multiple dimension tables. It's preferred in Power BI because it simplifies DAX calculations, improves query performance, and makes the data model easier to understand and navigate for users." },
                        { q: "How do you handle many-to-many relationships in Power BI?", h: "Many-to-many relationships are typically handled by introducing a 'bridge' or 'junction' table between the two tables that have the many-to-many relationship. This bridge table creates two one-to-many relationships, allowing proper filtering." },
                        { q: "Explain the concept of 'Data Lineage' in Power BI.", h: "Data lineage refers to the lifecycle of data, from its origin to its current state, including all transformations and movements. In Power BI, it helps understand where data comes from, how it's transformed, and where it's used, which is crucial for data governance and troubleshooting." },
                        { q: "What are Power BI Deployment Pipelines?", h: "Deployment Pipelines in Power BI Service allow developers to manage the lifecycle of their Power BI content (reports, dashboards, datasets) through development, test, and production stages, enabling continuous integration and deployment practices." },
                        { q: "Write a DAX measure to calculate the percentage of total sales for each product.", h: "```dax\n% of Total Sales = DIVIDE(\n    [Total Sales],\n    CALCULATE([Total Sales], ALL(Products))\n)\n```" },
                        { q: "How would you implement dynamic titles or labels in Power BI visuals?", h: "Dynamic titles can be created by writing a DAX measure that returns a text string, and then linking the visual's title property to that measure. This allows titles to change based on filter selections." },
                        { q: "Explain the purpose of 'DirectQuery' vs 'Import' mode in Power BI.", h: "Import mode loads all data into Power BI's in-memory engine, offering fast performance but requiring data refresh. DirectQuery mode connects directly to the source database, querying data in real-time but can be slower and has limitations. Choose based on data volume, refresh frequency, and performance needs." },
                        { q: "Write a DAX measure to calculate the rank of products by sales.", h: "```dax\nProduct Sales Rank = RANKX(ALL(Products[Product Name]), [Total Sales], , ASC, DENSE)\n```" },
                        { q: "How would you use the `CROSSJOIN` function in DAX?", h: "```dax\nCROSSJOIN(Table1[Column1], Table2[Column2])\n```\n`CROSSJOIN` returns a table that contains the Cartesian product of rows from all tables in its arguments. It's often used in advanced scenarios to create tables for calculations, but can be resource-intensive." },
                        { q: "What are 'Parameters' in Power Query and how are they used?", h: "Parameters allow you to make queries flexible and reusable by defining values that can be easily changed without editing the M code. They are used for dynamic data sources, filtering, or modifying query behavior (e.g., changing a file path or a date range)." },
                        { q: "How do you handle errors in Power Query (e.g., data type conversion errors)?", h: "You can use 'Replace Errors' transformation to replace errors with nulls or specific values. For more control, use `try...otherwise` expressions in custom columns to handle errors at a row level." },
                    ]
                },
                {
                    category: 'Python/R Questions',
                    id: 'python-r',
                    questions: [
                        { q: "How do you load a CSV file named 'data.csv' into a Pandas DataFrame?", h: "```python\nimport pandas as pd\ndf = pd.read_csv('data.csv')\n```" },
                        { q: "How do you handle missing values (NaN) in a Pandas DataFrame by dropping rows with any NaN?", h: "```python\ndf_cleaned = df.dropna()\n```" },
                        { q: "How do you calculate the sum of 'Sales' grouped by 'Category' in a Pandas DataFrame?", h: "```python\ncategory_sales = df.groupby('Category')['Sales'].sum()\n```" },
                        { q: "How do you merge two DataFrames, `df1` and `df2`, on a common column 'ID' using an inner join?", h: "```python\nmerged_df = pd.merge(df1, df2, on='ID', how='inner')\n```" },
                        { q: "Explain list comprehensions in Python and provide an example to create a list of squares for even numbers up to 10.", h: "List comprehensions offer a concise way to create lists. Example:\n```python\nsquares_of_evens = [x**2 for x in range(11) if x % 2 == 0]\n# Result: [0, 4, 16, 36, 64, 100]\n```" },
                        { q: "How do you handle outliers in a 'Price' column of a DataFrame using the IQR method?", h: "```python\nQ1 = df['Price'].quantile(0.25)\nQ3 = df['Price'].quantile(0.75)\nIQR = Q3 - Q1\nlower_bound = Q1 - 1.5 * IQR\nupper_bound = Q3 + 1.5 * IQR\ndf_filtered = df[(df['Price'] >= lower_bound) & (df['Price'] <= upper_bound)]\n```" },
                        { q: "Explain the difference between a list and a tuple in Python.", h: "Lists are mutable (can be changed), while tuples are immutable (cannot be changed after creation). Tuples are generally faster and used for data that should not be modified." },
                        { q: "What is feature engineering? Give an example.", h: "Feature engineering is the process of using domain knowledge to create new input variables (features) from existing data to improve machine learning model performance. Example: Creating a 'day_of_week' feature from a 'date' column." },
                        { q: "What is a decorator in Python?", h: "A decorator is a design pattern that allows you to add new functionality to an existing object without modifying its structure. It's essentially a function that takes another function as an argument and returns a new function." },
                        { q: "Difference between `iloc` and `loc` in Pandas.", h: "`iloc` is primarily integer-location based indexing (by position), while `loc` is primarily label-based indexing (by row/column labels)." },
                        { q: "How do you handle categorical data in Pandas?", h: "You can convert categorical columns to the 'category' dtype using `astype('category')`, or use one-hot encoding (`pd.get_dummies()`) or label encoding for machine learning models." },
                        { q: "Explain the `apply()` function in Pandas.", h: "The `apply()` method is used to apply a function along an axis of a DataFrame or Series. It's often used for row-wise or column-wise operations that are not vectorized." },
                        { q: "What is a virtual environment in Python and why is it used?", h: "A virtual environment is a self-contained directory tree that contains a Python installation and a number of additional packages. It's used to manage dependencies for different projects, preventing conflicts between package versions." },
                        { q: "Explain the concept of vectorization in Python/NumPy/Pandas.", h: "Vectorization is the process of performing operations on entire arrays/series at once, rather than element by element. It's significantly faster due to optimized C implementations and efficient memory usage." },
                        { q: "How would you read data from a database into a Pandas DataFrame?", h: "Use libraries like `sqlalchemy` and database-specific drivers (e.g., `psycopg2` for PostgreSQL) to create a connection, then `pd.read_sql_query()` or `pd.read_sql_table()`." },
                        { q: "What is the purpose of `pivot_table` in Pandas?", h: "`pivot_table` is used to summarize and aggregate data from a DataFrame. It allows you to transform data from 'long' to 'wide' format, similar to a pivot table in Excel." },
                        { q: "Explain `melt` in Pandas and when you would use it.", h: "`melt` is used to unpivot a DataFrame from a 'wide' format to a 'long' format. It's useful when you have multiple columns that represent values of a single variable, and you want to stack them into one column." },
                        { q: "How do you perform basic data visualization using Matplotlib/Seaborn?", h: "Matplotlib provides a foundational plotting library (e.g., `plt.plot()`, `plt.scatter()`). Seaborn is built on Matplotlib and provides a high-level interface for drawing attractive and informative statistical graphics (e.g., `sns.boxplot()`, `sns.scatterplot()`)." },
                        { q: "Explain the purpose of `lambda` functions in Python.", h: "Lambda functions are small, anonymous functions defined with the `lambda` keyword. They can take any number of arguments but can only have one expression. They are often used for short, simple operations where a full function definition is overkill, especially with `map()`, `filter()`, or `apply()`." },
                        { q: "How do you handle time series data in Pandas (e.g., setting index, resampling)?", h: "```python\ndf['Date'] = pd.to_datetime(df['Date'])\ndf = df.set_index('Date')\n# Resample to monthly sum\ndf_monthly = df.resample('M')['Sales'].sum()\n```\nPandas has robust time series capabilities, including `to_datetime` for conversion, `set_index` for time-based indexing, and `resample` for frequency conversion and aggregation." },
                        { q: "What is the difference between `append`, `concat`, and `merge` in Pandas?", h: "`append` (deprecated in newer Pandas versions, use `concat`) stacks rows. `concat` stacks DataFrames along an axis (rows or columns). `merge` combines DataFrames based on common columns (like SQL JOINs)." },
                        { q: "How would you perform a basic linear regression in Python using `scikit-learn`?", h: "```python\nfrom sklearn.linear_model import LinearRegression\nfrom sklearn.model_selection import train_test_split\n\nX = df[['Feature1', 'Feature2']] # Independent variables\ny = df['Target'] # Dependent variable\n\nX_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n\nmodel = LinearRegression()\nmodel.fit(X_train, y_train)\n\npredictions = model.predict(X_test)\n```" },
                        { q: "How would you count the number of unique values in a Pandas DataFrame column 'CustomerID'?", h: "```python\nunique_customers = df['CustomerID'].nunique()\n```" },
                        { q: "How do you handle large datasets in Pandas that might not fit into memory?", h: "Strategies include processing data in chunks (`pd.read_csv(chunksize=...)`), using more memory-efficient data types (`df.astype()`), using libraries like Dask or Vaex for out-of-core processing, or leveraging cloud-based data warehouses." },
                        { q: "Explain the concept of 'broadcasting' in NumPy.", h: "Broadcasting describes how NumPy treats arrays with different shapes during arithmetic operations. It allows operations to be performed on arrays of different sizes by effectively 'stretching' the smaller array across the larger array without actually duplicating data." },
                        { q: "How would you perform string manipulation (e.g., cleaning text) on a Pandas Series?", h: "```python\ndf['TextColumn'] = df['TextColumn'].str.lower() # Convert to lowercase\ndf['TextColumn'] = df['TextColumn'].str.strip() # Remove leading/trailing whitespace\ndf['TextColumn'] = df['TextColumn'].str.replace('[^a-zA-Z0-9\\s]', '', regex=True) # Remove special characters\n```" },
                        { q: "What is the purpose of `pd.cut()` and `pd.qcut()` in Pandas?", h: "`pd.cut()` is used to segment and sort data values into bins (intervals) of equal width. `pd.qcut()` is used to segment data based on quantiles, meaning each bin will have approximately the same number of observations. Useful for binning continuous data." },
                        { q: "How do you handle duplicate rows in a Pandas DataFrame?", h: "```python\ndf_no_duplicates = df.drop_duplicates() # Drop all duplicate rows\ndf_no_duplicates_subset = df.drop_duplicates(subset=['col1', 'col2']) # Drop based on specific columns\n```" },
                        { q: "Explain how you would use `pivot` and `unstack` in Pandas.", h: "`pivot` is used to reshape a DataFrame based on column values. `unstack` is used to pivot a level of the (hierarchical) row index to the column axis. They are powerful for reshaping data for analysis or visualization." },
                        { q: "How do you save a Pandas DataFrame to a CSV or Excel file?", h: "```python\ndf.to_csv('output.csv', index=False) # Save to CSV, without DataFrame index\ndf.to_excel('output.xlsx', index=False) # Save to Excel\n```" },
                        { q: "Describe a scenario where you would use `map()` or `applymap()` in Pandas.", h: "`map()` is used on a Series for element-wise transformations (e.g., mapping IDs to names). `applymap()` is used on a DataFrame for element-wise transformations across all cells (e.g., formatting all numbers)." },
                    ]
                },
                {
                    category: 'Statistics & Probability',
                    id: 'stats-prob',
                    questions: [
                        { q: "Define mean, median, and mode. When is the median a better measure of central tendency than the mean?", h: "Mean: average. Median: middle value. Mode: most frequent value. The median is better than the mean when the data has significant outliers, as the mean is sensitive to extreme values." },
                        { q: "What is the Central Limit Theorem? Why is it important?", h: "It states that the sampling distribution of the sample mean will be approximately normally distributed, regardless of the original population's distribution, as long as the sample size is large enough. It's the foundation for many statistical procedures like hypothesis testing and confidence intervals." },
                        { q: "What is hypothesis testing? Explain Type I and Type II errors.", h: "Hypothesis testing is a statistical method to test an assumption. Type I error (false positive): rejecting a true null hypothesis. Type II error (false negative): failing to reject a false null hypothesis." },
                        { q: "What is a p-value? How do you interpret it?", h: "The p-value is the probability of obtaining test results at least as extreme as the results actually observed, assuming the null hypothesis is correct. A small p-value (typically ≤ 0.05) indicates strong evidence against the null hypothesis." },
                        { q: "How would you design an A/B test for a new website feature?", h: "Define a clear hypothesis. Randomly split users into a control group (A, no feature) and a variant group (B, with feature). Collect data on a key metric (e.g., conversion rate). Use statistical tests to determine if the difference is significant." },
                        { q: "Explain the difference between correlation and causation.", h: "Correlation measures the strength and direction of a linear relationship between two variables. Causation means one event is the result of the occurrence of the other event. Correlation does not imply causation." },
                        { q: "What is multicollinearity in regression analysis?", h: "Multicollinearity occurs when independent variables in a regression model are highly correlated with each other. It can make it difficult to interpret the individual impact of each independent variable." },
                        { q: "Explain sampling bias and give an example.", h: "Sampling bias occurs when a sample is collected in such a way that some members of the intended population are less likely to be included than others. Example: A survey about online habits conducted only via landline phones." },
                        { q: "What is ANOVA (Analysis of Variance)? When is it used?", h: "ANOVA is a statistical test used to compare the means of three or more groups to determine if there are statistically significant differences between them. It's typically used when you have one categorical independent variable and one continuous dependent variable." },
                        { q: "Difference between parametric and non-parametric tests.", h: "Parametric tests assume the data comes from a specific probability distribution (e.g., normal distribution) and often require interval or ratio data. Non-parametric tests make fewer assumptions about the data distribution and are often used for ordinal or nominal data." },
                        { q: "How do you determine statistical significance in an A/B test?", h: "You typically set a significance level (alpha, e.g., 0.05). If the p-value from your statistical test (e.g., t-test, chi-squared test) is less than alpha, you reject the null hypothesis and consider the result statistically significant." },
                        { q: "What is a confidence interval and how is it interpreted?", h: "A confidence interval provides a range of values within which the true population parameter (e.g., mean, proportion) is likely to lie, with a certain level of confidence (e.g., 95%). It quantifies the uncertainty around an estimate." },
                        { q: "Explain the concept of bias-variance tradeoff in modeling.", h: "Bias is the error from erroneous assumptions in the learning algorithm (underfitting). Variance is the error from sensitivity to small fluctuations in the training set (overfitting). The tradeoff is that reducing one often increases the other; the goal is to find a balance for optimal model performance." },
                        { q: "What is sampling and why is it important in data analysis?", h: "Sampling is the process of selecting a subset of data from a larger dataset. It's important because analyzing entire large datasets can be computationally expensive or impossible. Proper sampling allows for drawing valid inferences about the entire population from a smaller, representative subset." },
                        { q: "How do you calculate the required sample size for an A/B test?", h: "Sample size calculation depends on several factors: desired statistical power (e.g., 80%), significance level (alpha, e.g., 0.05), minimum detectable effect (MDE), and baseline conversion rate. Online calculators or statistical formulas (e.g., for proportions) can be used." },
                        { q: "Explain the difference between descriptive and inferential statistics.", h: "Descriptive statistics summarize and describe the characteristics of a dataset (e.g., mean, median, standard deviation). Inferential statistics use a sample to make inferences or predictions about a larger population (e.g., hypothesis testing, confidence intervals)." },
                        { q: "What is a Z-score and how is it used?", h: "A Z-score (or standard score) measures how many standard deviations an element is from the mean. It's used to standardize data, compare observations from different distributions, and identify outliers." },
                        { q: "What is statistical power in hypothesis testing?", h: "Statistical power is the probability that a test will correctly reject a false null hypothesis (i.e., avoid a Type II error). A higher power means a lower chance of missing a real effect." },
                        { q: "Explain the concept of A/B test duration and common pitfalls.", h: "Test duration depends on sample size, expected effect, and traffic. Pitfalls include stopping too early (peeking), not running long enough to capture full cycles (e.g., weekly seasonality), and not accounting for novelty effects." },
                        { q: "What is a regression residual and what does it tell you?", h: "A residual is the difference between the observed value and the predicted value in a regression model. Analyzing residuals (e.g., plotting them against predicted values) helps check model assumptions (linearity, homoscedasticity, normality of errors)." },
                        { q: "Describe the characteristics of a normal distribution.", h: "A normal distribution (bell curve) is symmetric around its mean, with the mean, median, and mode being equal. Its shape is determined by its mean and standard deviation, and it's commonly found in natural phenomena and sampling distributions." },
                        { q: "How would you handle missing data when performing statistical analysis?", h: "Strategies include complete case analysis (listwise deletion), mean/median/mode imputation, regression imputation, or more advanced methods like multiple imputation. The choice depends on the nature and extent of missingness." },
                        { q: "What is the difference between a one-tailed and two-tailed hypothesis test?", h: "A one-tailed test is used when you have a specific direction in your hypothesis (e.g., 'A is *greater than* B'). A two-tailed test is used when you are looking for any significant difference, regardless of direction (e.g., 'A is *different from* B')." },
                    ]
                },
                {
                    category: 'Machine Learning Fundamentals',
                    id: 'ml-fundamentals',
                    questions: [
                        { q: "Explain the difference between supervised and unsupervised learning.", h: "Supervised learning uses labeled data to train models to predict outcomes (e.g., classification, regression). Unsupervised learning works with unlabeled data to find patterns or structures (e.g., clustering, dimensionality reduction)." },
                        { q: "What is overfitting and underfitting in machine learning?", h: "Overfitting occurs when a model learns the training data too well, including noise, and performs poorly on new data. Underfitting occurs when a model is too simple to capture the underlying patterns in the data, performing poorly on both training and new data." },
                        { q: "Name and explain some common evaluation metrics for classification models.", h: "Accuracy (overall correct predictions), Precision (proportion of true positives among all positive predictions), Recall (proportion of true positives among all actual positives), F1-score (harmonic mean of precision and recall), AUC-ROC curve (measures classifier performance across all classification thresholds)." },
                        { q: "What is a regression model used for?", h: "Regression models are used to predict a continuous outcome variable based on one or more independent variables. Examples include predicting house prices or sales figures." },
                        { q: "What is clustering? Give an example of a clustering algorithm.", h: "Clustering is an unsupervised learning technique used to group similar data points together. K-Means is a common algorithm that partitions data into K clusters where each data point belongs to the cluster with the nearest mean." },
                        { q: "How do you handle imbalanced datasets in machine learning?", h: "Techniques include oversampling the minority class (e.g., SMOTE), undersampling the majority class, using different evaluation metrics (e.g., F1-score, AUC-ROC instead of accuracy), or using algorithms robust to imbalance." },
                        { q: "What is cross-validation and why is it used?", h: "Cross-validation is a technique for evaluating machine learning models by training them on subsets of the input data and testing them on the complementary subset. It helps assess how the model generalizes to an independent dataset and reduces the risk of overfitting." },
                        { q: "Explain the concept of a 'feature' in machine learning.", h: "A feature is an individual measurable property or characteristic of a phenomenon being observed. In a dataset, features are typically represented as columns. Effective feature selection and engineering are crucial for model performance." },
                        { q: "What is the difference between classification and regression?", h: "Classification models predict a categorical outcome (e.g., spam/not spam, customer churn/no churn). Regression models predict a continuous numerical outcome (e.g., house price, sales volume)." },
                        { q: "What is the purpose of regularization in machine learning?", h: "Regularization techniques (e.g., L1 Lasso, L2 Ridge) are used to prevent overfitting by adding a penalty to the model's complexity. They discourage overly complex models that might fit the training data too well but generalize poorly." },
                        { q: "Explain the concept of a confusion matrix.", h: "A confusion matrix is a table that summarizes the performance of a classification model on a set of test data. It shows the number of true positives, true negatives, false positives, and false negatives, which are used to calculate other metrics like precision and recall." },
                        { q: "What is the 'Curse of Dimensionality' in machine learning?", h: "The 'Curse of Dimensionality' refers to various problems that arise when working with high-dimensional data (many features). As the number of dimensions increases, the data becomes sparse, making it harder to find patterns and leading to increased computational complexity and risk of overfitting." },
                        { q: "Explain 'Feature Scaling' and why it's important.", h: "Feature scaling is the process of normalizing the range of independent variables or features of data. It's important for algorithms that calculate distances between data points (e.g., K-Means, SVM, KNN) or rely on gradient descent (e.g., neural networks) to prevent features with larger ranges from dominating the objective function." },
                        { q: "What is a 'Decision Tree' and how does it work?", h: "A Decision Tree is a flowchart-like structure where each internal node represents a 'test' on an attribute, each branch represents the outcome of the test, and each leaf node represents a class label (for classification) or a numerical value (for regression). It makes decisions by recursively splitting the data based on feature values." },
                        { q: "What is the difference between 'Bagging' and 'Boosting' ensemble methods?", h: "Both are ensemble methods that combine multiple models to improve performance. Bagging (e.g., Random Forest) trains models independently in parallel and averages their predictions. Boosting (e.g., Gradient Boosting, XGBoost) trains models sequentially, with each new model trying to correct the errors of the previous ones." },
                        { q: "How do you evaluate a regression model?", h: "Common metrics include Mean Absolute Error (MAE), Mean Squared Error (MSE), Root Mean Squared Error (RMSE), and R-squared. MAE is less sensitive to outliers, MSE/RMSE penalize larger errors more, and R-squared indicates the proportion of variance in the dependent variable predictable from the independent variables." },
                    ]
                },
                {
                    category: 'Data Visualization',
                    id: 'viz',
                    questions: [
                        { q: "What makes a good data visualization?", h: "Clarity, accuracy, and efficiency. It should tell a clear story, represent the data truthfully, and allow the viewer to understand the key insights quickly without clutter." },
                        { q: "How do you choose the right chart type for your data?", h: "It depends on the goal. Use line charts for trends over time, bar charts for comparisons, scatter plots for relationships between variables, and pie charts for parts of a whole." },
                        { q: "Explain the importance of data storytelling.", h: "Data storytelling is the practice of building a narrative around a set of data and its accompanying visualizations to help convey the meaning of that data in a powerful and memorable way. It adds context and interpretation to the numbers." },
                        { q: "What are some common pitfalls in data visualization and how do you avoid them?", h: "Pitfalls include misleading scales, inappropriate chart types, too much clutter, poor color choices, and lack of clear labels. Avoid them by focusing on clarity, accuracy, simplicity, and understanding your audience." },
                        { q: "How do you ensure accessibility in your data visualizations?", h: "Consider color blindness (use color-blind friendly palettes), provide text alternatives for images, ensure sufficient contrast, and use clear, readable fonts. Tools can help check for accessibility compliance." },
                        { q: "How do you handle 'too much data' for a single visualization?", h: "Strategies include aggregation (summing, averaging), filtering (showing top N, specific segments), sampling, using interactive elements (drill-downs, filters), or choosing appropriate chart types for large datasets (e.g., heatmaps, density plots)." },
                        { q: "Describe the difference between an infographic and a data dashboard.", h: "An infographic is typically a static, self-contained visual narrative designed to tell a specific story or explain a concept. A data dashboard is an interactive tool that provides a high-level overview of key metrics, allowing users to explore data and drill down into details." },
                        { q: "When would you use a heatmap vs. a scatter plot?", h: "A heatmap is used to visualize the magnitude of a phenomenon as color in two dimensions (e.g., correlation matrix, density of points). A scatter plot is used to show the relationship between two continuous variables for individual data points." },
                        { q: "How do you ensure your visualizations are actionable?", h: "Link visuals directly to business questions, highlight key insights, provide context and recommendations, and ensure users can easily interact to explore relevant segments or drill down for details. A good visualization should prompt a decision or action." },
                        { q: "Explain the concept of 'pre-attentive attributes' in data visualization.", h: "Pre-attentive attributes are visual properties that are processed by our brains almost instantly, without conscious effort. Examples include color hue, intensity, length, width, size, orientation, shape, and enclosure. Using them effectively helps guide the viewer's attention to important information quickly." },
                    ]
                },
                {
                    category: 'Behavioral & Situational',
                    id: 'behavioral',
                    questions: [
                        { q: "Describe a challenging data problem you faced and how you solved it.", h: "Use the STAR method: Situation (describe the context), Task (what was your goal?), Action (what steps did you take?), and Result (what was the outcome?). Focus on your analytical process and problem-solving skills." },
                        { q: "How do you ensure the accuracy and reliability of your data analysis?", h: "Mention data validation, checking for outliers, documenting your process, sense-checking results against domain knowledge, and having your analysis peer-reviewed." },
                        { q: "How do you explain complex technical concepts to a non-technical audience?", h: "Focus on using analogies, avoiding jargon, and connecting the findings to business impact and goals. Emphasize the 'so what?' of the analysis." },
                        { q: "How do you prioritize your analytical tasks when you have multiple requests?", h: "Prioritize based on business impact, urgency, data availability, and effort required. Communicate clearly with stakeholders about timelines and expectations." },
                        { q: "Describe a time you had to deal with ambiguous or messy data. How did you approach it?", h: "Focus on your systematic approach: understanding the data source, profiling the data, identifying inconsistencies, documenting assumptions, and communicating limitations to stakeholders. Emphasize iterative cleaning and validation." },
                        { q: "How do you stay updated with the latest trends and technologies in data analytics?", h: "Mention continuous learning through online courses, industry blogs, conferences, professional communities (e.g., LinkedIn, Kaggle), and hands-on projects. Show enthusiasm for learning." },
                        { q: "Tell me about a time you made a mistake in your analysis. How did you handle it?", h: "Be honest, take responsibility, explain the steps you took to identify and correct the mistake, and what you learned to prevent it from happening again. Emphasize your commitment to accuracy and continuous improvement." },
                        { q: "How do you handle feedback or criticism on your work?", h: "Demonstrate openness to feedback, a willingness to learn, and a focus on improving the outcome. Ask clarifying questions, consider different perspectives, and explain your rationale respectfully." },
                        { q: "Describe a project where you collaborated with a non-technical team. What was your role and how did you ensure successful communication?", h: "Highlight your ability to bridge the gap between technical and business teams. Discuss active listening, translating technical jargon, setting clear expectations, and focusing on business value." },
                        { q: "What are your strengths and weaknesses as a Data Analyst?", h: "For strengths, align them with the job description (e.g., problem-solving, SQL, communication). For weaknesses, choose a genuine one, but frame it positively by discussing steps you're taking to improve it (e.g., 'I sometimes get too focused on technical details, so I'm actively practicing summarizing insights for business users')." },
                    ]
                },
                {
                    category: 'Case Study / Problem Solving',
                    id: 'case-study',
                    questions: [
                        { q: "Our company is seeing a 10% drop in user engagement. How would you investigate this?", h: "Structure your approach. Start with clarifying questions (How is 'engagement' measured? When did the drop start? Does it affect all user segments?). Then, form hypotheses and outline the data you'd need to test them." },
                        { q: "How would you define and measure 'customer churn' for a subscription service?", h: "Churn rate is the percentage of customers who stop using a service over a given period. To measure it: (Churned Customers / Total Customers at Start of Period) * 100. Define what counts as a 'churned' customer (e.g., cancelled subscription, inactive for 90 days)." },
                        { q: "A new marketing campaign was launched. How would you measure its effectiveness?", h: "Define clear KPIs (e.g., conversion rate, customer acquisition cost, ROI). Compare performance of the campaign group vs. a control group (if A/B tested). Analyze data before and after the campaign. Consider segmenting results by channel, audience, etc." },
                        { q: "How would you approach forecasting sales for the next quarter?", h: "Start by gathering historical sales data and relevant external factors (e.g., seasonality, economic indicators, marketing spend). Choose an appropriate forecasting model (e.g., time series models like ARIMA, Prophet, or regression models). Evaluate model performance and communicate forecasts with confidence intervals." },
                        { q: "Given a dataset of customer demographics and purchase history, how would you identify high-value customers?", h: "Define 'high-value' (e.g., high total spend, high frequency, recent purchases - RFM analysis). Segment customers based on these criteria using clustering or simple rule-based methods. Analyze characteristics of each segment to tailor marketing strategies." },
                        { q: "How would you design a dashboard to track key business metrics for a product manager?", h: "Understand the product manager's goals and decisions. Identify key metrics (e.g., user acquisition, engagement, retention, conversion, revenue). Choose appropriate visualizations for each, ensure interactivity, and prioritize actionable insights over raw data." },
                        { q: "Imagine you're analyzing website traffic data. How would you identify bot traffic and remove it from your analysis?", h: "Look for unusual patterns: extremely high bounce rates, very short session durations, unusual user agent strings, IP addresses from known botnets, or traffic spikes from unexpected geographies. Use filtering rules based on these anomalies." },
                        { q: "How would you determine if a new website feature is cannibalizing an existing one?", h: "Analyze key metrics (e.g., usage, conversion) for both features before and after the new feature launch, ideally with A/B testing. Look for shifts in user behavior, not just overall performance. Segment users who interact with both features vs. only one." },
                        { q: "Your team needs to decide whether to invest more in mobile app development or website optimization. How would you use data to inform this decision?", h: "Compare user engagement, conversion rates, feature usage, and revenue generation across both platforms. Analyze user demographics and preferences for each. Identify pain points and opportunities on both, then quantify potential impact of improvements on each platform." },
                        { q: "Describe a situation where you had to present complex analytical findings to senior leadership. How did you structure your presentation?", h: "Focus on the 'so what?' and 'now what?'. Start with key takeaways and recommendations, then provide supporting data and visualizations. Keep it concise, use clear language, and be prepared for questions about methodology and assumptions." },
                    ]
                },
                {
                    category: 'Data Warehouse & ETL',
                    id: 'warehouse-etl',
                    questions: [
                        { q: "What is a Data Warehouse and why is it used?", h: "A data warehouse is a central repository of integrated data from one or more disparate sources. It's used for reporting and data analysis, and is a core component of business intelligence." },
                        { q: "Difference between OLTP and OLAP.", h: "OLTP (Online Transaction Processing) systems are designed for transactional data, focusing on fast inserts, updates, and deletes. OLAP (Online Analytical Processing) systems are designed for analytical queries, focusing on fast retrieval and aggregation of large amounts of historical data." },
                        { q: "Explain the ETL process.", h: "ETL stands for Extract, Transform, Load. Extract involves pulling data from source systems. Transform involves cleaning, standardizing, and aggregating data. Load involves moving the transformed data into the data warehouse." },
                        { q: "What is a data mart?", h: "A data mart is a subset of a data warehouse, designed for a specific department or business function (e.g., sales data mart, marketing data mart). It provides focused access to relevant data." },
                        { q: "Explain Star Schema vs. Snowflake Schema.", h: "Star schema: a central fact table surrounded by multiple dimension tables, with direct relationships. Snowflake schema: dimensions are normalized into multiple related tables, creating a more complex, snowflake-like structure." },
                        { q: "What are fact and dimension tables?", h: "Fact tables contain quantitative data (measures) about business processes. Dimension tables contain descriptive attributes related to the data in the fact table (e.g., product name, customer demographics)." },
                        { q: "What are some common challenges in ETL?", h: "Data quality issues (inconsistent formats, missing values), performance bottlenecks, managing data volume, ensuring data security, and handling schema changes in source systems." },
                        { q: "What is data governance?", h: "Data governance is the overall management of the availability, usability, integrity, and security of data in an enterprise. It involves defining policies, standards, and processes for data handling." },
                        { q: "Explain the concept of a 'Data Lake' and how it differs from a 'Data Warehouse'.", h: "A Data Lake stores raw, unstructured, and semi-structured data at scale, often before it's fully defined or transformed. A Data Warehouse stores structured, processed data optimized for reporting and analysis. Data lakes are for 'schema-on-read', warehouses for 'schema-on-write'." },
                        { q: "What is Change Data Capture (CDC) and why is it important in ETL?", h: "CDC is a set of software design patterns used to determine and track the data that has changed so that action can be taken with the changed data. It's important in ETL for efficient data loading, as it only processes new or modified data, reducing load times and resource usage." },
                        { q: "What is data lineage and why is it important?", h: "Data lineage tracks the journey of data from its origin to its current state, including all transformations and movements. It's crucial for auditing, compliance, troubleshooting data quality issues, and understanding the reliability of reports." },
                        { q: "Describe the difference between a 'full load' and an 'incremental load' in ETL.", h: "A full load involves loading all data from the source to the target every time. An incremental load only loads new or changed data since the last load. Incremental loads are more efficient for large datasets and frequent updates." },
                        { q: "What is a 'Staging Area' in ETL and why is it used?", h: "A staging area (or landing zone) is an intermediate storage area used during the ETL process to temporarily hold data extracted from source systems before it's transformed and loaded into the data warehouse. It allows for data cleaning, validation, and transformation without impacting source systems or the data warehouse directly." },
                        { q: "Explain the concept of 'Data Quality' in a data warehouse context.", h: "Data quality refers to the fitness of data for its intended use. In a data warehouse, it means data is accurate, complete, consistent, timely, and valid. Poor data quality leads to unreliable reports and poor business decisions." },
                        { q: "What are 'Metadata' and 'Master Data Management' (MDM) in data warehousing?", h: "Metadata is 'data about data' (e.g., data types, column descriptions, source systems). MDM is a technology-enabled discipline in which business and IT work together to ensure the uniformity, accuracy, stewardship, semantic consistency, and accountability of the enterprise's official shared master data assets." },
                        { q: "How do you handle data security and compliance in a data warehouse?", h: "Implement access controls (role-based security), data encryption (at rest and in transit), data masking/anonymization for sensitive data, regular security audits, and ensure adherence to regulations like GDPR, HIPAA, CCPA." },
                    ]
                },
                {
                    category: 'Cloud & Version Control',
                    id: 'cloud-git',
                    questions: [
                        { q: "What are the benefits of using cloud platforms (e.g., AWS, Azure, GCP) for data storage and analytics?", h: "Scalability (easily adjust resources), cost-effectiveness (pay-as-you-go), reliability, security, and access to a wide range of managed services (databases, data lakes, analytics tools) without managing infrastructure." },
                        { q: "Can you name some common cloud data storage services?", h: "AWS: S3 (object storage), RDS (relational databases), Redshift (data warehouse). Azure: Blob Storage, Azure SQL Database, Azure Synapse Analytics. GCP: Cloud Storage, Cloud SQL, BigQuery (data warehouse)." },
                        { q: "What is Git and why is it important for a Data Analyst?", h: "Git is a distributed version control system used for tracking changes in source code during software development. For a Data Analyst, it's crucial for managing SQL queries, Python/R scripts, Jupyter notebooks, collaborating with teammates, and reverting to previous versions of work." },
                        { q: "Explain basic Git commands like `clone`, `pull`, `push`, `commit`.", h: "`git clone`: copies a remote repository to your local machine. `git pull`: fetches changes from the remote and merges them into your local branch. `git push`: uploads your local commits to the remote repository. `git commit`: saves changes to your local repository with a message." },
                        { q: "What is the purpose of a `.gitignore` file?", h: "A `.gitignore` file specifies intentionally untracked files that Git should ignore. This is useful for excluding temporary files, compiled code, or sensitive data from your repository." },
                        { q: "Explain the concept of 'Infrastructure as Code' (IaC) in cloud environments.", h: "IaC is the management of infrastructure (networks, virtual machines, load balancers) in a descriptive model, using the same versioning as source code. It allows for automated provisioning, consistency, and reproducibility of environments." },
                        { q: "What are containers (e.g., Docker) and how are they relevant for data professionals?", h: "Containers package an application and all its dependencies into a single unit, ensuring it runs consistently across different environments. For data professionals, containers simplify deployment of analytical applications, ensure reproducibility of analyses, and manage dependencies for Python/R environments." },
                        { q: "How do you manage sensitive data (e.g., PII) in a cloud environment?", h: "Use encryption at rest and in transit, implement strict access controls (IAM roles, least privilege), anonymize or pseudonymize data where possible, and adhere to data residency and compliance regulations (e.g., GDPR, CCPA)." },
                        { q: "What is the difference between a 'branch' and 'master/main' in Git?", h: "The `master` (or `main`) branch is typically the default and represents the stable version of the project. A `branch` is a separate line of development that allows you to work on new features or bug fixes without affecting the main codebase until merged." },
                        { q: "What is a 'data pipeline' in a cloud context and why is it important?", h: "A data pipeline is a series of steps to move data from source systems to a destination (e.g., data warehouse, data lake) for analysis. In the cloud, this involves services like AWS Glue, Azure Data Factory, or GCP Dataflow. They are crucial for automating data ingestion, transformation, and ensuring data is readily available for analysis." },
                        { q: "Explain the concept of 'Serverless Computing' (e.g., AWS Lambda, Azure Functions) and its relevance to data analytics.", h: "Serverless computing allows you to run code without provisioning or managing servers. For data analytics, it's useful for event-driven data processing (e.g., triggering a function when a new file lands in S3), small ETL tasks, or creating API endpoints for data services, offering scalability and cost-efficiency." },
                        { q: "How do you ensure data security in a cloud data environment?", h: "Implement robust Identity and Access Management (IAM), encrypt data at rest and in transit, use network security (VPCs, firewalls), regularly audit access logs, and ensure compliance with relevant industry standards and regulations." },
                        { q: "What is the purpose of a 'pull request' (or 'merge request') in Git workflows?", h: "A pull request is a feature in Git-based platforms (GitHub, GitLab, Bitbucket) that allows developers to notify team members about changes they've pushed to a branch and request reviews before merging those changes into another branch (e.g., `main`). It facilitates code review, collaboration, and quality control." },
                    ]
                },
                {
                    category: 'Business Acumen & Storytelling',
                    id: 'business-storytelling',
                    questions: [
                        { q: "How do you translate a business problem into an analytical question?", h: "Start by understanding the business objective, then break it down into measurable components. For example, 'Why are sales declining?' becomes 'What factors correlate with sales decline, and which segments are most affected?'" },
                        { q: "What is a KPI (Key Performance Indicator) and how do you define a good one?", h: "A KPI is a measurable value that demonstrates how effectively a company is achieving key business objectives. Good KPIs are SMART (Specific, Measurable, Achievable, Relevant, Time-bound)." },
                        { q: "Describe a time you had to influence a decision using data.", h: "Use the STAR method. Focus on how you presented the data, anticipated objections, and tailored your message to the audience to drive action. Emphasize the business impact of your findings." },
                        { q: "How do you handle a situation where your data analysis contradicts a stakeholder's intuition or existing belief?", h: "Present your findings clearly and objectively, focusing on the methodology and evidence. Be open to feedback and further investigation, but stand firm on data-backed conclusions. Offer to explore their hypothesis with data." },
                        { q: "How do you prioritize your analytical tasks when you have multiple requests?", h: "Prioritize based on business impact, urgency, data availability, and effort required. Communicate clearly with stakeholders about timelines and expectations." },
                        { q: "What is the difference between descriptive, diagnostic, predictive, and prescriptive analytics?", h: "Descriptive: What happened? (e.g., sales reports). Diagnostic: Why did it happen? (e.g., root cause analysis). Predictive: What will happen? (e.g., sales forecasting). Prescriptive: What should we do? (e.g., optimization, recommendations)." },
                        { q: "How do you ensure data privacy and security in your analysis?", h: "Adhere to company policies and regulations (e.g., GDPR, HIPAA). Anonymize or pseudonymize sensitive data, use secure data storage, control access permissions, and avoid sharing raw sensitive data unnecessarily." },
                        { q: "How do you approach a project where the data is incomplete or of poor quality?", h: "Communicate the issue early. Work with data owners to understand the root cause. Document limitations. Implement data cleaning and validation steps. Make informed assumptions and state them clearly. Focus on extracting what insights you can, even with imperfect data." },
                        { q: "Describe your experience with A/B testing beyond just the statistical aspect. How do you ensure business impact?", h: "Beyond statistics, focus on defining clear business goals, ensuring proper experimental design (e.g., avoiding novelty effects), monitoring during the test, iterating based on results, and communicating actionable recommendations to business stakeholders." },
                        { q: "How do you measure the ROI (Return on Investment) of a data analytics project?", h: "Quantify the business value generated (e.g., increased revenue, reduced costs, improved efficiency). Compare this value to the resources invested (time, tools, personnel). Present findings in financial terms that resonate with business leaders." },
                        { q: "What steps do you take to ensure the ethical use of data in your analysis?", h: "Prioritize data privacy, avoid discriminatory outcomes from models, ensure transparency in data collection and usage, consider potential societal impacts of your analysis, and adhere to ethical guidelines and regulations." },
                        { q: "How do you handle a situation where stakeholders disagree on the interpretation of data?", h: "Facilitate a discussion by re-presenting the data clearly, highlighting different interpretations, and walking through the methodology. Focus on facts and evidence. If necessary, propose further analysis to resolve the disagreement." },
                        { q: "Describe a time you had to adapt your analytical approach due to new information or changing business requirements.", h: "Show flexibility and problem-solving. Explain how you reassessed the situation, adjusted your methodology, communicated changes to stakeholders, and managed expectations to still deliver valuable insights." },
                    ]
                }
            ];

            const navigation = document.getElementById('navigation');
            const questionsContainer = document.getElementById('questions-container');
            const mainHeader = document.getElementById('main-header'); // Corrected assignment
            const searchInput = document.getElementById('search-input');
            const welcomeSection = document.getElementById('welcome-section');
            const noResults = document.getElementById('no-results');
            const sidebar = document.getElementById('sidebar');
            const menuButton = document.getElementById('menu-button');

            const filterAllBtn = document.getElementById('filter-all');
            const filterPreparedBtn = document.getElementById('filter-prepared');
            const filterUnpreparedBtn = document.getElementById('filter-unprepared');

            let currentFilter = 'all'; // 'all', 'prepared', 'unprepared'

            // --- Local Storage Functions ---
            const saveProgress = () => {
                const progress = {};
                document.querySelectorAll('.question-card').forEach(card => {
                    const questionText = card.querySelector('p[data-question-text]').dataset.questionText;
                    progress[questionText] = card.classList.contains('prepared');
                });
                localStorage.setItem('interviewProgress', JSON.stringify(progress));
            };

            const loadProgress = () => {
                const progress = JSON.parse(localStorage.getItem('interviewProgress')) || {};
                document.querySelectorAll('.question-card').forEach(card => {
                    const questionText = card.querySelector('p[data-question-text]').dataset.questionText;
                    if (progress[questionText]) {
                        card.classList.add('prepared');
                        card.querySelector('.prepared-checkbox').checked = true;
                    } else {
                        card.classList.remove('prepared');
                        card.querySelector('.prepared-checkbox').checked = false;
                    }
                });
            };

            const applyFiltersAndSearch = () => {
                const searchTerm = searchInput.value.toLowerCase();
                let resultsFound = false;

                document.querySelectorAll('.question-category').forEach(cat => cat.classList.add('hidden')); 
                welcomeSection.classList.add('hidden');

                interviewData.forEach(categoryData => {
                    let categoryHasVisibleQuestions = false;
                    const categorySectionElement = document.getElementById(categoryData.id);
                    if (!categorySectionElement) return; 

                    categoryData.questions.forEach(item => {
                        const questionText = item.q.toLowerCase();
                        const hintText = item.h.toLowerCase();
                        const questionElement = categorySectionElement.querySelector(`[data-question-text="${item.q.replace(/"/g, '&quot;')}"]`)?.closest('.question-card');
                        
                        if (questionElement) { 
                            const isPrepared = questionElement.classList.contains('prepared');
                            const matchesSearch = questionText.includes(searchTerm) || hintText.includes(searchTerm);
                            let shouldShow = false;

                            if (currentFilter === 'all' && matchesSearch) {
                                shouldShow = true;
                            } else if (currentFilter === 'prepared' && isPrepared && matchesSearch) {
                                shouldShow = true;
                            } else if (currentFilter === 'unprepared' && !isPrepared && matchesSearch) {
                                shouldShow = true;
                            }

                            if (shouldShow) {
                                questionElement.classList.remove('hidden');
                                categoryHasVisibleQuestions = true;
                                resultsFound = true;
                            } else {
                                questionElement.classList.add('hidden');
                            }
                        }
                    });
                    if (categoryHasVisibleQuestions) {
                        categorySectionElement.classList.remove('hidden'); 
                    }
                });
                
                noResults.classList.toggle('hidden', resultsFound);
            };

            const renderQuestions = (data) => {
                questionsContainer.innerHTML = '';
                data.forEach(categoryData => {
                    const categorySection = document.createElement('section');
                    categorySection.id = categoryData.id;
                    categorySection.className = 'question-category';

                    categoryData.questions.forEach((item, index) => {
                        const questionDiv = document.createElement('div');
                        questionDiv.className = 'question-card bg-white p-4 rounded-lg shadow-sm cursor-pointer';
                        questionDiv.innerHTML = `
                            <div class="flex items-start justify-between">
                                <p class="font-medium text-gray-800 flex-1 pr-4" data-question-text="${item.q.replace(/"/g, '&quot;')}">${item.q}</p>
                                <div class="flex items-center">
                                    <input type="checkbox" class="h-5 w-5 rounded text-blue-600 focus:ring-blue-500 border-gray-300 mr-3 prepared-checkbox" title="Mark as prepared">
                                    <span class="text-2xl text-gray-400 toggle-hint-icon">+</span>
                                </div>
                            </div>
                            <div class="hint mt-2 pl-1">
                                <p class="text-sm text-gray-600 border-l-2 border-blue-200 pl-3 py-1 bg-blue-50/50 rounded-r"><strong>Hint:</strong> ${item.h.replace(/```sql/g, '<pre class="code-block" data-lang="sql">').replace(/```excel/g, '<pre class="code-block" data-lang="excel">').replace(/```dax/g, '<pre class="code-block" data-lang="dax">').replace(/```powerquery-m/g, '<pre class="code-block" data-lang="powerquery-m">').replace(/```python/g, '<pre class="code-block" data-lang="python">').replace(/```/g, '</pre>')}</p>
                            </div>
                        `;
                        categorySection.appendChild(questionDiv);
                    });
                    questionsContainer.appendChild(categorySection);
                });
                addEventListenersToCards();
                loadProgress(); // Load progress after rendering cards
                addCopyButtons(); // Add copy buttons after rendering hints
                applyFiltersAndSearch(); // Apply filters and search after loading progress
            };

            const populateNav = () => {
                navigation.innerHTML = ''; // Clear existing navigation
                interviewData.forEach(item => {
                    const link = document.createElement('a');
                    link.href = `#${item.id}`;
                    link.textContent = item.category;
                    link.className = 'nav-link block p-2 rounded-md hover:bg-gray-100';
                    link.dataset.target = item.id;
                    navigation.appendChild(link);
                });
            };
            
            const handleNavClick = (targetId) => {
                document.querySelectorAll('.question-category').forEach(sec => sec.classList.add('hidden'));
                
                const targetSection = document.getElementById(targetId);
                if (targetSection) {
                    targetSection.classList.remove('hidden');
                }
                
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.toggle('active', link.dataset.target === targetId);
                });
                
                const category = interviewData.find(cat => cat.id === targetId);
                mainHeader.textContent = category ? category.category : 'Welcome';
                welcomeSection.classList.add('hidden');
                searchInput.value = ''; // Clear search when navigating categories
                currentFilter = 'all'; // Reset filter
                filterAllBtn.classList.add('active');
                filterPreparedBtn.classList.remove('active');
                filterUnpreparedBtn.classList.remove('active');
                applyFiltersAndSearch();
            }

            navigation.addEventListener('click', (e) => {
                e.preventDefault();
                if (e.target.classList.contains('nav-link')) {
                    const targetId = e.target.dataset.target;
                    handleNavClick(targetId);
                    if(window.innerWidth < 768) {
                        sidebar.classList.add('hidden');
                    }
                }
            });

            const addEventListenersToCards = () => {
                questionsContainer.addEventListener('click', (e) => {
                    const card = e.target.closest('.question-card');
                    if (!card) return;

                    if(e.target.classList.contains('prepared-checkbox')) {
                         card.classList.toggle('prepared');
                         saveProgress(); // Save progress on checkbox change
                         applyFiltersAndSearch(); // Re-apply filters to update view
                    } else {
                        const hint = card.querySelector('.hint');
                        const icon = card.querySelector('.toggle-hint-icon');
                        hint.classList.toggle('show');
                        icon.textContent = hint.classList.contains('show') ? '−' : '+';
                    }
                });
            }

            searchInput.addEventListener('input', applyFiltersAndSearch);

            filterAllBtn.addEventListener('click', () => {
                currentFilter = 'all';
                filterAllBtn.classList.add('active');
                filterPreparedBtn.classList.remove('active');
                filterUnpreparedBtn.classList.remove('active');
                applyFiltersAndSearch();
            });

            filterPreparedBtn.addEventListener('click', () => {
                currentFilter = 'prepared';
                filterAllBtn.classList.remove('active');
                filterPreparedBtn.classList.add('active');
                filterUnpreparedBtn.classList.remove('active');
                applyFiltersAndSearch();
            });

            filterUnpreparedBtn.addEventListener('click', () => {
                currentFilter = 'unprepared';
                filterAllBtn.classList.remove('active');
                filterPreparedBtn.classList.remove('active');
                filterUnpreparedBtn.classList.add('active');
                applyFiltersAndSearch();
            });

            const addCopyButtons = () => {
                document.querySelectorAll('.code-block').forEach(codeBlock => {
                    const button = document.createElement('button');
                    button.className = 'copy-button';
                    button.textContent = 'Copy';
                    codeBlock.appendChild(button);

                    button.addEventListener('click', () => {
                        const codeToCopy = codeBlock.textContent.replace('Copy', '').trim(); // Remove 'Copy' text
                        navigator.clipboard.writeText(codeToCopy).then(() => {
                            button.textContent = 'Copied!';
                            setTimeout(() => {
                                button.textContent = 'Copy';
                            }, 1500);
                        }).catch(err => {
                            console.error('Failed to copy: ', err);
                        });
                    });
                });
            };
            
            let overviewChartInstance = null; 

            const renderChart = () => {
                if (overviewChartInstance) {
                    overviewChartInstance.destroy(); 
                }
                const ctx = document.getElementById('overviewChart').getContext('2d');
                const data = {
                    labels: interviewData.map(d => {
                        const label = d.category;
                        const words = label.split(' ');
                        let wrappedLabel = '';
                        let currentLine = '';
                        for (let i = 0; i < words.length; i++) {
                            if ((currentLine + words[i]).length > 16 && currentLine.length > 0) {
                                wrappedLabel += currentLine + '\n';
                                currentLine = words[i] + ' ';
                            } else {
                                currentLine += words[i] + ' ';
                            }
                        }
                        wrappedLabel += currentLine;
                        return wrappedLabel.trim();
                    }),
                    datasets: [{
                        label: 'Number of Questions',
                        data: interviewData.map(d => d.questions.length),
                        backgroundColor: [
                            '#3b82f6', '#10b981', '#f97316', '#8b5cf6', 
                            '#ec4899', '#f59e0b', '#6366f1', '#ef4444',
                            '#06b6d4', '#a855f7' 
                        ],
                        hoverOffset: 4
                    }]
                };
                overviewChartInstance = new Chart(ctx, { 
                    type: 'doughnut',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    padding: 15
                                }
                            },
                            title: {
                                display: true,
                                text: 'Question Distribution by Category',
                                font: { size: 16 }
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(context) {
                                        return context[0].label.replace(/\n/g, ' '); 
                                    }
                                }
                            }
                        }
                    }
                });
            };

            menuButton.addEventListener('click', () => {
                sidebar.classList.toggle('hidden');
            });


            populateNav();
            renderQuestions(interviewData);
            document.querySelectorAll('.question-category').forEach(sec => sec.classList.add('hidden'));
            welcomeSection.classList.remove('hidden'); 
            renderChart();
        });
    </script>
</body>
</html>
