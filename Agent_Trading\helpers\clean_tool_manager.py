"""
Clean Tool Manager with only essential tools for crypto and Indian markets.
Removes unnecessary tools and adds ChromaDB query capability.
"""

import logging
import time
from typing import Dict, List, Callable, Any
from datetime import datetime
import google.generativeai as genai
from .indian_market_tools import indian_market_analyzer
from .duckduckgo_news import duckduckgo_news
from .chromadb_rag_system import ChromaDBTradingRAGSystem
from .yfinance_integration import get_comprehensive_news, get_indian_market_context
from .llm_logger import get_llm_logger

logger = logging.getLogger(__name__)
llm_logger = get_llm_logger()

class ToolResult:
    """Standardized tool result wrapper."""
    def __init__(self, success: bool, data: Any, error: str = None, execution_time: float = 0.0):
        self.success = success
        self.data = data
        self.error = error
        self.execution_time = execution_time

class CleanToolRegistry:
    """Clean registry with only essential tools."""
    
    def __init__(self):
        self.tools: Dict[str, Callable] = {}
        self.tool_definitions: List[genai.protos.FunctionDeclaration] = []
        self.tool_stats: Dict[str, Dict[str, Any]] = {}
        self.indian_analyzer = indian_market_analyzer
        self.rag_system = ChromaDBTradingRAGSystem()
        self._register_essential_tools()
        self._register_economic_calendar_tool()
    
    def _register_essential_tools(self):
        """Register only essential tools for trading analysis."""
        
        # 1. Symbol Detection Tool - REMOVED (redundant with LLM vision analysis)
        
        # 2. Enhanced Market News Tool (DuckDuckGo + YFinance)
        self.register_tool(
            name="get_comprehensive_market_news",
            function=self._get_comprehensive_market_news,
            description="Get comprehensive market news from multiple sources (DuckDuckGo + YFinance) for any symbol.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Trading symbol (e.g., 'BTC', 'NIFTY')"}
                },
                "required": ["symbol"]
            }
        )
        
        # 3. Market Context Tool
        self.register_tool(
            name="get_market_context_summary",
            function=self._get_market_context_summary,
            description="Get market context and technical analysis summary.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Trading symbol"}
                },
                "required": ["symbol"]
            }
        )
        
        # 4. Indian Market Specific Tool (only for Indian symbols)
        self.register_tool(
            name="get_fii_dii_flows",
            function=self._get_fii_dii_flows,
            description="Get FII/DII flows for Indian market analysis (use only for Indian symbols like NIFTY, BANKNIFTY).",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Indian market symbol"}
                },
                "required": ["symbol"]
            }
        )
        
        # 5. ChromaDB Memory Query Tool - LLM decides what to search
        self.register_tool(
            name="query_trading_memory",
            function=self._query_trading_memory,
            description="Query historical trading analysis and patterns from memory database. Use this to find similar market conditions, patterns, or previous analysis.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Trading symbol to search for"},
                    "query": {"type": "string", "description": "What to search for (e.g., 'similar patterns', 'support resistance levels', 'market conditions')"},
                    "market_type": {"type": "string", "description": "Market type: 'crypto', 'indian', or 'general'"},
                    "analysis_type": {"type": "string", "description": "Analysis type: 'positional', 'scalp', or 'general'"}
                },
                "required": ["symbol", "query"]
            }
        )
    
    def register_tool(self, name: str, function: Callable, description: str, parameters: dict):
        """Register a tool with the registry."""
        self.tools[name] = function
        
        # Create Gemini function declaration
        func_declaration = genai.protos.FunctionDeclaration(
            name=name,
            description=description,
            parameters=genai.protos.Schema(
                type=genai.protos.Type.OBJECT,
                properties={
                    prop_name: genai.protos.Schema(
                        type=genai.protos.Type.STRING if prop_def["type"] == "string" else genai.protos.Type.NUMBER,
                        description=prop_def.get("description", "")
                    )
                    for prop_name, prop_def in parameters["properties"].items()
                },
                required=parameters.get("required", [])
            )
        )
        
        self.tool_definitions.append(func_declaration)
        self.tool_stats[name] = {"calls": 0, "successes": 0, "failures": 0, "avg_execution_time": 0.0, "total_time": 0.0}
        logger.info(f"Registered tool: {name}")
    
    def execute_tool(self, name: str, **kwargs) -> ToolResult:
        """Execute a tool and return standardized result."""
        if name not in self.tools:
            return ToolResult(False, None, f"Tool {name} not found")
        
        start_time = time.time()
        self.tool_stats[name]["calls"] += 1
        
        try:
            result = self.tools[name](**kwargs)
            execution_time = time.time() - start_time
            self.tool_stats[name]["calls"] += 1
            self.tool_stats[name]["successes"] += 1
            self.tool_stats[name]["total_time"] += execution_time
            self.tool_stats[name]["avg_execution_time"] = (
                self.tool_stats[name]["total_time"] / self.tool_stats[name]["successes"]
            )
            logger.info(f"Tool {name} executed successfully in {execution_time:.2f}s")
            return ToolResult(True, result, execution_time=execution_time)
        except Exception as e:
            execution_time = time.time() - start_time
            self.tool_stats[name]["calls"] += 1
            self.tool_stats[name]["failures"] += 1
            logger.error(f"Tool {name} failed: {str(e)}")
            return ToolResult(False, None, str(e), execution_time)
    
    # Tool implementations
    # _detect_trading_symbol REMOVED - redundant with LLM vision analysis
    
    def _get_comprehensive_market_news(self, symbol: str) -> dict:
        """Get comprehensive market news from multiple sources."""
        try:
            # Log tool call
            start_time = time.time()

            # Get news from both DuckDuckGo and YFinance
            duckduckgo_result = duckduckgo_news.get_comprehensive_market_news(symbol)
            yfinance_result = get_comprehensive_news(symbol)

            execution_time = time.time() - start_time

            # Combine results
            combined_result = {
                "symbol": symbol,
                "duckduckgo_news": duckduckgo_result,
                "yfinance_news": yfinance_result,
                "data_sources": ["duckduckgo", "yfinance"],
                "timestamp": datetime.now().isoformat()
            }

            # Log the tool call
            llm_logger.log_tool_call(
                tool_name="get_comprehensive_market_news",
                tool_args={"symbol": symbol},
                tool_response=combined_result,
                success=True,
                execution_time=execution_time,
                context={"symbol": symbol}
            )

            return combined_result

        except Exception as e:
            # Log error
            llm_logger.log_tool_call(
                tool_name="get_comprehensive_market_news",
                tool_args={"symbol": symbol},
                tool_response=None,
                success=False,
                error=str(e),
                context={"symbol": symbol}
            )
            return {"error": str(e), "symbol": symbol}
    
    def _get_market_context_summary(self, symbol: str) -> dict:
        """Get enhanced market context summary with YFinance integration."""
        try:
            start_time = time.time()

            # Get data from both sources
            indian_analyzer_result = self.indian_analyzer.get_market_context_summary(symbol)

            # For Indian markets, also get YFinance context
            if symbol.upper() in ["NIFTY", "NIFTY50", "BANKNIFTY", "BANK NIFTY"]:
                yfinance_context = get_indian_market_context(symbol)
                combined_result = {
                    "symbol": symbol,
                    "indian_analyzer": indian_analyzer_result,
                    "yfinance_context": yfinance_context,
                    "data_sources": ["indian_analyzer", "yfinance"],
                    "timestamp": datetime.now().isoformat()
                }
            else:
                combined_result = indian_analyzer_result

            execution_time = time.time() - start_time

            # Log the tool call
            llm_logger.log_tool_call(
                tool_name="get_market_context_summary",
                tool_args={"symbol": symbol},
                tool_response=combined_result,
                success=True,
                execution_time=execution_time,
                context={"symbol": symbol}
            )

            return combined_result

        except Exception as e:
            llm_logger.log_tool_call(
                tool_name="get_market_context_summary",
                tool_args={"symbol": symbol},
                tool_response=None,
                success=False,
                error=str(e),
                context={"symbol": symbol}
            )
            return {"error": str(e), "symbol": symbol}
    
    def _get_fii_dii_flows(self, symbol: str) -> dict:
        """Get FII/DII flows for Indian markets using hybrid approach."""
        try:
            start_time = time.time()

            # 🇮🇳 Only process for Indian market symbols
            indian_symbols = ["NIFTY", "BANKNIFTY", "^NSEI", "^NSEBANK", "NIFTY50", "BANK NIFTY"]
            if not any(indian_sym in symbol.upper() for indian_sym in indian_symbols):
                logger.warning(f"⚠️ FII/DII flows requested for non-Indian symbol: {symbol}")
                return {
                    "success": False,
                    "error": f"FII/DII flows are only available for Indian market symbols, not {symbol}",
                    "symbol": symbol
                }

            logger.info(f"🇮🇳 Getting FII/DII flows for Indian market symbol: {symbol}")

            # Get FII/DII news from DuckDuckGo
            fii_dii_news = duckduckgo_news.get_fii_dii_news()

            # Get Indian market context from yfinance for additional data
            from .yfinance_integration import get_indian_market_context
            market_context = get_indian_market_context(symbol)

            # Combine both sources
            combined_result = {
                "success": True,
                "symbol": symbol,
                "fii_dii_news": fii_dii_news,
                "market_context": market_context,
                "data_sources": ["DuckDuckGo News", "YFinance"],
                "timestamp": datetime.now().isoformat(),
                "analysis_note": "FII/DII flows analysis combining news sentiment and market data"
            }

            execution_time = time.time() - start_time

            # Log the tool call
            llm_logger.log_tool_call(
                tool_name="get_fii_dii_flows",
                tool_args={"symbol": symbol},
                tool_response=combined_result,
                success=True,
                execution_time=execution_time,
                context={"symbol": symbol, "market_type": "indian"}
            )

            return combined_result

        except Exception as e:
            llm_logger.log_tool_call(
                tool_name="get_fii_dii_flows",
                tool_args={"symbol": symbol},
                tool_response=None,
                success=False,
                error=str(e),
                context={"symbol": symbol}
            )
            return {"error": str(e), "symbol": symbol}
    
    def _query_trading_memory(self, symbol: str, query: str, market_type: str = "general", analysis_type: str = "general") -> dict:
        """Query ChromaDB for historical trading patterns and analysis."""
        try:
            memory_results = self.rag_system.get_contextual_memory(
                symbol=symbol,
                mode=analysis_type,
                market=market_type,
                query=query,
                n_results=5
            )
            
            return {
                "success": True,
                "memory_results": memory_results,
                "query_used": query,
                "symbol": symbol,
                "context": f"Found historical analysis for {symbol} related to: {query}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Memory query failed: {str(e)}",
                "query_used": query,
                "symbol": symbol
            }
    
    def get_tool_definitions(self) -> List[genai.protos.FunctionDeclaration]:
        """Get all tool definitions for Gemini."""
        return self.tool_definitions
    
    def get_tool_names(self) -> List[str]:
        """Get list of all registered tool names."""
        return list(self.tools.keys())
    
    def get_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get tool usage statistics."""
        return self.tool_stats

    def _register_economic_calendar_tool(self):
        """Register economic calendar tool for major event detection."""
        from .economic_calendar_tool import get_economic_calendar_risk

        def get_economic_calendar_risk_wrapper(symbol: str, market_type: str = "crypto") -> Dict[str, Any]:
            """Economic calendar risk assessment wrapper."""
            start_time = time.time()
            try:
                result = get_economic_calendar_risk(symbol, market_type)
                execution_time = time.time() - start_time

                # Log the tool call
                llm_logger.log_tool_call(
                    tool_name="get_economic_calendar_risk",
                    tool_args={"symbol": symbol, "market_type": market_type},
                    tool_response=result,
                    execution_time=execution_time
                )

                logger.info(f"Tool get_economic_calendar_risk executed successfully in {execution_time:.2f}s")
                return result

            except Exception as e:
                execution_time = time.time() - start_time
                error_result = {"success": False, "error": str(e), "symbol": symbol}

                llm_logger.log_tool_call(
                    tool_name="get_economic_calendar_risk",
                    tool_args={"symbol": symbol, "market_type": market_type},
                    tool_response=error_result,
                    execution_time=execution_time
                )

                logger.error(f"Tool get_economic_calendar_risk failed: {e}")
                return error_result

        # Register the tool
        tool_name = "get_economic_calendar_risk"
        self.tools[tool_name] = get_economic_calendar_risk_wrapper

        # Create function declaration for Gemini
        function_declaration = genai.protos.FunctionDeclaration(
            name=tool_name,
            description="Check for major economic events today that could impact trading decisions. Provides risk assessment and trading advisory based on economic calendar.",
            parameters=genai.protos.Schema(
                type=genai.protos.Type.OBJECT,
                properties={
                    "symbol": genai.protos.Schema(
                        type=genai.protos.Type.STRING,
                        description="Trading symbol (e.g., 'BTC/USD', 'NIFTY', 'BANKNIFTY')"
                    ),
                    "market_type": genai.protos.Schema(
                        type=genai.protos.Type.STRING,
                        description="Market type: 'crypto' for cryptocurrency, 'indian' for Indian markets, 'us' for US markets",
                        enum=["crypto", "indian", "us"]
                    )
                },
                required=["symbol"]
            )
        )

        self.tool_definitions.append(function_declaration)
        self.tool_stats[tool_name] = {"calls": 0, "successes": 0, "failures": 0, "total_time": 0.0}
        logger.info(f"Registered tool: {tool_name}")

# Global clean tool registry instance
clean_tool_registry = CleanToolRegistry()

def get_clean_tool_registry() -> CleanToolRegistry:
    """Get the clean tool registry instance."""
    return clean_tool_registry
