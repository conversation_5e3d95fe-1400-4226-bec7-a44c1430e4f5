#!/usr/bin/env python3
"""
🎯 FOCUSED AI TRADING ANALYSIS SYSTEM
Clean LangGraph implementation with only essential features:
- Chart analysis with symbol detection
- Parallel tool execution (market data, news, FII/DII)
- Tool summarization with Gemini Flash
- RAG integration for historical context
- Final analysis with sophisticated prompts
- Structured JSON output for trading signals
"""

import json
import logging
import time
from typing import List, Dict, Any, Optional, TypedDict
from datetime import datetime
import google.generativeai as genai
from langgraph.graph import StateGraph, END
from .llm_logger import get_llm_logger
from .workflow_logger import workflow_logger
from .trade_result_storage import get_trade_storage
from .gemini_rate_limiter import get_rate_limiter

logger = logging.getLogger(__name__)
llm_logger = get_llm_logger()

class CompleteTradingAnalysisState(TypedDict):
    """🎯 FOCUSED STATE SCHEMA - Essential trading analysis state."""
    # Input
    chart_images: List[bytes]
    analysis_mode: str  # "positional" or "scalp"
    user_query: Optional[str]
    market_specialization: str  # "Crypto", "Indian Market", "General"

    # Vision Analysis
    detected_symbol: Optional[str]
    market_type: Optional[str]  # "crypto", "indian", "us"
    chart_patterns: List[str]
    support_levels: List[float]
    resistance_levels: List[float]
    timeframe: Optional[str]

    # Tool Data (Raw)
    market_data: Dict[str, Any]
    news_data: Dict[str, Any]
    fii_dii_data: Dict[str, Any]
    historical_patterns: Dict[str, Any]

    # Tool Data (Summarized)
    summarized_tool_data: Dict[str, str]

    # Analysis Results
    trading_signals: Dict[str, Any]
    analysis: str

    # Workflow Control
    workflow_status: str
    progress_messages: List[str]
    error: Optional[str]
    tool_usage_log: List[Dict[str, Any]]

class CompleteLangGraphTradingWorkflow:
    """🎯 FOCUSED AI TRADING ANALYSIS SYSTEM - Clean and efficient implementation."""

    def __init__(self, google_api_key: str, tier: str = "free", preferred_model: str = "gemini-2.5-pro"):
        """Initialize focused trading workflow with essential features only."""
        self.google_api_key = google_api_key
        genai.configure(api_key=google_api_key)

        # 🚦 Initialize rate limiter with official Google limits
        self.rate_limiter = get_rate_limiter(tier)
        logger.info(f"🚦 Rate limiter initialized for {tier.upper()} tier")

        # 🤖 Smart model selection with rate limiting
        self.preferred_model = preferred_model
        self.model = self._get_best_available_model()
        logger.info(f"🤖 Using model: {self.model.model_name}")

        # Initialize all components from old system
        self._initialize_tools()
        self._initialize_prompts()
        self._initialize_optimization_systems()

        # Create clean, focused workflow
        self.workflow = self._create_focused_workflow()

    def _get_best_available_model(self):
        """🤖 Get best available model based on rate limits and preferences."""
        # Model priority based on user preference and rate limits
        model_options = [
            ("gemini-2.5-pro", "gemini-2.5-pro"),
            ("gemini-2.5-flash", "gemini-2.5-flash"),
            ("gemini-2.0-flash", "gemini-2.0-flash-exp")
        ]

        # If user has specific preference, try that first
        if self.preferred_model:
            for rate_limit_name, model_name in model_options:
                if self.preferred_model in model_name:
                    can_proceed, reason = self.rate_limiter.check_rate_limit(rate_limit_name, 2000)
                    if can_proceed:
                        try:
                            model = genai.GenerativeModel(model_name)
                            logger.info(f"✅ Selected preferred model: {model_name}")
                            return model
                        except Exception as e:
                            logger.warning(f"Preferred model {model_name} unavailable: {e}")
                    else:
                        logger.warning(f"Preferred model {model_name} rate limited: {reason}")

        # Fallback to best available model
        for rate_limit_name, model_name in model_options:
            can_proceed, reason = self.rate_limiter.check_rate_limit(rate_limit_name, 2000)
            if can_proceed:
                try:
                    model = genai.GenerativeModel(model_name)
                    logger.info(f"✅ Selected fallback model: {model_name}")
                    return model
                except Exception as e:
                    logger.warning(f"Model {model_name} unavailable: {e}")

        # Last resort - use 2.0 Flash without rate check
        logger.warning("⚠️ All models rate limited - using 2.0 Flash as emergency fallback")
        return genai.GenerativeModel("gemini-2.0-flash-exp")

    def _initialize_tools(self):
        """Initialize all tools from clean_tool_manager + RAG as a tool."""
        try:
            from .clean_tool_manager import get_clean_tool_registry
            self.tool_registry = get_clean_tool_registry()

            # Add RAG as a tool that LLM can call dynamically
            self._add_rag_tool_to_registry()

            logger.info("✅ Tool registry initialized with RAG tool")
        except Exception as e:
            logger.error(f"❌ Tool registry initialization failed: {e}")
            self.tool_registry = None

    def _add_rag_tool_to_registry(self):
        """Add RAG as a tool that the LLM can call dynamically."""
        try:
            from .chromadb_rag_system import ChromaDBTradingRAGSystem

            def rag_search_tool(query: str, limit: int = 5) -> dict:
                """
                Search trading memory for relevant past analyses.

                Args:
                    query: Search query (e.g., "BTC scalping analysis", "Nifty support levels")
                    limit: Number of results to return

                Returns:
                    Dict with search results from past analyses
                """
                try:
                    rag_system = ChromaDBTradingRAGSystem()
                    results = rag_system.get_relevant_context(query, limit)

                    return {
                        "success": True,
                        "results": results,
                        "query": query,
                        "count": len(results) if results else 0
                    }
                except Exception as e:
                    return {
                        "success": False,
                        "error": str(e),
                        "query": query
                    }

            # Register RAG tool
            if self.tool_registry:
                self.tool_registry.register_tool(
                    name="query_trading_memory",
                    function=rag_search_tool,
                    description="Search trading memory for relevant past analyses and patterns.",
                    parameters={
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query (e.g., 'BTC scalping analysis', 'Nifty support levels')"},
                            "limit": {"type": "number", "description": "Number of results to return (default: 5)"}
                        },
                        "required": ["query"]
                    }
                )
                logger.info("✅ RAG tool added to registry")

        except Exception as e:
            logger.error(f"❌ Failed to add RAG tool: {e}")

    def _initialize_prompts(self):
        """Initialize sophisticated prompts from prompts.py."""
        try:
            from .prompts import (
                positional_prompt, scalp_prompt, crypto_prompt, indian_market_prompt,
                tool_summarization_prompts, generic_summarization_prompt
            )
            self.prompts = {
                "positional": positional_prompt,
                "scalp": scalp_prompt,
                "crypto": crypto_prompt,
                "indian_market": indian_market_prompt,
                "tool_summarization": tool_summarization_prompts,
                "generic_summarization": generic_summarization_prompt
            }
            logger.info("✅ Sophisticated prompts loaded")
        except Exception as e:
            logger.error(f"❌ Prompt loading failed: {e}")
            self.prompts = {}

    def _initialize_optimization_systems(self):
        """Initialize performance optimization and quality assurance systems."""
        # Optional optimization systems - not required for core functionality
        self.performance_optimizer = None
        self.qa_system = None
        self.orchestrator = None

    def _create_focused_workflow(self) -> StateGraph:
        """
        🎯 CREATE FOCUSED WORKFLOW
        Clean implementation with your proven 8-step process:
        1. Chart analysis → symbol detection
        2. Parallel tool execution → market data, news, FII/DII
        3. Tool summarization → Gemini Flash compression
        4. RAG integration → historical context
        5. Final analysis → sophisticated prompts
        6. Structured output → JSON with Entry/SL/TP
        7. Memory storage → for future reference
        8. Dashboard display → tool summaries with links
        """
        workflow = StateGraph(CompleteTradingAnalysisState)

        # Simple, proven workflow
        workflow.add_node("comprehensive_analysis", self.comprehensive_analysis_node)
        workflow.add_node("memory_storage", self.memory_storage_node)

        # Clean linear flow
        workflow.set_entry_point("comprehensive_analysis")
        workflow.add_edge("comprehensive_analysis", "memory_storage")
        workflow.add_edge("memory_storage", END)

        return workflow.compile()

    def comprehensive_analysis_node(self, state: CompleteTradingAnalysisState) -> CompleteTradingAnalysisState:
        """
        🎯 MAIN ANALYSIS NODE - Implements flow.svg CLEAN ARCHITECTURE:

        STEP 1: Chart Upload → Gemini 2.5 Pro (Vision + Symbol Detection) → LLM Call 1
        STEP 2: Smart Tool Selection → LLM decides which APIs to call → API Calls (NOT LLM)
        STEP 3: Tool Results → Gemini 2.5 Flash Summarization → LLM Call 2
        STEP 4: Final Analysis → Chart + Summary + RAG Tool → Gemini 2.5 Pro → LLM Call 3

        ✅ ONLY 3 LLM CALLS TOTAL - Clean, efficient, intelligent architecture
        """
        logger.info("🚀 Starting comprehensive analysis with PARALLEL tools + RAG...")

        # Start comprehensive workflow logging
        workflow_id = f"trading_analysis_{int(time.time())}"
        workflow_logger.start_workflow(workflow_id, {
            "analysis_mode": state.get("analysis_mode"),
            "market_specialization": state.get("market_specialization"),
            "chart_count": len(state.get("chart_images", []))
        })

        try:
            # Progress tracking
            state["progress_messages"] = ["🚀 Starting comprehensive AI analysis..."]

            # STEP 1: Chart analysis to detect symbol and market type
            state["progress_messages"].append("👁️ Analyzing chart to detect symbol and market...")
            workflow_logger.log_step("Chart Analysis", "llm_call",
                                   input_data="Chart image for symbol detection")

            step_start = time.time()
            chart_analysis = self._analyze_chart_for_symbol_detection(state)
            step_time = time.time() - step_start

            if not chart_analysis.get("success"):
                workflow_logger.log_step("Chart Analysis", "llm_call",
                                       execution_time=step_time, status="error",
                                       error="Failed to analyze chart for symbol detection")
                state["error"] = "Failed to analyze chart for symbol detection"
                state["workflow_status"] = "chart_analysis_failed"
                workflow_logger.end_workflow("error", "Chart analysis failed")
                return state

            workflow_logger.log_step("Chart Analysis", "llm_call",
                                   output_data=chart_analysis, execution_time=step_time)

            # Update state with chart analysis results
            state.update({
                "detected_symbol": chart_analysis.get("detected_symbol"),
                "market_type": chart_analysis.get("market_type"),
                "chart_patterns": chart_analysis.get("basic_patterns", []),  # Use basic_patterns from vision
                "support_levels": chart_analysis.get("support_levels", []),
                "resistance_levels": chart_analysis.get("resistance_levels", []),
                "timeframe": chart_analysis.get("timeframe"),
                "chart_analysis_raw": chart_analysis  # Store full analysis for debugging
            })

            state["progress_messages"].append(f"✅ STEP 1 Complete: {state['detected_symbol']} ({state['market_type']} market)")

            # 🎯 STEP 2: Smart Tool Selection → API Calls (NOT LLM calls)
            state["progress_messages"].append("🎯 STEP 2: Smart Tool Selection → API Calls")
            workflow_logger.log_step("Step 2: Tool Selection", "api_calls",
                                   input_data=f"Market: {state.get('market_type')}, Symbol: {state.get('detected_symbol')}")

            step_start = time.time()
            parallel_results = self._execute_tools_parallel(state)
            step_time = time.time() - step_start

            workflow_logger.log_step("Step 2: Tool Selection", "api_calls",
                                   output_data=parallel_results, execution_time=step_time)

            state["progress_messages"].append("✅ STEP 2 Complete: Tool data collected")

            # 🎯 STEP 3: Tool Results → Flash Summarization (LLM Call 2)
            state["progress_messages"].append("🎯 STEP 3: Flash Summarization (LLM Call 2)")
            workflow_logger.log_step("Step 3: Flash Summarization", "llm_call",
                                   input_data="Tool results for summarization")

            step_start = time.time()
            summarized_tools = self._summarize_tool_results_with_gemini_flash(parallel_results, state)
            step_time = time.time() - step_start

            workflow_logger.log_step("Step 3: Flash Summarization", "llm_call",
                                   output_data=summarized_tools, execution_time=step_time)

            state["progress_messages"].append("✅ STEP 3 Complete: Tool results summarized")

            # Store summaries in RAG (for LLM tool access)
            state["progress_messages"].append("💾 Storing summaries in RAG for tool access...")
            rag_storage = self._store_summaries_in_rag(summarized_tools, state)

            # 🎯 STEP 4: Final Analysis → Chart + Summary + RAG Tool (LLM Call 3)
            state["progress_messages"].append("🎯 STEP 4: Final Analysis (LLM Call 3)")
            workflow_logger.log_step("Step 4: Final Analysis", "llm_call",
                                   input_data="Chart + Tool summaries + RAG tool available")

            step_start = time.time()
            final_analysis = self._generate_final_analysis_with_rag_tool(
                chart_analysis, summarized_tools, state
            )
            step_time = time.time() - step_start

            # Update state with final results
            if final_analysis.get("success"):
                workflow_logger.log_step("Final Analysis", "llm_call",
                                       output_data=final_analysis, execution_time=step_time)

                # Prepare tool data for GUI display (with raw data for links/URLs)
                tool_display_data = {}
                raw_tool_results = parallel_results.get("tool_results", {})
                summarized_tool_data = summarized_tools.get("summarized_tools", {})

                for tool_name, raw_data in raw_tool_results.items():
                    tool_display_data[tool_name] = {
                        "result_summary": summarized_tool_data.get(tool_name, {}).get("summary", "No summary available"),
                        "result": raw_data,  # Keep raw data for URLs and structured info
                        "success": True,
                        "execution_time": 1.0  # Default execution time
                    }

                state.update({
                    "analysis": final_analysis.get("analysis"),
                    "trading_signals": final_analysis.get("trading_signals"),
                    "tool_usage_log": parallel_results.get("tool_usage_log", []),
                    "tool_results": tool_display_data,  # Rich tool data for GUI
                    "workflow_status": "analysis_complete"
                })
                state["progress_messages"].append("✅ STEP 4 Complete: Final analysis generated!")
                state["progress_messages"].append("🎯 flow.svg Architecture Complete: 3 LLM calls total")
                logger.info("✅ flow.svg architecture completed successfully - 3 LLM calls total")

                # End workflow successfully
                workflow_summary = workflow_logger.end_workflow("success")
                state["workflow_summary"] = workflow_summary

            else:
                workflow_logger.log_step("Final Analysis", "llm_call",
                                       execution_time=step_time, status="error",
                                       error=final_analysis.get("error", "Final analysis failed"))

                state["error"] = final_analysis.get("error", "Final analysis failed")
                state["workflow_status"] = "analysis_failed"
                state["progress_messages"].append(f"❌ Analysis failed: {state['error']}")

                # End workflow with error
                workflow_logger.end_workflow("error", state["error"])

            return state

        except Exception as e:
            logger.error(f"Comprehensive analysis failed: {e}")

            # End workflow with error
            workflow_logger.end_workflow("error", f"Analysis failed: {str(e)}")

            state["analysis"] = {
                "error": f"Analysis failed: {str(e)}",
                "tool_usage": []
            }
            state["workflow_status"] = "analysis_failed"
            state["progress_messages"].append(f"❌ Analysis failed: {str(e)}")

        return state

    def _analyze_chart_for_symbol_detection(self, state: CompleteTradingAnalysisState) -> Dict[str, Any]:
        """
        🎯 STEP 1 of flow.svg: Vision + Symbol Detection (LLM Call 1)
        Single Gemini 2.5 Pro call for chart analysis and symbol detection.
        """
        try:
            # Import clean vision prompt
            from .clean_prompts import vision_prompt

            logger.info("🎯 STEP 1: Vision + Symbol Detection (LLM Call 1)")

            chart_images = state.get("chart_images", [])
            if not chart_images:
                return {"success": False, "error": "No chart images provided"}

            # Fix image processing - handle bytes properly and optimize for Gemini API
            try:
                from PIL import Image
                import io

                # Ensure we have bytes
                image_data = chart_images[0]
                if isinstance(image_data, bytes):
                    img = Image.open(io.BytesIO(image_data))
                else:
                    # If it's already a PIL image, use it directly
                    img = image_data

                # Ensure image is in RGB mode for Gemini
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Optimize image size to prevent metadata size issues
                # Resize to smaller dimensions to prevent "metadata size exceeds hard limit" error
                max_size = (800, 800)  # Reduced from 1024 to prevent metadata size limits
                if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)
                    logger.info(f"Resized image to {img.size} to prevent API limits")

                # Compress image to further reduce metadata size
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='JPEG', quality=85, optimize=True)
                img_bytes.seek(0)

                # Reload compressed image
                img = Image.open(img_bytes)
                logger.info(f"Compressed and optimized image: {img.size}")

            except Exception as img_error:
                logger.error(f"Image processing failed: {img_error}")
                return {"success": False, "error": f"Image processing failed: {str(img_error)}"}

            # 🚀 FIXED: Increased token limit for sophisticated prompts
            generation_config = {
                "temperature": 0.1,
                "max_output_tokens": 8000,  # 🎯 Increased from 1000 to handle sophisticated prompts
            }

            # Generate chart analysis with comprehensive logging and error handling
            start_time = time.time()
            try:
                logger.info(f"🎯 Making vision API call (LLM Call 1) with image size: {img.size}, mode: {img.mode}")
                response = self.model.generate_content(
                    [vision_prompt, img],
                    generation_config=generation_config
                )
                execution_time = time.time() - start_time
                logger.info(f"Vision API call successful in {execution_time:.2f}s")
            except Exception as api_error:
                execution_time = time.time() - start_time
                logger.error(f"Vision API call failed after {execution_time:.2f}s: {api_error}")
                logger.error(f"Image details: size={img.size}, mode={img.mode}, format={img.format}")
                logger.error(f"Prompt length: {len(vision_prompt)} characters")
                raise api_error

            # Log the LLM interaction for chart analysis
            llm_logger.log_llm_interaction(
                prompt=vision_prompt,
                response=response.text if response else "No response",
                model="gemini-2.0-flash-exp",
                context={
                    "step": "chart_analysis",
                    "image_size": f"{img.size[0]}x{img.size[1]}",
                    "analysis_mode": state.get("analysis_mode", "unknown")
                },
                execution_time=execution_time
            )

            # 🔍 DETAILED LOGGING for debugging
            logger.info(f"� Response object exists: {response is not None}")
            logger.info(f"🔍 Response type: {type(response)}")

            if response:
                logger.info(f"🔍 Response attributes: {dir(response)}")
                logger.info(f"🔍 Response text exists: {hasattr(response, 'text')}")
                if hasattr(response, 'text'):
                    logger.info(f"📝 Vision analysis raw response length: {len(response.text) if response.text else 0}")
                    logger.info(f"📝 Vision analysis response preview: {response.text[:300] if response.text else 'No response text'}")

                # Check for other response attributes
                if hasattr(response, 'candidates'):
                    logger.info(f"🔍 Response candidates: {len(response.candidates) if response.candidates else 0}")
                    if response.candidates:
                        for i, candidate in enumerate(response.candidates):
                            logger.info(f"🔍 Candidate {i}: {candidate}")
                            if hasattr(candidate, 'content'):
                                logger.info(f"🔍 Candidate {i} content: {candidate.content}")

            if response and response.text:
                import json
                import re

                # 🔍 Log the full response for debugging
                logger.info(f"🔍 FULL VISION RESPONSE: {response.text}")

                try:
                    # Clean the response text
                    response_text = response.text.strip()

                    # Try direct JSON parsing first
                    result = json.loads(response_text)
                    result["success"] = True
                    logger.info(f"✅ Successfully parsed vision analysis directly: {result.get('detected_symbol', 'No symbol')}")
                    return result

                except json.JSONDecodeError as e:
                    logger.warning(f"⚠️ Direct JSON parsing failed: {e}")
                    logger.info(f"🔍 Attempting markdown extraction...")

                    # Try to extract JSON from markdown blocks
                    json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
                    matches = re.findall(json_pattern, response.text, re.DOTALL)

                    for match in matches:
                        try:
                            result = json.loads(match)
                            result["success"] = True
                            logger.info(f"Successfully extracted JSON from markdown: {result}")
                            return result
                        except json.JSONDecodeError as parse_error:
                            logger.warning(f"⚠️ Markdown JSON parsing failed: {parse_error}")
                            continue

                    # 🚨 CRITICAL ERROR - No valid JSON found
                    logger.error(f"🚨 VISION ANALYSIS PARSING FAILED!")
                    logger.error(f"🔍 Original error: {e}")
                    logger.error(f"📝 Full response: {response.text}")
                    logger.error(f"🔍 Markdown matches found: {len(matches)}")

                    return {
                        "success": False,
                        "error": f"Failed to parse vision analysis JSON: {str(e)}",
                        "raw_response": response.text,
                        "parsing_attempts": ["direct_json", "markdown_extraction"],
                        "markdown_matches": len(matches)
                    }

            # 🚨 CRITICAL: Empty response from Gemini Vision API
            logger.error("🚨 GEMINI VISION API RETURNED EMPTY RESPONSE!")
            logger.error("🔍 This is a known Gemini API issue - the model sometimes returns empty responses")
            logger.error("🔍 Possible causes:")
            logger.error("   1. Image content may be blocked by safety filters")
            logger.error("   2. API quota/rate limiting issues")
            logger.error("   3. Temporary Gemini service issues")
            logger.error("   4. Image format/size issues")

            return {
                "success": False,
                "error": "Gemini Vision API returned empty response",
                "error_details": {
                    "issue": "empty_vision_response",
                    "possible_causes": [
                        "Safety filters blocked image content",
                        "API quota/rate limiting",
                        "Temporary Gemini service issues",
                        "Image format/size problems"
                    ],
                    "suggestions": [
                        "Try a different chart image",
                        "Check API quota status",
                        "Retry in a few minutes",
                        "Verify image is a clear trading chart"
                    ]
                }
            }

        except Exception as e:
            logger.error(f"🚨 Chart analysis failed with exception: {e}")
            logger.error(f"🔍 Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"🔍 Full traceback: {traceback.format_exc()}")
            return {"success": False, "error": str(e), "exception_type": type(e).__name__}

    def _get_intelligent_tool_selection(self, detected_symbol: str, market_type: str, state: CompleteTradingAnalysisState) -> List[Dict[str, Any]]:
        """
        🧠 LLM-driven intelligent tool selection (following flow.svg Step 2)
        LLM decides which tools to call based on detected symbol and market type.
        """
        # For now, implement smart default selection based on market type
        # TODO: Later can be enhanced with actual LLM decision-making

        tool_calls = []

        # Core tools for all markets
        tool_calls.extend([
            {"name": "get_market_context_summary", "args": {"symbol": detected_symbol}},
            {"name": "get_comprehensive_market_news", "args": {"symbol": detected_symbol}}
        ])

        # Market-specific intelligent selection
        if market_type == "indian":
            logger.info("🇮🇳 Intelligent selection: Adding Indian market specific tools")
            tool_calls.extend([
                {"name": "get_economic_calendar_risk", "args": {"symbol": detected_symbol, "market_type": "indian"}},
                {"name": "get_fii_dii_flows", "args": {"symbol": detected_symbol}}
            ])
        elif market_type == "crypto":
            logger.info("₿ Intelligent selection: Adding crypto market specific tools")
            tool_calls.extend([
                {"name": "get_economic_calendar_risk", "args": {"symbol": detected_symbol, "market_type": "crypto"}}
            ])
        else:
            logger.info("🌍 Intelligent selection: Adding general market tools")
            tool_calls.extend([
                {"name": "get_economic_calendar_risk", "args": {"symbol": detected_symbol, "market_type": "us"}}
            ])

        return tool_calls

    def _execute_tools_parallel(self, state: CompleteTradingAnalysisState) -> Dict[str, Any]:
        """
        🎯 STEP 2 of flow.svg: Smart Tool Selection (API Calls, NOT LLM calls)
        LLM-driven intelligent tool selection based on detected symbol and market type.
        """
        try:
            from .parallel_utils import execute_tools_parallel

            detected_symbol = state.get("detected_symbol", "")
            market_type = state.get("market_type", "unknown")

            logger.info(f"🎯 STEP 2: Smart Tool Selection for {market_type.upper()} market analysis of {detected_symbol}")

            # 🧠 LLM-DRIVEN TOOL SELECTION (following flow.svg)
            # Instead of hardcoded tools, let LLM decide which tools to call
            # 🧠 INTELLIGENT TOOL SELECTION (following flow.svg)
            # LLM decides which tools to call based on analysis needs
            tool_calls = self._get_intelligent_tool_selection(detected_symbol, market_type, state)

            logger.info(f"🧠 LLM selected {len(tool_calls)} tools for analysis: {[t['name'] for t in tool_calls]}")

            # Create tool functions mapping
            tool_functions = {}
            for tool_name in self.tool_registry.get_tool_names():
                tool_functions[tool_name] = lambda name=tool_name, **kwargs: self.tool_registry.execute_tool(name, **kwargs).data

            # Execute tools in parallel
            def progress_callback(message):
                if "progress_messages" in state:
                    state["progress_messages"].append(message)

            parallel_results = execute_tools_parallel(tool_calls, tool_functions, progress_callback)

            return {
                "success": True,
                "tool_results": parallel_results.get("results", {}),
                "tool_usage_log": parallel_results.get("execution_log", []),
                "execution_time": parallel_results.get("total_time", 0)
            }

        except Exception as e:
            logger.error(f"Parallel tool execution failed: {e}")
            return {"success": False, "error": str(e), "tool_results": {}, "tool_usage_log": []}

    def _summarize_tool_results_with_gemini_flash(self, parallel_results: Dict[str, Any], state: CompleteTradingAnalysisState) -> Dict[str, Any]:
        """
        🎯 STEP 3 of flow.svg: Flash Summarization (LLM Call 2)
        Gemini 2.5 Flash summarizes all tool results into concise insights.
        """
        try:
            from .clean_prompts import flash_summarization_prompt

            logger.info("🎯 STEP 3: Flash Summarization (LLM Call 2)")

            tool_results = parallel_results.get("tool_results", {})
            summarized_tools = {}

            # 🚦 Smart model selection with rate limiting for summarization
            flash_model = None
            flash_model_name = None

            # Try 2.5 Flash first
            can_proceed, reason = self.rate_limiter.check_rate_limit("gemini-2.5-flash", 1000)
            if can_proceed:
                try:
                    flash_model = genai.GenerativeModel("gemini-2.5-flash")
                    flash_model_name = "gemini-2.5-flash"
                    logger.info("✅ Using Gemini 2.5 Flash for tool summarization")
                except Exception as e:
                    logger.warning(f"Gemini 2.5 Flash not available: {e}")
            else:
                logger.warning(f"🚫 Gemini 2.5 Flash rate limit: {reason}")

            # Fallback to 2.0 Flash if 2.5 Flash unavailable or rate limited
            if flash_model is None:
                can_proceed, reason = self.rate_limiter.check_rate_limit("gemini-2.0-flash", 1000)
                if can_proceed:
                    try:
                        flash_model = genai.GenerativeModel("gemini-2.0-flash-exp")
                        flash_model_name = "gemini-2.0-flash"
                        logger.info("✅ Using Gemini 2.0 Flash for tool summarization (fallback)")
                    except Exception as e:
                        logger.warning(f"Gemini 2.0 Flash not available: {e}")
                else:
                    logger.warning(f"🚫 Gemini 2.0 Flash rate limit: {reason}")

            # If both models are rate limited, skip summarization
            if flash_model is None:
                logger.error("❌ Both Flash models are rate limited - skipping tool summarization")
                return {
                    "success": False,
                    "error": "Rate limits exceeded for summarization models",
                    "summarized_tools": {},
                    "tool_usage_log": []
                }

            # 🎯 Use clean flash summarization prompt for all tool results at once
            try:
                # Format all tool results for summarization
                tool_results_text = ""
                for tool_name, tool_data in tool_results.items():
                    tool_results_text += f"\n**{tool_name}:**\n{str(tool_data)}\n"

                # Use clean flash summarization prompt
                summarization_prompt = flash_summarization_prompt.format(tool_results=tool_results_text)

                # Generate summary with Flash model
                response = flash_model.generate_content(
                    summarization_prompt,
                    generation_config={"temperature": 0.1, "max_output_tokens": 2000}
                )

                # 🚦 Record successful API usage
                if flash_model_name:
                    self.rate_limiter.record_request(flash_model_name, 1500)  # Estimate for batch summarization

                summary_text = response.text if response.text else "No summary generated"
                logger.info(f"✅ Flash summarization completed: {len(summary_text)} characters")

                # Store summarized results
                for tool_name in tool_results.keys():
                    summarized_tools[tool_name] = {
                        "summary": summary_text,  # All tools get the comprehensive summary
                        "raw_data": tool_results[tool_name],
                        "timestamp": datetime.now().isoformat()
                    }

            except Exception as e:
                logger.warning(f"Flash summarization failed: {e}")
                # Fallback to individual tool summaries
                for tool_name, tool_data in tool_results.items():
                    summarized_tools[tool_name] = {
                        "summary": f"Raw data (summarization failed): {str(tool_data)[:200]}...",
                        "raw_data": tool_data,
                        "timestamp": datetime.now().isoformat()
                    }

            return {
                "success": True,
                "summarized_tools": summarized_tools,
                "tool_count": len(summarized_tools)
            }

        except Exception as e:
            logger.error(f"Tool summarization failed: {e}")
            return {"success": False, "error": str(e), "summarized_tools": {}}







    def _store_summaries_in_rag(self, summarized_tools: Dict[str, Any], state: CompleteTradingAnalysisState) -> Dict[str, Any]:
        """Step 4: Store tool summaries in RAG for future learning (NO pre-querying)."""
        try:
            from .chromadb_rag_system import ChromaDBTradingRAGSystem

            detected_symbol = state.get("detected_symbol", "")
            market_type = state.get("market_type", "")
            analysis_mode = state.get("analysis_mode", "positional")

            logger.info(f"💾 Storing tool summaries in RAG for {market_type.upper()} market: {detected_symbol} ({analysis_mode})")

            # Initialize RAG system
            rag_system = ChromaDBTradingRAGSystem()

            # Store current tool summaries in RAG for future learning
            tool_summaries = summarized_tools.get("summarized_tools", {})
            stored_count = 0

            for tool_name, tool_data in tool_summaries.items():
                try:
                    rag_system.ingest_analysis_summary(
                        summary=tool_data.get("summary", ""),
                        symbol=detected_symbol,
                        mode=analysis_mode,
                        market=market_type
                    )
                    stored_count += 1
                except Exception as e:
                    logger.warning(f"Failed to store {tool_name} in RAG: {e}")

            logger.info(f"✅ Stored {stored_count}/{len(tool_summaries)} tool summaries in RAG")

            # 🎯 RAG is now a PURE TOOL - LLM will query it dynamically when needed
            # No pre-querying! LLM decides what to ask and when to ask it.
            logger.info("🧠 RAG system ready - LLM can now query historical patterns dynamically via 'query_trading_memory' tool")

            return {
                "success": True,
                "stored_summaries": stored_count,
                "rag_available": True,
                "message": "Tool summaries stored in RAG. LLM can now query historical patterns dynamically."
            }

        except Exception as e:
            logger.error(f"RAG integration failed: {e}")
            return {"success": False, "error": str(e), "rag_context": {}}

    def _generate_final_analysis_with_rag_tool(self, chart_analysis: Dict[str, Any], summarized_tools: Dict[str, Any],
                                             state: CompleteTradingAnalysisState) -> Dict[str, Any]:
        """
        🎯 STEP 4 of flow.svg: Final Analysis (LLM Call 3)
        Gemini 2.5 Pro receives: Chart + Symbol + Tool Summary + RAG Tool available.
        Uses single main prompt for comprehensive analysis.
        """
        import json
        import time

        try:
            from .clean_prompts import create_main_prompt

            logger.info("🎯 STEP 4: Final Analysis (LLM Call 3)")

            # 🎯 USE SINGLE MAIN PROMPT (combines analysis type + market specialization)
            analysis_mode = state.get("analysis_mode", "positional")
            market_specialization = state.get("market_specialization", "indian market")

            # Create intelligent single prompt
            main_prompt = create_main_prompt(analysis_mode, market_specialization)

            logger.info(f"🧠 Using single main prompt: {analysis_mode} + {market_specialization}")

            # Get context data
            symbol = state.get("detected_symbol", "Unknown")
            market_type = state.get("market_type", "unknown")

            # STEP 1: Create compressed tool summary using Gemini Flash
            logger.info("🔄 Creating compressed tool summary with Gemini Flash...")
            compressed_tool_summary = self._create_compressed_tool_summary(summarized_tools, symbol, market_type)

            # 🎯 STEP 2: LLM will use RAG as a TOOL - no pre-querying!
            logger.info("🧠 RAG available as tool - LLM can query historical patterns dynamically")

            # STEP 3: Use single main prompt directly - NO truncation
            logger.info(f"✅ Using single main prompt directly ({len(main_prompt)} chars)")

            # STEP 4: Create optimized prompt with compressed context (NO pre-queried RAG)
            chart_info = f"Symbol: {symbol} | Market: {market_type}"
            if chart_analysis.get('timeframe'):
                chart_info += f" | Timeframe: {chart_analysis['timeframe']}"
            if chart_analysis.get('basic_patterns'):
                chart_info += f" | Chart Patterns: {', '.join(chart_analysis['basic_patterns'])}"
            if chart_analysis.get('support_levels'):
                chart_info += f" | Support: {chart_analysis['support_levels']}"
            if chart_analysis.get('resistance_levels'):
                chart_info += f" | Resistance: {chart_analysis['resistance_levels']}"

            enhanced_prompt = f"""{main_prompt}

CHART ANALYSIS:
{chart_info}

MARKET INTELLIGENCE (Tool Summary):
{compressed_tool_summary}

🧠 RAG TOOL AVAILABLE: You can query historical trading patterns using the 'query_trading_memory' tool.
Query examples: "{symbol} {analysis_mode} patterns", "similar {market_type} market setups", "historical outcomes for {symbol}"

ANALYSIS TARGET: {symbol} ({market_type}) - {analysis_mode} trading analysis"""

            # Log prompt optimization results
            prompt_length = len(enhanced_prompt)
            logger.info(f"Final analysis prompt optimized: {prompt_length} characters")

            # Gemini 2.5 Pro handles large prompts efficiently (32k token limit)
            logger.info(f"Using Gemini 2.5 Pro with {prompt_length} character prompt")

            # 🚦 Smart model selection with rate limiting for final analysis
            final_model = None
            model_name = None

            # Try user's preferred model first (usually 2.5 Pro)
            if "2.5-pro" in self.preferred_model.lower():
                can_proceed, reason = self.rate_limiter.check_rate_limit("gemini-2.5-pro", 4000)
                if can_proceed:
                    try:
                        final_model = genai.GenerativeModel(
                            model_name="gemini-2.5-pro",
                            generation_config=genai.types.GenerationConfig(
                                temperature=0.1,
                                max_output_tokens=16000,
                                candidate_count=1,
                                stop_sequences=None
                            )
                        )
                        model_name = "gemini-2.5-pro"
                        logger.info("✅ Using Gemini 2.5 Pro for final analysis")
                    except Exception as e:
                        logger.warning(f"Gemini 2.5 Pro unavailable: {e}")
                else:
                    logger.warning(f"🚫 Gemini 2.5 Pro rate limited: {reason}")

            # Fallback to 2.5 Flash
            if final_model is None:
                can_proceed, reason = self.rate_limiter.check_rate_limit("gemini-2.5-flash", 4000)
                if can_proceed:
                    try:
                        final_model = genai.GenerativeModel(
                            model_name="gemini-2.5-flash",
                            generation_config=genai.types.GenerationConfig(
                                temperature=0.1,
                                max_output_tokens=12000,
                                candidate_count=1,
                                stop_sequences=None
                            )
                        )
                        model_name = "gemini-2.5-flash"
                        logger.info("✅ Using Gemini 2.5 Flash for final analysis (fallback)")
                    except Exception as e:
                        logger.warning(f"Gemini 2.5 Flash unavailable: {e}")
                else:
                    logger.warning(f"🚫 Gemini 2.5 Flash rate limited: {reason}")

            # Last resort - 2.0 Flash
            if final_model is None:
                final_model = genai.GenerativeModel(
                    model_name="gemini-2.0-flash-exp",
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.1,
                        max_output_tokens=8000,
                        candidate_count=1,
                        stop_sequences=None
                    )
                )
                model_name = "gemini-2.0-flash"
                logger.info("⚠️ Using Gemini 2.0 Flash for final analysis (emergency fallback)")

            # Log prompt statistics
            prompt_length = len(enhanced_prompt)
            logger.info(f"Final analysis prompt length: {prompt_length} characters")

            # Generate final analysis with retry logic
            max_retries = 2
            response = None
            execution_time = 0

            # 🧠 Add RAG tool to the model so LLM can query historical patterns
            try:
                # Get the RAG tool function
                rag_tool_func = self.tool_manager.get_tool_function("query_trading_memory")
                if rag_tool_func:
                    # Configure model with tools
                    final_model = genai.GenerativeModel(
                        model_name=model_name,
                        tools=[rag_tool_func],  # LLM can now call RAG dynamically!
                        generation_config=genai.types.GenerationConfig(
                            temperature=0.1,
                            max_output_tokens=16000,
                            candidate_count=1,
                            stop_sequences=None
                        )
                    )
                    logger.info("🧠 RAG tool added to LLM - can now query historical patterns dynamically!")
                else:
                    logger.warning("⚠️ RAG tool not found - proceeding without tool access")
            except Exception as e:
                logger.warning(f"⚠️ Failed to add RAG tool to model: {e}")

            for attempt in range(max_retries):
                try:
                    start_time = time.time()
                    response = final_model.generate_content(enhanced_prompt)
                    execution_time = time.time() - start_time

                    # Log response statistics
                    response_length = len(response.text) if response and response.text else 0
                    logger.info(f"Final analysis attempt {attempt + 1}: {response_length} characters, execution time: {execution_time:.2f}s")

                    # Check if response is valid
                    if response and response.text and len(response.text.strip()) > 50:
                        break  # Success
                    else:
                        logger.warning(f"Attempt {attempt + 1} produced insufficient response, retrying...")

                except Exception as e:
                    logger.warning(f"Attempt {attempt + 1} failed: {e}")
                    if attempt == max_retries - 1:
                        raise  # Re-raise on final attempt

            # 🚦 Record successful API usage
            if model_name and response and response.text:
                # Estimate tokens used (rough calculation: 4 chars per token)
                estimated_tokens = (len(enhanced_prompt) + len(response.text)) // 4
                self.rate_limiter.record_request(model_name, estimated_tokens)
                logger.info(f"📊 Recorded {estimated_tokens} tokens for {model_name}")

            # Log the LLM interaction
            llm_logger.log_llm_interaction(
                prompt=enhanced_prompt,
                response=response.text if response and response.text else "Empty response",
                model=model_name or "unknown",
                context={
                    "symbol": state.get("detected_symbol"),
                    "market_type": state.get("market_type"),
                    "analysis_mode": state.get("analysis_mode"),
                    "step": "final_analysis"
                },
                execution_time=execution_time
            )

            # Check for empty response
            if not response or not response.text:
                logger.error("Empty response from Gemini API")
                return {
                    "success": False,
                    "error": "Empty response from AI model",
                    "analysis_notes": "Analysis failed - empty response from AI model",
                    "detected_symbol": symbol,
                    "market_type": market_type
                }

            # Improved truncation detection for nested JSON structures
            response_text = response.text.strip()

            # Check if response appears truncated (more sophisticated detection)
            is_truncated = False
            if response_text:
                # Remove markdown code blocks if present
                clean_text = response_text
                if clean_text.startswith('```'):
                    # Extract content from markdown blocks
                    import re
                    json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', clean_text, re.DOTALL)
                    if json_match:
                        clean_text = json_match.group(1)

                # Check for proper JSON closure
                if clean_text:
                    # Count opening and closing braces
                    open_braces = clean_text.count('{')
                    close_braces = clean_text.count('}')

                    # Also check for incomplete JSON patterns
                    incomplete_patterns = [
                        '":', '",', '",\n', ':\n', ',\n', '[\n', '{\n'
                    ]

                    ends_incomplete = any(clean_text.rstrip().endswith(pattern.rstrip()) for pattern in incomplete_patterns)

                    if open_braces != close_braces or ends_incomplete:
                        is_truncated = True
                        logger.warning(f"Response appears truncated - braces: {open_braces} open, {close_braces} close")
                        logger.warning(f"Last 100 chars: {response_text[-100:]}")

            if is_truncated:
                # Try to complete the JSON
                try:
                    # Attempt to fix common truncation issues
                    fixed_json = self._attempt_json_completion(response_text)
                    if fixed_json:
                        logger.info("Successfully completed truncated JSON")
                        response_text = fixed_json
                    else:
                        # 🚨 CLEAR ERROR - No fallback masking
                        logger.error(f"🚨 JSON completion failed - response truncated")
                        logger.error(f"🔍 Response preview: {response_text[:200] if response_text else 'No response'}")
                        return {
                            "success": False,
                            "error": "JSON parsing failed - response may be truncated or malformed",
                            "analysis_notes": f"Analysis failed - JSON parsing error",
                            "detected_symbol": symbol,
                            "market_type": market_type,
                            "raw_response_preview": response_text[:500] if response_text else "No response"
                        }

                except Exception as parsing_error:
                    logger.error(f"🚨 JSON completion failed: {parsing_error}")
                    return {
                        "success": False,
                        "error": f"JSON completion failed: {str(parsing_error)}",
                        "analysis_notes": "Analysis failed - JSON completion error",
                        "detected_symbol": symbol,
                        "market_type": market_type
                    }

            if response and response.text:
                import json
                import re

                try:
                    # Try to parse as JSON directly
                    analysis_result = json.loads(response.text)
                    logger.info(f"✅ Successfully parsed JSON response with keys: {list(analysis_result.keys())}")

                    # Handle sophisticated prompt format (from prompts.py)
                    if "status" in analysis_result and "trade_ideas" in analysis_result:
                        logger.info("✅ Detected sophisticated prompt response format")

                        # Extract and convert trade_ideas to trading_signals format for GUI compatibility
                        trade_ideas = analysis_result.get("trade_ideas", [])
                        trading_signals = {}

                        if trade_ideas and len(trade_ideas) > 0:
                            first_trade = trade_ideas[0]

                            # Parse entry price range
                            entry_range = first_trade.get("Entry_Price_Range", "")
                            entry_levels = []
                            if entry_range:
                                if "-" in str(entry_range):
                                    try:
                                        parts = str(entry_range).replace(",", "").split("-")
                                        entry_levels = [float(parts[0]), float(parts[1])]
                                    except:
                                        entry_levels = [float(str(entry_range).replace(",", ""))]
                                else:
                                    try:
                                        entry_levels = [float(str(entry_range).replace(",", ""))]
                                    except:
                                        entry_levels = []

                            # Parse stop loss and take profits
                            try:
                                stop_loss = float(str(first_trade.get("Stop_Loss", "0")).replace(",", "")) if first_trade.get("Stop_Loss") else 0
                            except:
                                stop_loss = 0

                            take_profits = []
                            for tp_key in ["Take_Profit_1", "Take_Profit_2"]:
                                tp_val = first_trade.get(tp_key)
                                if tp_val:
                                    try:
                                        take_profits.append(float(str(tp_val).replace(",", "")))
                                    except:
                                        pass

                            # Parse confidence
                            try:
                                confidence = int(first_trade.get("Confidence", 0))
                            except:
                                confidence = 0

                            trading_signals = {
                                "entry_levels": entry_levels,
                                "stop_loss": stop_loss,
                                "take_profit": take_profits,
                                "confidence": confidence
                            }

                        # Create GUI-compatible response preserving sophisticated format
                        gui_response = {
                            "success": True,
                            "analysis": response.text,
                            "analysis_notes": analysis_result.get("analysis_notes", ""),
                            "trading_signals": trading_signals,
                            "status": analysis_result.get("status", ""),
                            "analysis_summary": analysis_result.get("analysis_summary", ""),
                            "trade_ideas": trade_ideas,
                            "key_levels": analysis_result.get("key_levels", {}),
                            "market_context": analysis_result.get("market_context", ""),
                            "risk_management": analysis_result.get("risk_management", ""),
                            "tool_data_summary": analysis_result.get("tool_data_summary", ""),
                            "detected_symbol": symbol,
                            "market_type": market_type
                        }

                        # Extract support/resistance for compatibility
                        key_levels = analysis_result.get("key_levels", {})
                        gui_response["support_levels"] = key_levels.get("support", [])
                        gui_response["resistance_levels"] = key_levels.get("resistance", [])

                    else:
                        # Fallback for simple format
                        logger.info("Using fallback parsing for simple format")
                        analysis_notes = analysis_result.get("analysis_notes", analysis_result.get("detailed_report", response.text))

                        gui_response = {
                            "success": True,
                            "analysis": response.text,
                            "analysis_notes": analysis_notes,
                            "trading_signals": analysis_result.get("trading_signals", analysis_result),
                            "detected_symbol": symbol,
                            "market_type": market_type
                        }

                        # Copy other fields if present
                        for key in ["status", "analysis_summary", "detailed_report", "trade_ideas"]:
                            if key in analysis_result:
                                gui_response[key] = analysis_result[key]

                    # Store analysis result for persistent dashboard display
                    self._store_analysis_result(gui_response, state, summarized_tools)
                    logger.info(f"✅ Final analysis completed successfully: {len(response.text)} characters")

                    return gui_response

                except json.JSONDecodeError:
                    # Try to extract JSON from markdown
                    json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
                    matches = re.findall(json_pattern, response.text, re.DOTALL)

                    for match in matches:
                        try:
                            analysis_result = json.loads(match)

                            # Create GUI-compatible response
                            analysis_notes = analysis_result.get("analysis_notes",
                                                                analysis_result.get("detailed_report", response.text))
                            gui_response = {
                                "success": True,
                                "analysis": response.text,
                                "analysis_notes": analysis_notes,  # GUI compatibility
                                "trading_signals": analysis_result,
                                "detected_symbol": symbol,
                                "market_type": market_type
                            }

                            # Add specific fields for GUI compatibility
                            if isinstance(analysis_result, dict):
                                # Copy important fields to top level for GUI
                                for key in ["status", "analysis_summary", "detailed_report", "trade_ideas"]:
                                    if key in analysis_result:
                                        gui_response[key] = analysis_result[key]

                            # Store analysis result for persistent dashboard display
                            self._store_analysis_result(gui_response, state, summarized_tools)

                            return gui_response

                        except json.JSONDecodeError:
                            continue

                    # If no JSON found, return text analysis
                    return {
                        "success": True,
                        "analysis": response.text,
                        "analysis_notes": response.text,  # GUI compatibility
                        "detailed_report": response.text,  # For positional analysis
                        "trading_signals": {"analysis": response.text},
                        "detected_symbol": symbol,
                        "market_type": market_type
                    }

            # Fallback response if no valid response
            return {
                "success": False,
                "error": "No response from final analysis",
                "analysis_notes": "Analysis failed - no response from AI model",
                "detected_symbol": symbol,
                "market_type": market_type
            }

        except Exception as e:
            logger.error(f"Final analysis generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_notes": f"Analysis failed - error: {str(e)}",
                "detected_symbol": symbol if 'symbol' in locals() else "Unknown",
                "market_type": market_type if 'market_type' in locals() else "unknown"
            }

    def _attempt_json_completion(self, truncated_response: str) -> str:
        """Attempt to complete truncated JSON response."""
        try:
            import json
            import re

            # Remove markdown code blocks if present
            clean_text = truncated_response.strip()
            if clean_text.startswith('```'):
                json_match = re.search(r'```(?:json)?\s*(\{.*)', clean_text, re.DOTALL)
                if json_match:
                    clean_text = json_match.group(1)
                    # Remove trailing ``` if present
                    clean_text = re.sub(r'\s*```\s*$', '', clean_text)

            # Try to parse as-is first
            try:
                json.loads(clean_text)
                return clean_text  # Already valid JSON
            except json.JSONDecodeError:
                pass

            # Count braces to determine how many to add
            open_braces = clean_text.count('{')
            close_braces = clean_text.count('}')
            missing_braces = open_braces - close_braces

            if missing_braces > 0:
                # Add missing closing braces
                completed = clean_text + ('}' * missing_braces)

                # Try to parse the completed JSON
                try:
                    json.loads(completed)
                    logger.info(f"Successfully completed JSON by adding {missing_braces} closing braces")
                    return completed
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON completion failed even after adding braces: {e}")

            # Try to fix common truncation patterns
            completion_attempts = [
                # If ends with incomplete value
                clean_text.rstrip(',') + '}',
                clean_text.rstrip(',') + '}}',
                clean_text.rstrip() + '}',
                clean_text.rstrip() + '}}',
                # If ends with incomplete array
                clean_text.rstrip(',') + ']}',
                clean_text.rstrip(',') + ']}}',
            ]

            for attempt in completion_attempts:
                try:
                    json.loads(attempt)
                    logger.info(f"Successfully completed JSON with pattern completion")
                    return attempt
                except json.JSONDecodeError:
                    continue

            logger.warning("Could not complete truncated JSON")
            return None

        except Exception as e:
            logger.error(f"JSON completion attempt failed: {e}")
            return None

    def _store_analysis_result(self, gui_response: Dict[str, Any], state: CompleteTradingAnalysisState, summarized_tools: Dict[str, Any]):
        """Store analysis result in persistent storage for dashboard display."""
        try:
            trade_storage = get_trade_storage()

            # Extract trading signals and levels from analysis
            trading_signals = gui_response.get("trading_signals", {})

            # Parse entry levels, stop loss, take profit from analysis
            entry_levels = []
            stop_loss = 0.0
            take_profit = []
            confidence_score = 0

            if isinstance(trading_signals, dict):
                # Try to extract trading levels from various possible fields
                entry_levels = trading_signals.get("entry_levels", trading_signals.get("entry", []))
                stop_loss = trading_signals.get("stop_loss", trading_signals.get("sl", 0.0))
                take_profit = trading_signals.get("take_profit", trading_signals.get("tp", []))
                confidence_score = trading_signals.get("confidence", trading_signals.get("confidence_score", 0))

                # Ensure proper types
                if not isinstance(entry_levels, list):
                    entry_levels = [entry_levels] if entry_levels else []
                if not isinstance(take_profit, list):
                    take_profit = [take_profit] if take_profit else []
                if not isinstance(stop_loss, (int, float)):
                    stop_loss = 0.0
                if not isinstance(confidence_score, (int, float)):
                    confidence_score = 0

            # Calculate risk-reward ratio
            risk_reward_ratio = 0.0
            if entry_levels and take_profit and stop_loss > 0:
                avg_entry = sum(entry_levels) / len(entry_levels)
                avg_tp = sum(take_profit) / len(take_profit)
                risk = abs(avg_entry - stop_loss)
                reward = abs(avg_tp - avg_entry)
                if risk > 0:
                    risk_reward_ratio = reward / risk

            # Prepare analysis data for storage
            analysis_data = {
                "symbol": state.get("detected_symbol", ""),
                "market_type": state.get("market_type", ""),
                "analysis_mode": state.get("analysis_mode", "positional"),
                "chart_path": "",  # Could be added if chart is saved
                "analysis_notes": gui_response.get("analysis_notes", ""),
                "trading_signals": trading_signals,
                "tool_results": summarized_tools,
                "confidence_score": int(confidence_score),
                "entry_levels": entry_levels,
                "stop_loss": stop_loss,
                "take_profit": take_profit,
                "risk_reward_ratio": risk_reward_ratio
            }

            # Store in persistent database
            analysis_id = trade_storage.store_analysis_result(analysis_data)

            if analysis_id:
                logger.info(f"✅ Analysis result stored with ID: {analysis_id}")
                # Add analysis_id to response for potential future reference
                gui_response["analysis_id"] = analysis_id
            else:
                logger.warning("⚠️ Failed to store analysis result")

        except Exception as e:
            logger.error(f"Failed to store analysis result: {e}")





    def memory_storage_node(self, state: CompleteTradingAnalysisState) -> CompleteTradingAnalysisState:
        """Store analysis in ChromaDB memory system."""
        logger.info("💾 Storing analysis in memory...")

        try:
            if self.tool_registry and hasattr(self.tool_registry, 'rag_system'):
                # Store analysis in ChromaDB
                symbol = state.get("detected_symbol", "")
                analysis = state.get("analysis", "")

                # Create analysis data even if analysis is empty
                analysis_data = {
                    "analysis": analysis or "Analysis failed",
                    "trading_signals": state.get("trading_signals", {}),
                    "chart_patterns": state.get("chart_patterns", []),
                    "support_levels": state.get("support_levels", []),
                    "resistance_levels": state.get("resistance_levels", []),
                    "workflow_status": state.get("workflow_status", "unknown"),
                    "error": state.get("error", "")
                }

                if symbol:
                    # Store in memory system using correct method signature
                    memory_id = self.tool_registry.rag_system.store_analysis(
                        analysis_data=analysis_data,
                        symbol=symbol,
                        mode=state.get("analysis_mode", "general"),
                        market=state.get("market_type", "general")
                    )

                    if memory_id:
                        state.update({
                            "memory_id": memory_id,
                            "workflow_status": "complete",
                            "progress_messages": state["progress_messages"] + ["💾 Analysis stored in memory"]
                        })
                        logger.info(f"✅ Analysis stored with ID: {memory_id}")
                    else:
                        logger.warning("⚠️ Memory storage returned empty ID")
                else:
                    logger.warning("⚠️ No symbol detected, skipping memory storage")

        except Exception as e:
            logger.error(f"❌ Memory storage failed: {e}")
            # Don't fail the whole workflow for memory storage issues
            state["workflow_status"] = "complete"

        return state

    def analyze_chart(self, chart_images: List[bytes], analysis_mode: str = "positional", user_query: str = None, market_specialization: str = "General") -> Dict[str, Any]:
        """Main analysis function with rate limiting - COMPLETE replacement for old system."""
        logger.info("🚀 Starting complete LangGraph analysis with rate limiting...")

        # 🚦 CHECK RATE LIMITS BEFORE STARTING - Use user's selected model
        model_name = self.model.model_name.replace("models/", "")  # Remove models/ prefix if present
        estimated_tokens = 5000  # Estimate for chart analysis

        can_proceed, reason = self.rate_limiter.check_rate_limit(model_name, estimated_tokens)
        if not can_proceed:
            logger.warning(f"🚫 Rate limit check failed: {reason}")
            return {
                "success": False,
                "error": f"Rate limit exceeded: {reason}",
                "analysis": {"error": f"Rate limit exceeded: {reason}"},
                "tool_usage": [],
                "rate_limit_info": self.rate_limiter.get_usage_summary()
            }

        logger.info(f"✅ Rate limit check passed for {model_name}")

        try:
            # Initialize state
            initial_state = CompleteTradingAnalysisState(
                chart_images=chart_images,
                analysis_mode=analysis_mode,
                user_query=user_query,
                market_specialization=market_specialization,
                progress_messages=[],
                tool_usage_log=[],
                workflow_status="starting"
            )

            # Run workflow with proper configuration
            config = {"configurable": {"thread_id": "trading_analysis"}}
            result = self.workflow.invoke(initial_state, config=config)

            # 🚦 RECORD SUCCESSFUL API USAGE
            if result.get("workflow_status") == "complete":
                # Estimate tokens used (rough calculation)
                estimated_tokens_used = 3000  # Base analysis
                if result.get("tool_usage_log"):
                    estimated_tokens_used += len(result.get("tool_usage_log", [])) * 1000  # Tool summaries

                self.rate_limiter.record_request(model_name, estimated_tokens_used)
                logger.info(f"📊 Recorded API usage: {estimated_tokens_used} tokens")

            # Format result for compatibility
            return {
                "success": result.get("workflow_status") == "complete",
                "detected_symbol": result.get("detected_symbol"),
                "market_type": result.get("market_type"),
                "trading_signals": result.get("trading_signals", {}),
                "analysis": result.get("analysis", ""),
                "tool_usage": result.get("tool_results", {}),  # GUI expects "tool_usage" with rich data
                "progress_messages": result.get("progress_messages", []),
                "workflow_status": result.get("workflow_status"),
                "error": result.get("error"),
                "chart_patterns": result.get("chart_patterns", []),
                "support_levels": result.get("support_levels", []),
                "resistance_levels": result.get("resistance_levels", []),
                "rate_limit_info": self.rate_limiter.get_usage_summary()  # Include rate limit info
            }

        except Exception as e:
            logger.error(f"❌ Complete workflow failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "workflow_status": "failed"
            }

    def _create_compressed_tool_summary(self, summarized_tools: Dict[str, Any], symbol: str, market_type: str) -> str:
        """Create compressed tool summary using Gemini Flash with rate limiting."""
        try:
            if not summarized_tools or not summarized_tools.get("summarized_tools"):
                return "No market data available."

            # Extract key tool summaries
            tool_data = summarized_tools["summarized_tools"]
            summaries = []

            for tool_name, tool_info in tool_data.items():
                summary = tool_info.get("summary", "")
                if summary and len(summary) > 50:  # Only include meaningful summaries
                    summaries.append(f"{tool_name}: {summary}")

            if not summaries:
                return "Market data processing incomplete."

            # Combine all summaries
            combined_summary = "\n".join(summaries)

            # Use Gemini Flash to compress
            compression_prompt = f"""
Compress this market intelligence for {symbol} ({market_type}) trading analysis:

{combined_summary}

Create a concise 3-4 sentence summary focusing on:
1. Key price levels and market sentiment
2. Most important news/events affecting price
3. Trading opportunities or risks

Keep under 200 words, focus on actionable insights.
"""

            # 🚦 Try compression with rate limiting
            compression_model = None
            model_name = None

            # Try 2.5 Flash first
            can_proceed, reason = self.rate_limiter.check_rate_limit("gemini-2.5-flash", 800)
            if can_proceed:
                try:
                    compression_model = genai.GenerativeModel('gemini-2.5-flash')
                    model_name = "gemini-2.5-flash"
                except Exception as e:
                    logger.warning(f"2.5 Flash unavailable: {e}")

            # Fallback to 2.0 Flash
            if compression_model is None:
                can_proceed, reason = self.rate_limiter.check_rate_limit("gemini-2.0-flash", 800)
                if can_proceed:
                    try:
                        compression_model = genai.GenerativeModel('gemini-2.0-flash-exp')
                        model_name = "gemini-2.0-flash"
                    except Exception as e:
                        logger.warning(f"2.0 Flash unavailable: {e}")

            # Try compression if model available
            if compression_model:
                try:
                    response = compression_model.generate_content(compression_prompt)
                    if response and response.text:
                        compressed = response.text.strip()

                        # 🚦 Record successful usage
                        if model_name:
                            self.rate_limiter.record_request(model_name, 600)

                        logger.info(f"✅ Tool summary compressed: {len(combined_summary)} → {len(compressed)} chars")
                        return compressed
                except Exception as e:
                    logger.warning(f"Flash compression failed: {e}")

            # Fallback to truncation if compression unavailable
            logger.info("📝 Using truncation fallback for tool summary")
            return combined_summary[:500] + "..." if len(combined_summary) > 500 else combined_summary

        except Exception as e:
            logger.error(f"Tool summary compression failed: {e}")
            return "Market data compression failed."

    def _create_compressed_rag_summary(self, rag_context: Dict[str, Any], symbol: str, market_type: str, analysis_mode: str) -> str:
        """Create compressed RAG summary with relevancy scoring."""
        try:
            if not rag_context or not rag_context.get("relevant_memories"):
                return "No historical trading context available."

            memories = rag_context["relevant_memories"]
            if not memories:
                return "No relevant historical patterns found."

            # Score memories by relevance (already done by RAG system)
            # Take top 2-3 most relevant memories
            top_memories = memories[:3] if len(memories) > 3 else memories

            # Extract key insights from top memories
            memory_insights = []
            for memory in top_memories:
                content = memory.get("content", "")
                if content and len(content) > 30:
                    # Extract first sentence or key insight
                    first_sentence = content.split('.')[0] + '.' if '.' in content else content[:100]
                    memory_insights.append(first_sentence)

            if not memory_insights:
                return "Historical patterns available but not accessible."

            # Combine insights
            combined_insights = " ".join(memory_insights)

            # Use Gemini Flash to create intelligent summary
            rag_compression_prompt = f"""
Summarize these historical trading patterns for {symbol} ({market_type}) {analysis_mode} analysis:

{combined_insights}

Create a 2-3 sentence summary focusing on:
1. Most relevant historical pattern or outcome
2. Key lesson or trading insight
3. Risk or opportunity based on past performance

Keep under 150 words, focus on actionable historical context.
"""

            try:
                flash_model = genai.GenerativeModel('gemini-2.0-flash-exp')
                response = flash_model.generate_content(rag_compression_prompt)

                if response and response.text:
                    compressed = response.text.strip()
                    logger.info(f"✅ RAG summary compressed: {len(combined_insights)} → {len(compressed)} chars")
                    return compressed
                else:
                    return combined_insights[:300] + "..." if len(combined_insights) > 300 else combined_insights

            except Exception as e:
                logger.warning(f"RAG compression failed: {e}")
                return combined_insights[:300] + "..." if len(combined_insights) > 300 else combined_insights

        except Exception as e:
            logger.error(f"RAG summary compression failed: {e}")
            return "Historical context compression failed."
