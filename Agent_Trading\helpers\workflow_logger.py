"""
Comprehensive Workflow Logger
Tracks the complete LangGraph workflow with detailed step-by-step logging
"""

import json
import time
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class WorkflowLogger:
    """Comprehensive workflow logging system."""
    
    def __init__(self, log_dir: str = "logs"):
        """Initialize workflow logger."""
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Current workflow tracking
        self.current_workflow = None
        self.workflow_start_time = None
        self.step_count = 0
        self.llm_call_count = 0
        self.total_tokens = 0
        self.total_cost = 0.0
        
    def start_workflow(self, workflow_id: str, context: Dict[str, Any]) -> None:
        """Start tracking a new workflow."""
        self.current_workflow = {
            "workflow_id": workflow_id,
            "start_time": datetime.now().isoformat(),
            "context": context,
            "steps": [],
            "llm_calls": [],
            "summary": {
                "total_steps": 0,
                "total_llm_calls": 0,
                "total_tokens": 0,
                "total_cost": 0.0,
                "execution_time": 0.0
            }
        }
        self.workflow_start_time = time.time()
        self.step_count = 0
        self.llm_call_count = 0
        self.total_tokens = 0
        self.total_cost = 0.0
        
        logger.info(f"🚀 Started workflow: {workflow_id}")
    
    def log_step(self, step_name: str, step_type: str, input_data: Any = None, 
                 output_data: Any = None, execution_time: float = None, 
                 status: str = "success", error: str = None) -> None:
        """Log a workflow step."""
        if not self.current_workflow:
            return
            
        self.step_count += 1
        
        step_data = {
            "step_number": self.step_count,
            "step_name": step_name,
            "step_type": step_type,  # "llm_call", "tool_execution", "data_processing", etc.
            "timestamp": datetime.now().isoformat(),
            "execution_time": execution_time,
            "status": status,
            "error": error
        }
        
        # Add input/output data (truncated for readability)
        if input_data:
            step_data["input_summary"] = self._summarize_data(input_data)
        if output_data:
            step_data["output_summary"] = self._summarize_data(output_data)
            
        self.current_workflow["steps"].append(step_data)
        
        status_emoji = "✅" if status == "success" else "❌" if status == "error" else "⚠️"
        logger.info(f"{status_emoji} Step {self.step_count}: {step_name} ({step_type})")
    
    def log_llm_call(self, model: str, prompt_length: int, response_length: int, 
                     tokens_used: int = None, cost: float = None, 
                     execution_time: float = None, context: Dict[str, Any] = None) -> None:
        """Log an LLM API call."""
        if not self.current_workflow:
            return
            
        self.llm_call_count += 1
        if tokens_used:
            self.total_tokens += tokens_used
        if cost:
            self.total_cost += cost
            
        llm_call_data = {
            "call_number": self.llm_call_count,
            "model": model,
            "timestamp": datetime.now().isoformat(),
            "prompt_length": prompt_length,
            "response_length": response_length,
            "tokens_used": tokens_used,
            "estimated_cost": cost,
            "execution_time": execution_time,
            "context": context or {}
        }
        
        self.current_workflow["llm_calls"].append(llm_call_data)
        
        logger.info(f"🤖 LLM Call {self.llm_call_count}: {model} ({tokens_used} tokens, ${cost:.4f})")
    
    def end_workflow(self, status: str = "success", error: str = None) -> Dict[str, Any]:
        """End the current workflow and save logs."""
        if not self.current_workflow:
            return {}
            
        total_time = time.time() - self.workflow_start_time
        
        # Update summary
        self.current_workflow["end_time"] = datetime.now().isoformat()
        self.current_workflow["status"] = status
        self.current_workflow["error"] = error
        self.current_workflow["summary"] = {
            "total_steps": self.step_count,
            "total_llm_calls": self.llm_call_count,
            "total_tokens": self.total_tokens,
            "total_cost": self.total_cost,
            "execution_time": total_time
        }
        
        # Save to file
        self._save_workflow_log()
        
        # Log summary
        status_emoji = "✅" if status == "success" else "❌"
        logger.info(f"{status_emoji} Workflow completed: {self.current_workflow['workflow_id']}")
        logger.info(f"   📊 Steps: {self.step_count}, LLM Calls: {self.llm_call_count}")
        logger.info(f"   🎯 Tokens: {self.total_tokens}, Cost: ${self.total_cost:.4f}")
        logger.info(f"   ⏱️ Time: {total_time:.2f}s")
        
        workflow_summary = self.current_workflow.copy()
        self.current_workflow = None
        return workflow_summary
    
    def _summarize_data(self, data: Any, max_length: int = 200) -> str:
        """Summarize data for logging."""
        try:
            if isinstance(data, str):
                return data[:max_length] + "..." if len(data) > max_length else data
            elif isinstance(data, dict):
                keys = list(data.keys())[:5]  # First 5 keys
                return f"Dict with keys: {keys}" + ("..." if len(data) > 5 else "")
            elif isinstance(data, list):
                return f"List with {len(data)} items"
            else:
                data_str = str(data)
                return data_str[:max_length] + "..." if len(data_str) > max_length else data_str
        except Exception:
            return f"<{type(data).__name__} object>"
    
    def _save_workflow_log(self) -> None:
        """Save workflow log to file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"workflow_{timestamp}_{self.current_workflow['workflow_id']}.json"
            filepath = self.log_dir / filename
            
            with open(filepath, 'w') as f:
                json.dump(self.current_workflow, f, indent=2, default=str)
                
            logger.info(f"💾 Workflow log saved: {filepath}")
            
        except Exception as e:
            logger.error(f"Failed to save workflow log: {e}")
    
    def get_workflow_summary(self) -> Dict[str, Any]:
        """Get current workflow summary."""
        if not self.current_workflow:
            return {}
            
        current_time = time.time() - self.workflow_start_time if self.workflow_start_time else 0
        
        return {
            "workflow_id": self.current_workflow["workflow_id"],
            "status": "in_progress",
            "steps_completed": self.step_count,
            "llm_calls_made": self.llm_call_count,
            "tokens_used": self.total_tokens,
            "estimated_cost": self.total_cost,
            "elapsed_time": current_time,
            "current_step": self.current_workflow["steps"][-1]["step_name"] if self.current_workflow["steps"] else "Starting"
        }

# Global workflow logger instance
workflow_logger = WorkflowLogger()

def log_workflow_step(step_name: str, step_type: str, **kwargs):
    """Convenience function to log workflow steps."""
    workflow_logger.log_step(step_name, step_type, **kwargs)

def log_llm_call(model: str, prompt_length: int, response_length: int, **kwargs):
    """Convenience function to log LLM calls."""
    workflow_logger.log_llm_call(model, prompt_length, response_length, **kwargs)
