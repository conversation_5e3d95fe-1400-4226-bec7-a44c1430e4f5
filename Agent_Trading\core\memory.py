"""
ChromaDB-based RAG System for Trading Analysis
Optimized for contextual retrieval with metadata filtering
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import hashlib
import json

try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    chromadb = None

logger = logging.getLogger(__name__)

class ChromaDBTradingRAGSystem:
    """ChromaDB-based RAG system optimized for trading analysis with metadata filtering"""
    
    def __init__(self, persist_directory: str = "data/chromadb"):
        if not CHROMADB_AVAILABLE:
            raise ImportError("ChromaDB not available. Install with: pip install chromadb")
        
        self.persist_directory = persist_directory
        
        # Initialize ChromaDB client with persistence
        self.client = chromadb.PersistentClient(path=persist_directory)
        
        # Get or create collection for trading analysis
        self.collection = self.client.get_or_create_collection(
            name="trading_analysis",
            metadata={"description": "Trading analysis summaries with contextual metadata"}
        )
        
        logger.info(f"ChromaDB RAG system initialized with {self.collection.count()} existing documents")
    
    def ingest_analysis_summary(self, 
                              summary: str, 
                              symbol: str, 
                              mode: str = "general",
                              market: str = "general",
                              analysis_type: str = "market_data",
                              metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Ingest analysis summary with rich metadata for contextual retrieval
        
        Args:
            summary: The analysis summary text
            symbol: Trading symbol (BTC, NIFTY, etc.)
            mode: Trading mode (positional, scalp)
            market: Market type (crypto, indian_market)
            analysis_type: Type of analysis (market_data, news, technical)
            metadata: Additional metadata
        
        Returns:
            Document ID
        """
        try:
            # Create unique document ID
            doc_id = hashlib.md5(f"{symbol}_{mode}_{datetime.now().isoformat()}_{summary[:50]}".encode()).hexdigest()
            
            # Prepare metadata
            doc_metadata = {
                "symbol": symbol.upper(),
                "mode": mode.lower(),
                "market": market.lower(),
                "analysis_type": analysis_type,
                "timestamp": datetime.now().isoformat(),
                "date": datetime.now().strftime("%Y-%m-%d"),
                "month": datetime.now().strftime("%Y-%m"),
                **(metadata or {})
            }
            
            # Add to ChromaDB collection
            self.collection.add(
                documents=[summary],
                metadatas=[doc_metadata],
                ids=[doc_id]
            )
            
            logger.info(f"Ingested {analysis_type} summary for {symbol} {mode} analysis")
            return doc_id
            
        except Exception as e:
            logger.error(f"Failed to ingest analysis summary: {e}")
            return ""
    
    def ingest_market_data(self, market_data: Dict[str, Any], symbol: str, mode: str = "general", market: str = "general"):
        """Ingest market data summary"""
        try:
            # Create summary from market data
            summary_parts = []
            
            if isinstance(market_data, dict):
                if "current_price" in market_data:
                    summary_parts.append(f"{symbol} price: {market_data['current_price']}")
                if "price_change_pct" in market_data:
                    summary_parts.append(f"Change: {market_data['price_change_pct']}%")
                if "volume" in market_data:
                    summary_parts.append(f"Volume: {market_data['volume']}")
                if "trend" in market_data:
                    summary_parts.append(f"Trend: {market_data['trend']}")
                if "support_levels" in market_data:
                    summary_parts.append(f"Support: {market_data['support_levels']}")
                if "resistance_levels" in market_data:
                    summary_parts.append(f"Resistance: {market_data['resistance_levels']}")
            
            summary = f"{symbol} Market Data: " + ", ".join(summary_parts)
            
            # Extract price level for metadata
            price_level = market_data.get("current_price", 0) if isinstance(market_data, dict) else 0
            
            metadata = {
                "price_level": price_level,
                "data_source": "market_data_tool"
            }
            
            return self.ingest_analysis_summary(summary, symbol, mode, market, "market_data", metadata)
            
        except Exception as e:
            logger.error(f"Failed to ingest market data: {e}")
    
    def ingest_news_data(self, news_data: Dict[str, Any], symbol: str, mode: str = "general", market: str = "general"):
        """Ingest news data summary"""
        try:
            summary_parts = []
            
            if isinstance(news_data, dict) and "news_articles" in news_data:
                for article in news_data["news_articles"][:3]:  # Top 3 articles
                    title = article.get("title", "")
                    snippet = article.get("snippet", "")
                    summary_parts.append(f"{title}: {snippet}")
            
            summary = f"{symbol} News Summary: " + " | ".join(summary_parts)
            
            metadata = {
                "news_count": len(news_data.get("news_articles", [])) if isinstance(news_data, dict) else 0,
                "data_source": "news_tool"
            }
            
            return self.ingest_analysis_summary(summary, symbol, mode, market, "news", metadata)
            
        except Exception as e:
            logger.error(f"Failed to ingest news data: {e}")
    
    def get_contextual_memory(self, 
                            symbol: str, 
                            mode: str = "general",
                            market: str = "general",
                            query: str = "",
                            n_results: int = 5) -> str:
        """
        Get contextual memory with metadata filtering
        
        Args:
            symbol: Trading symbol to filter by
            mode: Trading mode to filter by
            market: Market type to filter by
            query: Semantic query text
            n_results: Number of results to return
        
        Returns:
            Formatted context string
        """
        try:
            # Build metadata filter
            where_filter = {
                "symbol": symbol.upper()
            }
            
            # Add optional filters
            if mode != "general":
                where_filter["mode"] = mode.lower()
            if market != "general":
                where_filter["market"] = market.lower()
            
            # Query with metadata filtering
            if query:
                search_query = f"{symbol} {mode} {query}"
            else:
                search_query = f"{symbol} {mode} analysis patterns and signals"

            # ChromaDB requires specific where clause format
            if len(where_filter) == 1:
                # Single filter
                key, value = next(iter(where_filter.items()))
                where_clause = {key: {"$eq": value}}
            else:
                # Multiple filters - use $and
                where_clause = {"$and": [{k: {"$eq": v}} for k, v in where_filter.items()]}

            results = self.collection.query(
                query_texts=[search_query],
                where=where_clause,
                n_results=n_results
            )
            
            if not results["documents"] or not results["documents"][0]:
                return ""
            
            # Format results
            context_parts = []
            for i, (doc, metadata) in enumerate(zip(results["documents"][0], results["metadatas"][0])):
                timestamp = metadata.get("date", "Unknown date")
                analysis_type = metadata.get("analysis_type", "analysis")
                context_parts.append(f"[{timestamp} {analysis_type.title()}] {doc}")
            
            context = "\n".join(context_parts)
            logger.info(f"Retrieved {len(context_parts)} contextual memories for {symbol} {mode}")
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to retrieve contextual memory: {e}")
            return ""
    
    def get_relevant_context(self, query: str, n_results: int = 5) -> str:
        """
        Get relevant context using semantic search (backward compatibility)
        """
        try:
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results
            )
            
            if not results["documents"] or not results["documents"][0]:
                return ""
            
            # Format results
            context_parts = []
            for doc, metadata in zip(results["documents"][0], results["metadatas"][0]):
                symbol = metadata.get("symbol", "")
                mode = metadata.get("mode", "")
                date = metadata.get("date", "")
                context_parts.append(f"[{symbol} {mode} - {date}] {doc}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Failed to get relevant context: {e}")
            return ""
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the RAG collection"""
        try:
            count = self.collection.count()
            
            # Get sample of metadata to analyze
            if count > 0:
                sample = self.collection.get(limit=min(100, count))
                symbols = set()
                modes = set()
                markets = set()
                
                for metadata in sample["metadatas"]:
                    symbols.add(metadata.get("symbol", ""))
                    modes.add(metadata.get("mode", ""))
                    markets.add(metadata.get("market", ""))
                
                return {
                    "total_documents": count,
                    "unique_symbols": len(symbols),
                    "symbols": list(symbols),
                    "modes": list(modes),
                    "markets": list(markets)
                }
            else:
                return {"total_documents": 0}
                
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {"error": str(e)}

# Backward compatibility wrapper
class EnhancedTradingRAGSystem:
    """Wrapper for backward compatibility - now uses ChromaDB exclusively"""

    def __init__(self, db_path: str = "data/chromadb"):
        if CHROMADB_AVAILABLE:
            self.rag_system = ChromaDBTradingRAGSystem(db_path)
        else:
            raise ImportError("ChromaDB required for best-in-class trading analysis. Install with: pip install chromadb")
    
    def ingest_market_data(self, market_data: Dict[str, Any], symbol: str):
        if hasattr(self.rag_system, 'ingest_market_data'):
            return self.rag_system.ingest_market_data(market_data, symbol)
    
    def ingest_news_data(self, news_data: Dict[str, Any], symbol: str):
        if hasattr(self.rag_system, 'ingest_news_data'):
            return self.rag_system.ingest_news_data(news_data, symbol)
    
    def get_relevant_context(self, query: str, n_results: int = 5) -> str:
        return self.rag_system.get_relevant_context(query, n_results)
    
    def get_contextual_memory(self, symbol: str, mode: str = "general", market: str = "general", query: str = "") -> str:
        if hasattr(self.rag_system, 'get_contextual_memory'):
            return self.rag_system.get_contextual_memory(symbol, mode, market, query)
        else:
            return self.rag_system.get_relevant_context(f"{symbol} {mode} {query}")
