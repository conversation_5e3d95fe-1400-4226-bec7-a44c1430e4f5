#!/usr/bin/env python3
"""
🧪 RATE LIMITER TEST SCRIPT
Test the Gemini API rate limiter with official Google limits.
"""

import sys
import os
import time
from datetime import datetime

# Add project root to path
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(script_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from helpers.gemini_rate_limiter import get_rate_limiter

def test_rate_limiter():
    """Test rate limiter functionality."""
    print("🚦 TESTING GEMINI API RATE LIMITER")
    print("=" * 50)
    
    # Test different tiers
    tiers = ["free", "tier1", "tier2", "tier3"]
    models = ["gemini-2.5-pro", "gemini-2.5-flash", "gemini-2.0-flash"]
    
    for tier in tiers:
        print(f"\n📊 TESTING {tier.upper()} TIER")
        print("-" * 30)
        
        rate_limiter = get_rate_limiter(tier)
        
        # Get tier info
        tier_info = rate_limiter.get_tier_info()
        print(f"Description: {tier_info['description']}")
        print(f"Upgrade Requirements: {tier_info['upgrade_requirements']}")
        
        # Test each model
        for model in models:
            limits = rate_limiter.get_rate_limits(model)
            if limits:
                print(f"\n🤖 {model}:")
                print(f"  RPM: {limits.rpm:,}")
                print(f"  TPM: {limits.tpm:,}")
                print(f"  RPD: {limits.rpd:,}" if limits.rpd != 999999 else "  RPD: No limit")
                
                # Test rate limit check
                can_proceed, reason = rate_limiter.check_rate_limit(model, 1000)
                print(f"  Status: {reason}")
                
                if can_proceed:
                    # Simulate a request
                    rate_limiter.record_request(model, 1000)
                    print(f"  ✅ Recorded test request (1000 tokens)")
            else:
                print(f"\n🤖 {model}: Not available in {tier}")
    
    print("\n" + "=" * 50)
    print("🎯 USAGE SUMMARY TEST")
    print("=" * 50)
    
    # Test usage summary for free tier
    rate_limiter = get_rate_limiter("free")
    
    # Simulate some usage
    test_models = ["gemini-2.0-flash", "gemini-2.5-flash"]
    for i, model in enumerate(test_models):
        for j in range(3):  # 3 requests per model
            can_proceed, reason = rate_limiter.check_rate_limit(model, 500)
            if can_proceed:
                rate_limiter.record_request(model, 500 + (i * 100) + (j * 50))
                print(f"✅ Recorded request {j+1} for {model}")
    
    # Get comprehensive usage summary
    summary = rate_limiter.get_usage_summary()
    
    print(f"\n📊 USAGE SUMMARY:")
    print(f"Tier: {summary['tier']}")
    print(f"Total Requests Today: {summary['total_requests_today']}")
    print(f"Active Models: {len(summary['models'])}")
    
    for model, stats in summary['models'].items():
        print(f"\n🤖 {model.upper()}:")
        print(f"  Requests/Minute: {stats['requests_this_minute']}")
        print(f"  Tokens/Minute: {stats['tokens_this_minute']}")
        print(f"  Requests/Day: {stats['requests_today']}")
        print(f"  RPM Usage: {stats['rpm_usage_percent']}%")
        print(f"  TPM Usage: {stats['tpm_usage_percent']}%")
        print(f"  RPD Usage: {stats['rpd_usage_percent']}%")
    
    print("\n" + "=" * 50)
    print("🔄 RATE LIMIT SIMULATION TEST")
    print("=" * 50)
    
    # Test rate limit enforcement
    rate_limiter = get_rate_limiter("free")
    model = "gemini-2.5-flash"  # 10 RPM limit on free tier
    
    print(f"Testing {model} with 10 RPM limit...")
    
    # Try to make 12 requests (should hit limit at 11th)
    for i in range(12):
        can_proceed, reason = rate_limiter.check_rate_limit(model, 1000)
        if can_proceed:
            rate_limiter.record_request(model, 1000)
            print(f"✅ Request {i+1}: {reason}")
        else:
            print(f"🚫 Request {i+1}: {reason}")
            break
    
    print("\n🎯 TEST COMPLETED!")
    print("Rate limiter is working correctly with official Google limits.")

def test_dashboard_data():
    """Test data format for dashboard display."""
    print("\n" + "=" * 50)
    print("📊 DASHBOARD DATA FORMAT TEST")
    print("=" * 50)
    
    rate_limiter = get_rate_limiter("tier1")
    
    # Simulate some usage
    rate_limiter.record_request("gemini-2.5-pro", 2000)
    rate_limiter.record_request("gemini-2.5-flash", 1500)
    
    # Get usage summary
    summary = rate_limiter.get_usage_summary()
    
    print("📋 Dashboard Data Structure:")
    print(f"Tier: {summary['tier']}")
    print(f"Total Requests: {summary['total_requests_today']}")
    print(f"Models: {list(summary['models'].keys())}")
    
    # Test tier info
    tier_info = rate_limiter.get_tier_info()
    print(f"\n🎯 Tier Info:")
    print(f"Current: {tier_info['current_tier']}")
    print(f"Description: {tier_info['description']}")
    print(f"Upgrade: {tier_info['upgrade_requirements']}")
    
    print("\n✅ Dashboard data format is correct!")

if __name__ == "__main__":
    print(f"🕒 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        test_rate_limiter()
        test_dashboard_data()
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print("Rate limiter is ready for production use.")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🕒 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
