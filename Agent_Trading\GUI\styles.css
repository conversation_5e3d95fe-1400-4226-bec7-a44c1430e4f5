/* Professional Trading Analysis Dashboard Styles */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #1f2937;
    --secondary-color: #374151;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --background-light: #f9fafb;
    --background-white: #ffffff;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
}

/* Global Styles */
.stApp {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: var(--background-light);
    min-height: 100vh;
}

/* Add subtle gradient only to header */
.main-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--border-radius-lg);
    margin-bottom: 2rem;
    padding: 2rem;
}

/* Main Container */
.main-container {
    background: var(--background-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    margin: 1rem;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

/* Header Styles */
.main-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.main-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.main-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* Card Styles */
.analysis-card {
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.analysis-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* Button Styles */
.stButton > button {
    background: linear-gradient(135deg, var(--accent-color) 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.stButton > button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    background: linear-gradient(135deg, #1d4ed8 0%, var(--accent-color) 100%);
}

/* Success Button */
.success-button > button {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
}

/* Warning Button */
.warning-button > button {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
}

/* Danger Button */
.danger-button > button {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
}

/* Sidebar Styles */
.css-1d391kg, .css-1lcbmhc, .css-17eq0hr {
    background: var(--background-white) !important;
    border-right: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* Sidebar text and elements */
.css-1d391kg .stSelectbox label,
.css-1d391kg .stRadio label,
.css-1d391kg .stCheckbox label,
.css-1d391kg .stSlider label,
.css-1d391kg .stTextInput label,
.css-1d391kg .stMultiSelect label,
.css-1d391kg h1, .css-1d391kg h2, .css-1d391kg h3,
.css-1d391kg p, .css-1d391kg span, .css-1d391kg div {
    color: var(--text-primary) !important;
}

/* Main content area */
.css-1y4p8pa, .css-12oz5g7, .css-1rs6os {
    background: var(--background-light) !important;
    color: var(--text-primary) !important;
}

/* Ensure all text is visible */
.stApp, .stApp * {
    color: var(--text-primary) !important;
}

/* Fix Streamlit specific elements */
.stMarkdown, .stText, .stCaption {
    color: var(--text-primary) !important;
}

/* Fix selectbox and other input elements */
.stSelectbox > div > div {
    background-color: var(--background-white) !important;
    color: var(--text-primary) !important;
}

/* Fix radio buttons */
.stRadio > div {
    background-color: transparent !important;
}

/* Fix multiselect */
.stMultiSelect > div > div {
    background-color: var(--background-white) !important;
}

/* Fix metrics */
.metric-container .metric-value {
    color: var(--primary-color) !important;
}

/* Override any remaining blue backgrounds */
[data-testid="stSidebar"] {
    background-color: var(--background-white) !important;
}

[data-testid="stAppViewContainer"] {
    background-color: var(--background-light) !important;
}

/* File Uploader */
.stFileUploader {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    background: var(--background-light);
    transition: all 0.3s ease;
}

.stFileUploader:hover {
    border-color: var(--accent-color);
    background: rgba(59, 130, 246, 0.05);
}

/* Progress Bar */
.stProgress > div > div {
    background: linear-gradient(90deg, var(--accent-color) 0%, var(--success-color) 100%);
    border-radius: var(--border-radius);
}

/* Metrics */
.metric-container {
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
}

.metric-card {
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    flex: 1;
    text-align: center;
    box-shadow: var(--shadow-sm);
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* Analysis Results */
.analysis-result {
    background: var(--background-light);
    border-left: 4px solid var(--accent-color);
    padding: 1.5rem;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    margin: 1rem 0;
}

/* Trading Signals */
.signal-buy {
    color: var(--success-color);
    font-weight: 600;
}

.signal-sell {
    color: var(--danger-color);
    font-weight: 600;
}

.signal-hold {
    color: var(--warning-color);
    font-weight: 600;
}

/* Tool Results */
.tool-result {
    background: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 0.5rem 0;
}

.tool-header {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Status Indicators */
.status-success {
    color: var(--success-color);
}

.status-warning {
    color: var(--warning-color);
}

.status-error {
    color: var(--danger-color);
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-container {
        margin: 0.5rem;
        padding: 1rem;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .metric-container {
        flex-direction: column;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-light);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}
