{"workflow_id": "trading_analysis_1754160470", "start_time": "2025-08-03T00:17:50.521229", "context": {"analysis_mode": "positional", "market_specialization": "Crypto", "chart_count": 1}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T00:17:50.522191", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T00:17:53.284771", "execution_time": 2.****************, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'support_levels', 'resistance_levels']..."}, {"step_number": 3, "step_name": "Tool Execution", "step_type": "tool_execution", "timestamp": "2025-08-03T00:17:53.284771", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: indian, Symbol: BANKNIFTY"}, {"step_number": 4, "step_name": "Tool Execution", "step_type": "tool_execution", "timestamp": "2025-08-03T00:17:53.284771", "execution_time": 0.0, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'error', 'tool_results', 'tool_usage_log']"}, {"step_number": 5, "step_name": "Tool Summarization", "step_type": "llm_call", "timestamp": "2025-08-03T00:17:53.284771", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 6, "step_name": "Tool Summarization", "step_type": "llm_call", "timestamp": "2025-08-03T00:17:53.284771", "execution_time": 0.0, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'error', 'summarized_tools']"}, {"step_number": 7, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T00:17:54.718574", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG context"}, {"step_number": 8, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T00:18:19.986327", "execution_time": 25.26775336265564, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis', 'analysis_notes', 'trading_signals', 'context_used']..."}], "llm_calls": [], "summary": {"total_steps": 8, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 29.46509838104248}, "end_time": "2025-08-03T00:18:19.986327", "status": "success", "error": null}