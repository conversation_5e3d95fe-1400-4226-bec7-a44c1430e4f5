{"workflow_id": "test_workflow", "start_time": "2025-08-02T21:27:27.582932", "context": {"test": "data"}, "steps": [{"step_number": 1, "step_name": "Test Step 1", "step_type": "llm_call", "timestamp": "2025-08-02T21:27:27.582995", "execution_time": 1.5, "status": "success", "error": null, "input_summary": "test input", "output_summary": "test output"}], "llm_calls": [{"call_number": 1, "model": "gemini-2.0-flash-exp", "timestamp": "2025-08-02T21:27:27.583013", "prompt_length": 100, "response_length": 200, "tokens_used": 300, "estimated_cost": 0.001, "execution_time": 2.0, "context": {}}], "summary": {"total_steps": 1, "total_llm_calls": 1, "total_tokens": 300, "total_cost": 0.001, "execution_time": 5.364418029785156e-05}, "end_time": "2025-08-02T21:27:27.583033", "status": "success", "error": null}