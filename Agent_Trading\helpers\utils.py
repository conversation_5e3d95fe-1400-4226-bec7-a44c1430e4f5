"""
Clean utilities module for the trading analysis system.
Contains only essential utility functions.
"""

import os
import json
import logging
import subprocess
from typing import Dict, Any, List, Tuple, Optional
from PIL import Image

logger = logging.getLogger(__name__)


def load_google_api_key() -> str:
    """Load Google API key from config.json"""
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        config_path = os.path.join(project_root, "config.json")

        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config = json.load(f)
                api_key = config.get("google_api_key")
                if api_key:
                    return api_key
                else:
                    raise ValueError("google_api_key not found in config.json")
        else:
            raise FileNotFoundError("config.json not found")

    except Exception as e:
        logger.error(f"Failed to load Google API key: {e}")
        raise


def check_ollama_status() -> <PERSON><PERSON>[bool, List[str]]:
    """Check if <PERSON>lla<PERSON> is running and get available models"""
    try:
        # Check if <PERSON>llama is running
        result = subprocess.run(
            ["ollama", "list"],
            capture_output=True,
            text=True,
            timeout=10
        )

        if result.returncode == 0:
            # Parse model list
            lines = result.stdout.strip().split('\n')
            models = []
            for line in lines[1:]:  # Skip header
                if line.strip():
                    model_name = line.split()[0]
                    models.append(model_name)
            return True, models
        else:
            return False, []

    except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
        return False, []


def generate_analysis_local(images: List[Image.Image], prompt: str, model_name: str) -> Dict[str, Any]:
    """
    Generate analysis using local Ollama model.
    This is a placeholder implementation.
    """
    try:
        # This is a simplified implementation
        # In a real scenario, you'd integrate with Ollama's vision models
        logger.warning("Local analysis not fully implemented - using placeholder")

        return {
            "success": True,
            "analysis": f"Local analysis using {model_name} (placeholder implementation)",
            "trading_signals": {
                "signal": "HOLD",
                "confidence": 50,
                "entry_price": "N/A",
                "stop_loss": "N/A",
                "take_profit": "N/A"
            },
            "detected_symbol": "Unknown",
            "market_type": "unknown",
            "timestamp": "placeholder",
            "system_version": "Local Ollama v1.0"
        }

    except Exception as e:
        logger.error(f"Local analysis failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "analysis": "Local analysis failed",
            "system_version": "Local Ollama v1.0"
        }


def get_memory_system():
    """Get the ChromaDB memory system"""
    from .chromadb_rag_system import ChromaDBTradingRAGSystem
    return ChromaDBTradingRAGSystem()