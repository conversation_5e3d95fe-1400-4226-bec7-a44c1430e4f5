//@version=5
indicator("VP Financials - The Definitive Clone", shorttitle="VP Definitive Clone", overlay=true)

// --- SETTINGS ---
// Timeframe for the SMOOTH Moving Averages.
ma_htf = input.timeframe("5", "MA Timeframe")
// Settings for the JAGGED Supertrend (on chart timeframe).
st_atr = input.int(12, "Supertrend ATR Period")
st_factor = input.float(10.0, "Supertrend Factor")
// Settings for the Moving Averages.
fast_len = input.int(9, "Fast MA Period")
slow_len = input.int(21, "Slow MA Period")
price_source = input.source(hlc3, "Price Source")

// --- CALCULATIONS ---

// 1. SUPERTRend ON CHART TIMEFRAME
// This creates the jagged, responsive line.
[supertrend, direction] = ta.supertrend(st_factor, st_atr)

// 2. SIMPLE MOVING AVERAGE (SMA) ON 5-MINUTE TIMEFRAME
// `lookahead = barmerge.lookahead_on` ensures the lines are smooth curves.
htf_fast_ma = request.security(syminfo.tickerid, ma_htf, ta.sma(price_source, fast_len), lookahead = barmerge.lookahead_on)
htf_slow_ma = request.security(syminfo.tickerid, ma_htf, ta.sma(price_source, slow_len), lookahead = barmerge.lookahead_on)


// --- PLOTTING ---

// Cloud Color: Uptrend (direction > 0) is RED, Downtrend is BLUE.
cloud_color = direction > 0 ? color.new(color.red, 85) : color.new(color.blue, 85)

// Create the plot boundaries for the fill.
// Boundary 1 is the JAGGED Supertrend.
plot_st = plot(supertrend, "Boundary 1: Supertrend", color=color.new(color.white, 100))
// THIS IS THE KEY CHANGE: Boundary 2 is now the PRICE itself (using our source).
plot_price = plot(price_source, "Boundary 2: Price", color=color.new(color.white, 100))

// Fill the area between the JAGGED Supertrend and the PRICE.
// This creates the correct visual of the cloud filling to the candles.
fill(plot_st, plot_price, color=cloud_color, title="Trend Cloud")

// Plot the visible SMOOTH MA lines ON TOP of the new, correct cloud.
plot(htf_fast_ma, "Smooth Fast MA", color=color.new(color.yellow, 0), linewidth=2)
plot(htf_slow_ma, "Smooth Slow MA", color=color.new(color.fuchsia, 0), linewidth=2)


// --- SIGNALS ---
// Signals are based on the chart's Supertrend flip.
buy_signal = ta.change(direction) > 0
sell_signal = ta.change(direction) < 0
plotshape(buy_signal, "Buy", style=shape.triangleup, location=location.belowbar, color=color.new(color.blue, 0), size=size.normal)
plotshape(sell_signal, "Sell", style=shape.triangledown, location=location.abovebar, color=color.red, size=size.normal)