"""
Parallel execution utilities for trading analysis tools.
Enables concurrent execution of multiple tools for better performance.
"""

import logging
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any, Callable, Optional
import google.generativeai as genai
from datetime import datetime

logger = logging.getLogger(__name__)

def execute_tools_parallel(tool_calls: List[Dict[str, Any]], 
                          tool_functions: Dict[str, Callable], 
                          progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    Execute multiple tools in parallel for better performance.
    
    Args:
        tool_calls: List of tool calls with name and args
        tool_functions: Dictionary mapping tool names to functions
        progress_callback: Optional callback for progress updates
    
    Returns:
        Dictionary with results, execution log, and timing info
    """
    start_time = time.time()
    results = {}
    execution_log = []
    
    if progress_callback:
        progress_callback(f"🚀 Starting parallel execution of {len(tool_calls)} tools...")
    
    # Use ThreadPoolExecutor for parallel execution
    with ThreadPoolExecutor(max_workers=min(len(tool_calls), 4)) as executor:
        # Submit all tool calls
        future_to_tool = {}
        for tool_call in tool_calls:
            tool_name = tool_call["name"]
            tool_args = tool_call.get("args", {})
            
            if tool_name in tool_functions:
                future = executor.submit(
                    _execute_single_tool, 
                    tool_name, 
                    tool_functions[tool_name], 
                    tool_args
                )
                future_to_tool[future] = tool_name
            else:
                logger.warning(f"Tool {tool_name} not found in tool_functions")
                results[tool_name] = {"error": f"Tool {tool_name} not available"}
        
        # Collect results as they complete
        completed_count = 0
        for future in as_completed(future_to_tool):
            tool_name = future_to_tool[future]
            completed_count += 1
            
            try:
                result = future.result()
                results[tool_name] = result["data"]
                execution_log.append({
                    "tool": tool_name,
                    "status": "success",
                    "execution_time": result["execution_time"],
                    "timestamp": datetime.now().isoformat()
                })
                
                if progress_callback:
                    progress_callback(f"✅ Completed {tool_name} ({completed_count}/{len(tool_calls)})")
                
                logger.info(f"✅ {tool_name} completed in {result['execution_time']:.2f}s")
                
            except Exception as e:
                logger.error(f"❌ {tool_name} failed: {e}")
                results[tool_name] = {"error": str(e)}
                execution_log.append({
                    "tool": tool_name,
                    "status": "failed",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
                
                if progress_callback:
                    progress_callback(f"❌ Failed {tool_name} ({completed_count}/{len(tool_calls)})")
    
    total_time = time.time() - start_time
    
    if progress_callback:
        progress_callback(f"🎯 Parallel execution completed in {total_time:.2f}s")
    
    return {
        "results": results,
        "execution_log": execution_log,
        "total_time": total_time,
        "success_count": len([log for log in execution_log if log["status"] == "success"]),
        "failure_count": len([log for log in execution_log if log["status"] == "failed"])
    }

def _execute_single_tool(tool_name: str, tool_function: Callable, tool_args: Dict[str, Any]) -> Dict[str, Any]:
    """Execute a single tool and return result with timing."""
    start_time = time.time()
    
    try:
        result = tool_function(**tool_args)
        execution_time = time.time() - start_time
        
        return {
            "data": result,
            "execution_time": execution_time,
            "status": "success"
        }
        
    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"Tool {tool_name} execution failed: {e}")
        
        return {
            "data": {"error": str(e)},
            "execution_time": execution_time,
            "status": "failed"
        }

def summarize_tool_result(tool_name: str, tool_data: Any, use_llm_summary: bool = True) -> str:
    """
    Summarize tool result data for better context management.
    
    Args:
        tool_name: Name of the tool
        tool_data: Raw tool output data
        use_llm_summary: Whether to use LLM for intelligent summarization
    
    Returns:
        Summarized string representation of the tool data
    """
    try:
        if use_llm_summary:
            return _create_llm_summary(tool_name, tool_data)
        else:
            return _create_simple_summary(tool_name, tool_data)
            
    except Exception as e:
        logger.error(f"Failed to summarize {tool_name}: {e}")
        return f"Summarization failed for {tool_name}: {str(e)}"

def _create_llm_summary(tool_name: str, tool_data: Any) -> str:
    """Create intelligent summary using Gemini Flash."""
    try:
        # Convert tool data to string representation
        if isinstance(tool_data, dict):
            data_str = str(tool_data)
        elif isinstance(tool_data, list):
            data_str = str(tool_data)
        else:
            data_str = str(tool_data)
        
        # Limit data size for LLM processing
        if len(data_str) > 3000:
            data_str = data_str[:3000] + "... [truncated]"
        
        # Create summarization prompt
        prompt = f"""
        Summarize this {tool_name} data for trading analysis context:
        
        {data_str}
        
        Provide a concise 2-3 sentence summary focusing on:
        1. Key trading-relevant information
        2. Market sentiment or price impact
        3. Actionable insights for traders
        
        Keep it under 200 words and focus on what matters for trading decisions.
        """
        
        # Use Gemini Flash for summarization
        try:
            model = genai.GenerativeModel('gemini-2.0-flash-exp')
            response = model.generate_content(prompt)
            
            if response and response.text:
                return response.text.strip()
            else:
                return _create_simple_summary(tool_name, tool_data)
                
        except Exception as e:
            logger.warning(f"LLM summarization failed for {tool_name}: {e}")
            return _create_simple_summary(tool_name, tool_data)
            
    except Exception as e:
        logger.error(f"LLM summary creation failed: {e}")
        return _create_simple_summary(tool_name, tool_data)

def _create_simple_summary(tool_name: str, tool_data: Any) -> str:
    """Create simple text-based summary without LLM."""
    try:
        if isinstance(tool_data, dict):
            if "error" in tool_data:
                return f"{tool_name}: Error - {tool_data['error']}"
            
            # Extract key information
            summary_parts = []
            
            # Look for common trading data fields
            key_fields = ['price', 'volume', 'sentiment', 'news', 'signal', 'trend', 'support', 'resistance']
            
            for field in key_fields:
                if field in tool_data:
                    summary_parts.append(f"{field}: {tool_data[field]}")
            
            if summary_parts:
                return f"{tool_name}: " + ", ".join(summary_parts[:3])  # Limit to 3 key fields
            else:
                # Fallback to first few key-value pairs
                items = list(tool_data.items())[:3]
                summary_parts = [f"{k}: {v}" for k, v in items]
                return f"{tool_name}: " + ", ".join(summary_parts)
        
        elif isinstance(tool_data, list):
            return f"{tool_name}: {len(tool_data)} items - {str(tool_data[:2])[:100]}..."
        
        else:
            return f"{tool_name}: {str(tool_data)[:150]}..."
            
    except Exception as e:
        return f"{tool_name}: Summary creation failed - {str(e)}"
