{"workflow_id": "trading_analysis_1754159549", "start_time": "2025-08-03T00:02:29.445689", "context": {"analysis_mode": "positional", "market_specialization": "Crypto", "chart_count": 1}, "steps": [{"step_number": 1, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T00:02:29.447227", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart image for symbol detection"}, {"step_number": 2, "step_name": "Chart Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T00:02:34.120712", "execution_time": 4.****************, "status": "success", "error": null, "output_summary": "Dict with keys: ['detected_symbol', 'market_type', 'timeframe', 'support_levels', 'resistance_levels']..."}, {"step_number": 3, "step_name": "Tool Execution", "step_type": "tool_execution", "timestamp": "2025-08-03T00:02:34.120712", "execution_time": null, "status": "success", "error": null, "input_summary": "Market: indian, Symbol: BANKNIFTY"}, {"step_number": 4, "step_name": "Tool Execution", "step_type": "tool_execution", "timestamp": "2025-08-03T00:02:39.312238", "execution_time": 5.**************, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'tool_results', 'tool_usage_log', 'execution_time']"}, {"step_number": 5, "step_name": "Tool Summarization", "step_type": "llm_call", "timestamp": "2025-08-03T00:02:39.312238", "execution_time": null, "status": "success", "error": null, "input_summary": "Tool results for summarization"}, {"step_number": 6, "step_name": "Tool Summarization", "step_type": "llm_call", "timestamp": "2025-08-03T00:02:49.717366", "execution_time": 10.***************, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'summarized_tools', 'tool_count']"}, {"step_number": 7, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T00:02:51.831623", "execution_time": null, "status": "success", "error": null, "input_summary": "Chart + Tool summaries + RAG context"}, {"step_number": 8, "step_name": "Final Analysis", "step_type": "llm_call", "timestamp": "2025-08-03T00:03:35.233534", "execution_time": 43.40191173553467, "status": "success", "error": null, "output_summary": "Dict with keys: ['success', 'analysis', 'analysis_notes', 'trading_signals', 'context_used']..."}], "llm_calls": [], "summary": {"total_steps": 8, "total_llm_calls": 0, "total_tokens": 0, "total_cost": 0.0, "execution_time": 65.78869128227234}, "end_time": "2025-08-03T00:03:35.234380", "status": "success", "error": null}