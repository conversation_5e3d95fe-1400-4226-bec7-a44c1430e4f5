"""
Indian Market Specialized Tools
Enhanced tools for Bank Nifty, Nifty 50, and Indian market analysis
"""

import requests
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import re

logger = logging.getLogger(__name__)

class IndianMarketAnalyzer:
    """Specialized analyzer for Indian markets"""
    
    def __init__(self):
        self.symbol_mapping = {
            # Visual/Text patterns to actual symbols
            'bank nifty': '^NSEBANK',
            'banknifty': '^NSEBANK', 
            'bank_nifty': '^NSEBANK',
            'nifty 50': '^NSEI',
            'nifty': '^NSEI',
            'nifty50': '^NSEI',
            'sensex': '^BSESN',
            'bse': '^BSESN',
            
            # Futures mapping
            'banknifty_fut': 'BANKNIFTY',
            'nifty_fut': 'NIFTY',
            'banknifty futures': 'BANKNIFTY',
            'nifty futures': 'NIFTY'
        }
        
        self.news_sources = {
            'economic_times': 'https://economictimes.indiatimes.com/markets/rss/markets',
            'moneycontrol': 'https://www.moneycontrol.com/rss/marketstocks.xml',
            'business_standard': 'https://www.business-standard.com/rss/markets-106.rss',
            'livemint': 'https://www.livemint.com/rss/markets',
            'financial_express': 'https://www.financialexpress.com/market/rss/'
        }
    
    def detect_symbol_from_text(self, text: str) -> str:
        """Detect trading symbol from chart text or user input"""
        text_lower = text.lower()
        
        # Check for exact matches first
        for pattern, symbol in self.symbol_mapping.items():
            if pattern in text_lower:
                logger.info(f"Detected symbol: {symbol} from pattern: {pattern}")
                return symbol
        
        # Check for partial matches
        if 'bank' in text_lower and 'nifty' in text_lower:
            return '^NSEBANK'
        elif 'nifty' in text_lower and ('50' in text_lower or 'fifty' in text_lower):
            return '^NSEI'
        elif 'nifty' in text_lower:
            return '^NSEI'
        elif 'sensex' in text_lower or 'bse' in text_lower:
            return '^BSESN'
        
        # Default fallback
        logger.warning(f"Could not detect symbol from text: {text}")
        return '^NSEI'  # Default to Nifty 50
    
    def get_futures_symbol(self, base_symbol: str, expiry_type: str = "current") -> str:
        """Get appropriate futures symbol with expiry"""
        base_mapping = {
            '^NSEBANK': 'BANKNIFTY',
            '^NSEI': 'NIFTY'
        }
        
        if base_symbol in base_mapping:
            futures_base = base_mapping[base_symbol]
            
            # For now, return base futures symbol
            # In production, you'd add expiry date logic
            return futures_base
        
        return base_symbol
    
    def get_enhanced_indian_news(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive Indian market news"""
        news_data = {
            'news': [],
            'market_sentiment': 'neutral',
            'key_events': [],
            'sector_news': []
        }
        
        try:
            # Determine market focus based on symbol
            if symbol in ['^NSEBANK', 'BANKNIFTY']:
                focus = 'banking'
            elif symbol in ['^NSEI', 'NIFTY']:
                focus = 'broad_market'
            else:
                focus = 'general'
            
            # Get news from multiple Indian sources
            for source_name, source_url in self.news_sources.items():
                try:
                    source_news = self._fetch_rss_news(source_url, focus)
                    news_data['news'].extend(source_news)
                except Exception as e:
                    logger.warning(f"Failed to fetch news from {source_name}: {e}")
            
            # Add market-specific events
            news_data['key_events'] = self._get_market_events(symbol)
            
            # Analyze sentiment
            news_data['market_sentiment'] = self._analyze_news_sentiment(news_data['news'])
            
            return news_data
            
        except Exception as e:
            logger.error(f"Failed to get Indian market news: {e}")
            return {
                'news': [],
                'error': str(e),
                'market_sentiment': 'neutral'
            }
    
    def _fetch_rss_news(self, rss_url: str, focus: str) -> List[Dict]:
        """Fetch news from RSS feed"""
        try:
            # For now, return sample news structure
            # In production, implement actual RSS parsing
            sample_news = [
                {
                    'title': f'Market Update: {focus.title()} Sector Analysis',
                    'summary': f'Latest developments in {focus} sector affecting market sentiment',
                    'source': rss_url.split('//')[1].split('/')[0],
                    'timestamp': datetime.now().isoformat(),
                    'sentiment': 'neutral',
                    'relevance': 'high'
                }
            ]
            return sample_news
        except Exception as e:
            logger.warning(f"RSS fetch failed for {rss_url}: {e}")
            return []
    
    def _get_market_events(self, symbol: str) -> List[Dict]:
        """Get upcoming market events and announcements"""
        events = []
        
        # Add common Indian market events
        today = datetime.now()
        
        # RBI policy dates (sample)
        events.append({
            'event': 'RBI Monetary Policy',
            'date': (today + timedelta(days=30)).strftime('%Y-%m-%d'),
            'impact': 'high',
            'description': 'Reserve Bank of India monetary policy announcement'
        })
        
        # Expiry dates for derivatives
        if symbol in ['^NSEBANK', 'BANKNIFTY']:
            events.append({
                'event': 'Bank Nifty Weekly Expiry',
                'date': self._get_next_thursday().strftime('%Y-%m-%d'),
                'impact': 'medium',
                'description': 'Bank Nifty options expiry'
            })
        
        return events
    
    def _get_next_thursday(self) -> datetime:
        """Get next Thursday (Bank Nifty expiry day)"""
        today = datetime.now()
        days_ahead = 3 - today.weekday()  # Thursday is 3
        if days_ahead <= 0:
            days_ahead += 7
        return today + timedelta(days=days_ahead)
    
    def _analyze_news_sentiment(self, news_list: List[Dict]) -> str:
        """Analyze overall sentiment from news"""
        if not news_list:
            return 'neutral'
        
        # Simple sentiment analysis based on keywords
        positive_keywords = ['surge', 'rally', 'gain', 'bullish', 'positive', 'growth']
        negative_keywords = ['fall', 'decline', 'bearish', 'negative', 'crash', 'drop']
        
        positive_count = 0
        negative_count = 0
        
        for news in news_list:
            title_lower = news.get('title', '').lower()
            summary_lower = news.get('summary', '').lower()
            text = title_lower + ' ' + summary_lower
            
            for keyword in positive_keywords:
                if keyword in text:
                    positive_count += 1
            
            for keyword in negative_keywords:
                if keyword in text:
                    negative_count += 1
        
        if positive_count > negative_count:
            return 'bullish'
        elif negative_count > positive_count:
            return 'bearish'
        else:
            return 'neutral'
    
    def get_options_chain_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get options chain analysis for Indian indices"""
        try:
            # Placeholder for options chain analysis
            # In production, integrate with NSE API or data provider
            
            analysis = {
                'symbol': symbol,
                'max_pain': 0,
                'put_call_ratio': 0,
                'option_interest': {
                    'calls': [],
                    'puts': []
                },
                'support_levels': [],
                'resistance_levels': [],
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            # Add sample data based on symbol
            if symbol in ['^NSEBANK', 'BANKNIFTY']:
                analysis.update({
                    'max_pain': 55000,
                    'put_call_ratio': 1.2,
                    'support_levels': [54500, 54000, 53500],
                    'resistance_levels': [55500, 56000, 56500]
                })
            elif symbol in ['^NSEI', 'NIFTY']:
                analysis.update({
                    'max_pain': 24000,
                    'put_call_ratio': 1.1,
                    'support_levels': [23800, 23600, 23400],
                    'resistance_levels': [24200, 24400, 24600]
                })
            
            return analysis
            
        except Exception as e:
            logger.error(f"Options chain analysis failed: {e}")
            return {
                'error': str(e),
                'symbol': symbol
            }

# Global instance
indian_market_analyzer = IndianMarketAnalyzer()
