#!/usr/bin/env python3
"""Quick test of workflow initialization"""

import sys
import os
sys.path.insert(0, os.path.join(os.getcwd(), 'Agent_Trading'))

import logging
logging.basicConfig(level=logging.INFO)

try:
    print("1. Testing imports...")
    from helpers.utils import load_google_api_key
    print("✅ Utils imported")
    
    from helpers.simplified_gemini_workflow import SimplifiedGeminiWorkflow
    print("✅ SimplifiedGeminiWorkflow imported")
    
    print("2. Loading API key...")
    api_key = load_google_api_key()
    if api_key:
        print("✅ API key loaded")
    else:
        print("❌ No API key found")
        exit(1)
    
    print("3. Initializing workflow...")
    workflow = SimplifiedGeminiWorkflow(api_key)
    print("✅ Workflow initialized")
    
    print("4. Checking available tools...")
    if workflow.available_tools:
        print(f"✅ Tools available: {list(workflow.available_tools.keys())}")
    else:
        print("❌ No tools available")
    
    print("🎉 Basic workflow initialization successful!")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
