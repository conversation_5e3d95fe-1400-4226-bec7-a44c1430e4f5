#!/usr/bin/env python3
"""
📊 RATE LIMIT DASHBOARD
Real-time display of Gemini API usage based on official Google documentation.
Shows RPM, TPM, RPD usage with visual progress bars and tier information.
"""

import streamlit as st
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from helpers.gemini_rate_limiter import get_rate_limiter

def create_usage_gauge(current: int, limit: int, title: str, color: str = "blue") -> go.Figure:
    """Create a gauge chart for usage visualization."""
    percentage = (current / limit) * 100 if limit > 0 else 0
    
    # Color based on usage level
    if percentage < 50:
        gauge_color = "green"
    elif percentage < 80:
        gauge_color = "orange"
    else:
        gauge_color = "red"
    
    fig = go.Figure(go.Indicator(
        mode = "gauge+number+delta",
        value = percentage,
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': title},
        delta = {'reference': 100},
        gauge = {
            'axis': {'range': [None, 100]},
            'bar': {'color': gauge_color},
            'steps': [
                {'range': [0, 50], 'color': "lightgray"},
                {'range': [50, 80], 'color': "yellow"},
                {'range': [80, 100], 'color': "lightcoral"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 90
            }
        }
    ))
    
    fig.update_layout(height=300, margin=dict(l=20, r=20, t=40, b=20))
    return fig

def display_rate_limit_dashboard():
    """Display comprehensive rate limit dashboard."""
    st.header("🚦 Gemini API Rate Limits Dashboard")
    
    # Get current tier from session state or default to free
    current_tier = st.session_state.get('gemini_tier', 'free')
    
    # Tier selection
    col1, col2 = st.columns([1, 3])
    with col1:
        tier_options = ["free", "tier1", "tier2", "tier3"]
        selected_tier = st.selectbox(
            "Select Your Tier:",
            tier_options,
            index=tier_options.index(current_tier),
            help="Your current Gemini API usage tier"
        )
        
        if selected_tier != current_tier:
            st.session_state['gemini_tier'] = selected_tier
            st.rerun()
    
    # Get rate limiter for selected tier
    rate_limiter = get_rate_limiter(selected_tier)
    
    # Get usage summary and tier info
    usage_summary = rate_limiter.get_usage_summary()
    tier_info = rate_limiter.get_tier_info()
    
    with col2:
        # Tier information
        st.info(f"""
        **{tier_info['current_tier']}**: {tier_info['description']}
        
        **Upgrade Requirements**: {tier_info['upgrade_requirements']}
        
        **Next Tier Benefits**: {tier_info['next_tier_benefits']}
        """)
    
    # Overall usage summary
    st.subheader("📊 Overall Usage Summary")
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric(
            "Total Requests Today",
            usage_summary['total_requests_today'],
            help="Total requests across all models today"
        )
    with col2:
        st.metric(
            "Active Models",
            len(usage_summary['models']),
            help="Number of models with recorded usage"
        )
    with col3:
        st.metric(
            "Current Tier",
            usage_summary['tier'],
            help="Your current API usage tier"
        )
    
    # Model-specific usage
    if usage_summary['models']:
        st.subheader("🤖 Model-Specific Usage")
        
        for model, stats in usage_summary['models'].items():
            with st.expander(f"📈 {model.upper()}", expanded=True):
                
                # Usage metrics
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric(
                        "Requests/Minute",
                        stats['requests_this_minute'],
                        help=f"Current: {stats['requests_this_minute']} | Limit: {stats['limits']['rpm']}"
                    )
                    
                    # RPM Progress bar
                    rpm_progress = stats['rpm_usage_percent'] / 100
                    st.progress(rpm_progress, text=f"RPM Usage: {stats['rpm_usage_percent']}%")
                
                with col2:
                    st.metric(
                        "Tokens/Minute",
                        stats['tokens_this_minute'],
                        help=f"Current: {stats['tokens_this_minute']} | Limit: {stats['limits']['tpm']}"
                    )
                    
                    # TPM Progress bar
                    tpm_progress = stats['tpm_usage_percent'] / 100
                    st.progress(tpm_progress, text=f"TPM Usage: {stats['tpm_usage_percent']}%")
                
                with col3:
                    st.metric(
                        "Requests/Day",
                        stats['requests_today'],
                        help=f"Current: {stats['requests_today']} | Limit: {stats['limits']['rpd']}"
                    )
                    
                    # RPD Progress bar
                    rpd_progress = stats['rpd_usage_percent'] / 100
                    st.progress(rpd_progress, text=f"RPD Usage: {stats['rpd_usage_percent']}%")
                
                # Rate limit details
                st.markdown("**Rate Limits:**")
                limits_df = pd.DataFrame([{
                    "Metric": "Requests per Minute (RPM)",
                    "Current": stats['requests_this_minute'].split('/')[0],
                    "Limit": stats['limits']['rpm'],
                    "Usage %": f"{stats['rpm_usage_percent']}%"
                }, {
                    "Metric": "Tokens per Minute (TPM)",
                    "Current": stats['tokens_this_minute'].split('/')[0],
                    "Limit": stats['limits']['tpm'],
                    "Usage %": f"{stats['tpm_usage_percent']}%"
                }, {
                    "Metric": "Requests per Day (RPD)",
                    "Current": stats['requests_today'].split('/')[0],
                    "Limit": stats['limits']['rpd'],
                    "Usage %": f"{stats['rpd_usage_percent']}%"
                }])
                
                st.dataframe(limits_df, use_container_width=True, hide_index=True)
    
    else:
        st.info("🔄 No API usage recorded yet. Usage will appear after making requests.")
    
    # Rate limit reference table
    st.subheader("📋 Official Rate Limits Reference")
    
    # Create comparison table for all tiers
    models = ["gemini-2.5-pro", "gemini-2.5-flash", "gemini-2.0-flash"]
    tiers = ["free", "tier1", "tier2", "tier3"]
    
    comparison_data = []
    for model in models:
        for tier in tiers:
            limiter = get_rate_limiter(tier)
            limits = limiter.get_rate_limits(model)
            if limits:
                comparison_data.append({
                    "Model": model,
                    "Tier": tier.upper(),
                    "RPM": limits.rpm,
                    "TPM": f"{limits.tpm:,}",
                    "RPD": limits.rpd if limits.rpd != 999999 else "No limit"
                })
    
    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        st.dataframe(comparison_df, use_container_width=True, hide_index=True)
    
    # Usage tips
    st.subheader("💡 Rate Limit Tips")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **🚀 Optimization Tips:**
        - Use Gemini 2.0 Flash for faster responses
        - Batch similar requests together
        - Implement exponential backoff for retries
        - Monitor token usage with compression
        - Use appropriate models for each task
        """)
    
    with col2:
        st.markdown("""
        **⚠️ Avoid Rate Limits:**
        - Check limits before making requests
        - Implement request queuing
        - Use caching for repeated queries
        - Spread requests across time
        - Monitor usage in real-time
        """)
    
    # Real-time monitoring
    st.subheader("🔄 Real-Time Monitoring")
    
    if st.button("🔄 Refresh Usage Stats", type="primary"):
        st.rerun()
    
    # Auto-refresh option
    auto_refresh = st.checkbox("🔄 Auto-refresh every 30 seconds")
    if auto_refresh:
        import time
        time.sleep(30)
        st.rerun()
    
    # Last updated timestamp
    st.caption(f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def display_rate_limit_widget():
    """Compact rate limit widget for main dashboard."""
    rate_limiter = get_rate_limiter(st.session_state.get('gemini_tier', 'free'))
    usage_summary = rate_limiter.get_usage_summary()
    
    st.markdown("### 🚦 API Usage")
    
    if usage_summary['models']:
        # Show most used model
        most_used_model = max(usage_summary['models'].items(), 
                            key=lambda x: int(x[1]['requests_today'].split('/')[0]))
        model_name, stats = most_used_model
        
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Requests Today", stats['requests_today'])
        with col2:
            st.metric("Current Tier", usage_summary['tier'])
        
        # Show usage bars
        rpm_pct = stats['rpm_usage_percent']
        tpm_pct = stats['tpm_usage_percent']
        
        st.progress(rpm_pct/100, text=f"RPM: {rpm_pct}%")
        st.progress(tpm_pct/100, text=f"TPM: {tpm_pct}%")
        
    else:
        st.info("No usage data yet")

if __name__ == "__main__":
    st.set_page_config(
        page_title="Gemini Rate Limits",
        page_icon="🚦",
        layout="wide"
    )
    display_rate_limit_dashboard()
