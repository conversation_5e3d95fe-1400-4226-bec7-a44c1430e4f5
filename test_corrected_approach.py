#!/usr/bin/env python3
"""Test the corrected approach using existing system"""

import sys
import os
sys.path.insert(0, os.path.join(os.getcwd(), 'Agent_Trading'))

import logging
logging.basicConfig(level=logging.INFO)

def test_corrected_approach():
    try:
        from helpers.langgraph_migration import generate_analysis_langgraph
        from PIL import Image
        import io
        
        # Create a test chart image
        img = Image.new('RGB', (800, 600), color='white')
        # Add some basic chart-like elements
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(img)
        
        # Draw chart elements
        draw.rectangle([100, 100, 700, 500], outline='black', width=2)
        draw.text((350, 50), "BTC/USDT - 1H", fill='black')
        draw.text((350, 520), "Test Chart", fill='black')
        
        # Convert to bytes
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        chart_data = img_bytes.getvalue()
        
        print("🧪 Testing corrected approach with existing proven system...")
        
        result = generate_analysis_langgraph(
            chart_images=[chart_data], 
            analysis_mode='scalp', 
            user_query='Analyze BTC scalping opportunity',
            market_specialization='Crypto'
        )
        
        print('✅ Test Results:')
        print('Success:', result.get('success'))
        if result.get('success'):
            print('System version:', result.get('system_version'))
            print('Detected symbol:', result.get('detected_symbol'))
            print('Analysis preview:', str(result.get('analysis', ''))[:200] + '...')
            print('Tool results count:', len(result.get('tool_results', [])))
        else:
            print('Error:', result.get('error'))
        
        return result
        
    except Exception as e:
        print(f'❌ Test failed: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_corrected_approach()
