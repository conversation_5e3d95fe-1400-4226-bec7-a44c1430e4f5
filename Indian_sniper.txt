//@version=5
indicator("VP Sniper System (Enhanced) – 1m (ST x10)", shorttitle="VP Sniper 1m x10", overlay=true)

// ========= SETTINGS =========
// Core
ma_htf          = input.timeframe("5", "MA / ADX Higher Timeframe", group="Core System")
st_atr          = input.int(12, "Supertrend ATR Period", group="Core System")            // per your choice
st_factor       = input.float(10.0, "Supertrend Factor", step=0.1, group="Core System")   // per your choice
require_st_dir  = input.bool(true, "Require Supertrend direction for entries", group="Core System")
fast_len        = input.int(9, "Fast MA Period", group="Core System")
slow_len        = input.int(21, "Slow MA Period", group="Core System")
price_source    = input.source(hlc3, "Price Source", group="Core System")

// Macro Regime Filter
long_ma_len     = input.int(50, "Long EMA (Regime)", group="Macro Regime Filter")
vlong_ma_len    = input.int(200, "Very Long EMA (Regime)", group="Macro Regime Filter")

// Noise Filters
enable_volatility_filter = input.bool(false, "Enable Volatility Filter", group="Noise Filters")
volatility_threshold     = input.float(2.0, "Max Volatility Ratio (ATR/avgATR)", step=0.1, group="Noise Filters")

enable_range_filter      = input.bool(true, "Enable Trend-Strength (ADX on HTF)", group="Noise Filters")
adx_threshold            = input.float(23.0, "ADX Threshold (HTF)", step=0.5, group="Noise Filters")

enable_momentum_filter   = input.bool(false, "Enable RSI Bias Filter", group="Noise Filters")
rsi_period               = input.int(14, "RSI Period", group="Noise Filters")

min_candle_gap           = input.int(8, "Min 1m Bars Between Signals", group="Noise Filters")

enable_strength_filter   = input.bool(true, "Enable Breakout Strength Filter (ATR)", group="Noise Filters")
min_breakout_atr         = input.float(0.20, "Min Breakout Strength (ATR multiples)", step=0.01, group="Noise Filters")

enable_consolidation_filter = input.bool(true, "Enable Consolidation Filter", group="Noise Filters")
use_bb_for_consolidation    = input.bool(true, "Use BB Bandwidth for Consolidation", group="Noise Filters")
bb_len                      = input.int(20, "BB Length", group="Noise Filters")
bb_mult                     = input.float(2.0, "BB Mult", step=0.1, group="Noise Filters")
bb_bw_threshold             = input.float(3.0, "BB Bandwidth % Threshold", step=0.1, group="Noise Filters")
consolidation_multiplier    = input.float(2.0, "Alt: Range×Candle Sensitivity", step=0.1, group="Noise Filters")

// Session / Execution
confirm_on_htf_close    = input.bool(true, "Confirm on HTF Bar Close (recommended)", group="Execution")
sess                    = input.session("0915-1525", "Active Session (IST)", group="Execution")
skip_open_mins          = input.int(10, "Skip first N minutes after open", group="Execution")

// State Management
unlock_bars             = input.int(60, "Unlock After N 1m Bars (multi-entry per trend)", group="State Management")

// ========= STATE =========
var int signal_lock = 0
var int last_signal_bar = 0

// ========= CORE CALCS =========
// Supertrend (direction: +1 up / -1 down)
[supertrend, dir] = ta.supertrend(st_factor, st_atr)
is_up   = dir == 1
is_down = dir == -1

// HTF MAs using the chosen higher timeframe
htf_fast_ma = request.security(syminfo.tickerid, ma_htf, ta.sma(price_source, fast_len), lookahead=barmerge.lookahead_off)
htf_slow_ma = request.security(syminfo.tickerid, ma_htf, ta.sma(price_source, slow_len), lookahead=barmerge.lookahead_off)

// Regime EMAs (chart TF)
long_ma  = ta.ema(close, long_ma_len)
vlong_ma = ta.ema(close, vlong_ma_len)
is_bullish_regime = long_ma > vlong_ma
is_bearish_regime = long_ma < vlong_ma

// HTF close pulse (for confirmation)
htf_time   = request.security(syminfo.tickerid, ma_htf, time, lookahead=barmerge.lookahead_off)
htf_closed = ta.change(htf_time)

// Session filter (IST): verify if current bar falls within session and has passed initial minutes
in_sess = not na(time(timeframe.period, sess))
new_session = ta.change(time(timeframe.period, sess))
bars_since_session = ta.barssince(new_session)
open_filter_ok = na(bars_since_session) ? true : bars_since_session >= skip_open_mins

// ========= FILTERS =========
// 1) Volatility (ATR ratio)
atr_current = ta.atr(14)
atr_avg     = ta.sma(atr_current, 20)
volatility_ratio = atr_current / atr_avg
volatility_ok = not enable_volatility_filter or (volatility_ratio < volatility_threshold)

// 2) Trend strength on HTF (ADX)
dm_period = 14

// Request high, low, close from HTF
[htf_high, htf_low, htf_close] = request.security(syminfo.tickerid, ma_htf, [high, low, close], lookahead=barmerge.lookahead_off)

// Calculate ADX on the requested HTF data using the built-in ta.adx function
adx_htf = ta.adx(dm_period, htf_high, htf_low, htf_close)
range_ok  = not enable_range_filter or (adx_htf >= adx_threshold)

// 3) Triggers vs MA ceiling/floor (+ small buffer)
ceiling = math.max(htf_fast_ma, htf_slow_ma)
floor   = math.min(htf_fast_ma, htf_slow_ma)
eps     = syminfo.mintick

buy_trigger  = ta.crossover(close, ceiling)
sell_trigger = ta.crossunder(close, floor)
// Ensure the price is sufficiently above/below the level after the cross
buy_trigger  := buy_trigger and (close > ceiling + eps)
sell_trigger := sell_trigger and (close < floor - eps)

// 4) RSI momentum (optional)
rsi = ta.rsi(close, rsi_period)
momentum_ok = not enable_momentum_filter or ( (buy_trigger  and (rsi >= 50)) or (sell_trigger and (rsi <= 50)) or (not buy_trigger and not sell_trigger) )

// 5) Gap between signals
time_gap_ok = (bar_index - last_signal_bar) >= min_candle_gap

// 6) Consolidation
bb_basis = ta.sma(close, bb_len)
bb_dev   = ta.stdev(close, bb_len)
bb_bw    = bb_basis != 0 ? (2.0 * bb_mult * bb_dev) / bb_basis * 100.0 : 0.0
is_consolidating_bb  = bb_bw < bb_bw_threshold

consolidation_period = 10
recent_high = ta.highest(high, consolidation_period)
recent_low  = ta.lowest(low, consolidation_period)
consolidation_range_pct = close != 0 ? (recent_high - recent_low) / close * 100 : 0
avg_candle_size_pct     = ta.sma(math.abs(close - open) / close * 100, 20)
is_consolidating_alt    = consolidation_range_pct < (avg_candle_size_pct * consolidation_multiplier)

is_consolidating = use_bb_for_consolidation ? is_consolidating_bb : is_consolidating_alt
consolidation_ok = not enable_consolidation_filter or (not is_consolidating)

// 7) Volume (bypass for indices / odd feeds)
is_index = str.lower(syminfo.type) == "index"
volume_ok = true
if not is_index
    vol_sma = ta.sma(volume, 20)
    volume_ok := (volume > 0) ? (volume > vol_sma * 0.8) : true

// 8) Breakout strength in ATR multiples (1m-friendly)
breakout_mult_buy  = (atr_current > 0) ? (close - ceiling) / atr_current : 0.0
breakout_mult_sell = (atr_current > 0) ? (floor - close)   / atr_current : 0.0

// Intermediate strength conditions
buy_strength_ok  = buy_trigger  and (breakout_mult_buy  >= min_breakout_atr)
sell_strength_ok = sell_trigger and (breakout_mult_sell >= min_breakout_atr)
no_trigger_ok    = not buy_trigger and not sell_trigger

strength_ok = not enable_strength_filter or buy_strength_ok or sell_strength_ok or no_trigger_ok

// Combined filters + session hygiene
all_filters_ok = volatility_ok and range_ok and momentum_ok and time_gap_ok and consolidation_ok and volume_ok and in_sess and open_filter_ok

// ========= SIGNALS & LOCKING =========
// Reset lock on Supertrend flip or time-based unlock for multiple entries
if ta.change(dir) or (bar_index - last_signal_bar >= unlock_bars)
    signal_lock := 0

st_gate_ok_buy  = not require_st_dir or is_up
st_gate_ok_sell = not require_st_dir or is_down

buy_signal  = is_bullish_regime and st_gate_ok_buy and buy_trigger and (signal_lock == 0) and all_filters_ok
sell_signal = is_bearish_regime and st_gate_ok_sell and sell_trigger and (signal_lock == 0) and all_filters_ok

confirmed_buy  = buy_signal  and (not confirm_on_htf_close or htf_closed)
confirmed_sell = sell_signal and (not confirm_on_htf_close or htf_closed)

// Engage Lock
if confirmed_buy
    signal_lock := 1
    last_signal_bar := bar_index
if confirmed_sell
    signal_lock := -1
    last_signal_bar := bar_index

// ========= PLOTTING =========
plot_st = plot(supertrend, "Supertrend", color=color.new(color.white, 100))
plot_price = plot(price_source, title="Price Source (cloud)", display=display.none)
cloud_color = is_up ? color.new(color.green, 85) : color.new(color.red, 85)
fill(plot_st, plot_price, color=cloud_color, title="Trend Cloud")

plot(htf_fast_ma, "HTF Fast MA", color=color.new(color.yellow, 0), linewidth=2)
plot(htf_slow_ma, "HTF Slow MA", color=color.new(color.fuchsia, 0), linewidth=2)
plot(long_ma,     "Regime EMA (Long)",      color=color.new(color.aqua, 25),   linewidth=2)
plot(vlong_ma,    "Regime EMA (Very Long)", color=color.new(color.orange, 25), linewidth=2)

plotshape(confirmed_buy,  title="Buy",  style=shape.triangleup,   location=location.belowbar, color=color.new(color.green, 0), size=size.large, text="BUY")
plotshape(confirmed_sell, title="Sell", style=shape.triangledown, location=location.abovebar, color=color.new(color.red, 0),   size=size.large, text="SELL")

bgcolor(not all_filters_ok ? color.new(color.gray, 95) : na, title="Filtered Zone")

// ========= ALERTS =========
alertcondition(confirmed_buy and barstate.isconfirmed, "VP Sniper Buy",  "Buy Signal Generated")
alertcondition(confirmed_sell and barstate.isconfirmed, "VP Sniper Sell", "Sell Signal Generated")

// ========= DEBUG TABLE =========
var table info_table = table.new(position.top_right, 2, 12, bgcolor=color.new(color.white, 85), border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "Filter", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 0, "Status", text_color=color.black, text_size=size.small)

    table.cell(info_table, 0, 1, "Regime", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 1, is_bullish_regime ? "BULL" : is_bearish_regime ? "BEAR" : "NEUTRAL", text_color=is_bullish_regime ? color.green : is_bearish_regime ? color.red : color.gray, text_size=size.small)

    table.cell(info_table, 0, 2, "Volatility", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 2, volatility_ok ? "OK" : "HIGH", text_color=volatility_ok ? color.green : color.red, text_size=size.small)

    table.cell(info_table, 0, 3, "ADX (HTF)", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 3, range_ok ? str.tostring(math.round(adx_htf, 1)) : "WEAK", text_color=range_ok ? color.green : color.red, text_size=size.small)

    table.cell(info_table, 0, 4, "RSI", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 4, momentum_ok ? str.tostring(math.round(rsi, 1)) : "CONTRA", text_color=momentum_ok ? color.green : color.red, text_size=size.small)

    table.cell(info_table, 0, 5, "Time Gap", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 5, time_gap_ok ? "OK" : "SOON", text_color=time_gap_ok ? color.green : color.red, text_size=size.small)

    table.cell(info_table, 0, 6, "Strength xATR", text_color=color.black, text_size=size.small)
    strength_val = buy_trigger ? breakout_mult_buy : sell_trigger ? breakout_mult_sell : na
    table.cell(info_table, 1, 6, strength_ok ? str.tostring(math.round(strength_val, 2)) : "WEAK", text_color=strength_ok ? color.green : color.red, text_size=size.small)

    table.cell(info_table, 0, 7, "Consolidation", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 7, consolidation_ok ? "CLEAR" : "TIGHT", text_color=consolidation_ok ? color.green : color.red, text_size=size.small)

    table.cell(info_table, 0, 8, "Volume", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 8, volume_ok ? "OK" : "LOW", text_color=volume_ok ? color.green : color.red, text_size=size.small)

    table.cell(info_table, 0, 9, "HTF Confirm", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 9, confirm_on_htf_close ? "ON" : "OFF", text_color=confirm_on_htf_close ? color.green : color.gray, text_size=size.small)
    table.cell(info_table, 0,10, "ST Gate", text_color=color.black, text_size=size.small)    table.cell(info_table, 1,10, require_st_dir ? "REQ" : "LOOSE", text_color=require_st_dir ? color.green : color.gray, text_size=size.small)
    table.cell(info_table, 0,11, "Overall", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1,11, all_filters_ok ? "READY" : "FILTERED", text_color=all_filters_ok ? color.green : color.red, text_size=size.small)

    table.cell(info_table, 0,11, "Overall", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1,11, all_filters_ok ? "READY" : "FILTERED", text_color=all_filters_ok ? color.green : color.red, text_size=size.small)