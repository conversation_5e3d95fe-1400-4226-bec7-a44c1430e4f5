#!/usr/bin/env python3
"""Test prompt loading and workflow initialization."""

from helpers.complete_langgraph_workflow import CompleteLangGraphTradingWorkflow
from helpers.utils import load_google_api_key
import logging

logging.basicConfig(level=logging.INFO)

def test_prompt_loading():
    """Test if prompts are loading correctly."""
    print("=== PROMPT LOADING TEST ===")
    
    try:
        # Test the workflow initialization
        api_key = load_google_api_key()
        workflow = CompleteLangGraphTradingWorkflow(api_key)
        
        # Check if prompts loaded correctly
        print(f"Prompts loaded: {list(workflow.prompts.keys())}")
        
        for prompt_name, prompt_content in workflow.prompts.items():
            if prompt_name in ['positional', 'scalp', 'crypto', 'indian_market']:
                print(f"{prompt_name} prompt length: {len(prompt_content)} chars")
                if len(prompt_content) > 8000:
                    print(f"  ⚠️  {prompt_name} prompt is too large (>{8000} chars) - will be replaced with fallback!")
                else:
                    print(f"  ✅ {prompt_name} prompt size is acceptable")
                    
        # Test with a real chart from memory folder
        print("\n=== TESTING WITH REAL CHART ===")

        import os

        # Use a real chart from memory/images folder
        chart_path = "memory/images/0ca45d52c38eda9f9249d88c3084414c.png"

        if os.path.exists(chart_path):
            with open(chart_path, 'rb') as f:
                chart_bytes = f.read()
            print(f"✅ Loaded real chart: {chart_path} ({len(chart_bytes)} bytes)")
        else:
            print(f"❌ Chart not found: {chart_path}")
            return
        
        # Test analysis
        result = workflow.analyze_chart(
            chart_images=[chart_bytes],
            analysis_mode="positional",
            user_query="Test analysis",
            market_specialization="Crypto"
        )
        
        print(f"Analysis result keys: {list(result.keys())}")
        print(f"Success: {result.get('success')}")
        print(f"Error: {result.get('error')}")
        print(f"Analysis length: {len(str(result.get('analysis', '')))}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_prompt_loading()
