import sys
import os

# Add the project root to the Python path to enable absolute imports
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import warnings
import json
import time
import io
import streamlit as st
from PIL import Image
from datetime import datetime

from Agent_Trading.helpers.clean_prompts import (
    create_main_prompt, vision_prompt, flash_summarization_prompt, feedback_prompt_template
)
from Agent_Trading.helpers.indian_market_tools import indian_market_analyzer
from Agent_Trading.helpers.utils import (
    load_google_api_key,
    check_ollama_status,
    generate_analysis_local,
    get_memory_system
)
from Agent_Trading.helpers.quota_manager import QuotaManager

# Import performance optimization systems
try:
    from Agent_Trading.GUI.performance_dashboard import (
        display_performance_metrics, display_token_usage_breakdown,
        display_cache_performance, display_quality_metrics,
        display_cost_optimization_insights, create_performance_sidebar
    )
    from Agent_Trading.helpers.performance_optimizer import get_performance_optimizer
    from Agent_Trading.helpers.quality_assurance import get_qa_system
    PERFORMANCE_AVAILABLE = True
except ImportError as e:
    print(f"Performance optimization not available: {e}")
    PERFORMANCE_AVAILABLE = False

# Load custom CSS
def load_css():
    """Load custom CSS for professional styling"""
    css_path = os.path.join(script_dir, "styles.css")
    if os.path.exists(css_path):
        with open(css_path, "r") as f:
            st.markdown(f"<style>{f.read()}</style>", unsafe_allow_html=True)
    else:
        # Fallback inline CSS if file doesn't exist
        st.markdown("""
        <style>
        .stApp {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f9fafb !important;
            color: #111827 !important;
        }
        .main-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            margin: 1rem;
            padding: 2rem;
            border: 1px solid #e5e7eb;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            margin-bottom: 2rem;
            padding: 2rem;
            text-align: center;
        }
        .stButton > button {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .stButton > button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }
        /* Ensure text visibility */
        .stApp, .stApp *, [data-testid="stSidebar"], [data-testid="stAppViewContainer"] {
            color: #111827 !important;
        }
        [data-testid="stSidebar"] {
            background-color: white !important;
        }
        [data-testid="stAppViewContainer"] {
            background-color: #f9fafb !important;
        }
        </style>
        """, unsafe_allow_html=True)

def get_tool_reference_sources(tool_name: str, args: dict, tool_result = None) -> dict:
    """Get clickable reference sources for each tool, using real URLs from DuckDuckGo results when available."""
    sources = {}

    # Handle case where tool_result might be a string instead of dict
    if isinstance(tool_result, str):
        try:
            import json
            tool_result = json.loads(tool_result)
        except (json.JSONDecodeError, TypeError):
            # If it's not valid JSON, return empty sources
            return sources

    # Ensure tool_result is a dict before proceeding
    if not isinstance(tool_result, dict):
        return sources

    # Extract real URLs from DuckDuckGo news results
    if tool_result and 'news' in tool_name.lower():
        if tool_result.get('success') and tool_result.get('news_articles'):
            for i, article in enumerate(tool_result['news_articles'][:5]):  # Top 5 articles
                if article.get('url'):
                    source_name = article.get('source', f"News Source {i+1}")
                    sources[source_name] = article['url']

        elif tool_result.get('success') and tool_result.get('fii_dii_news'):
            for i, article in enumerate(tool_result['fii_dii_news'][:5]):  # Top 5 articles
                if article.get('url'):
                    source_name = article.get('source', f"FII/DII Source {i+1}")
                    sources[source_name] = article['url']

        elif tool_result.get('success') and tool_result.get('comprehensive_news'):
            for i, article in enumerate(tool_result['comprehensive_news'][:5]):  # Top 5 articles
                if article.get('url'):
                    source_name = article.get('source', f"Market Source {i+1}")
                    sources[source_name] = article['url']

    # Fallback to default sources if no real URLs available
    if not sources:
        # News tools fallback
        if 'news' in tool_name.lower():
            sources.update({
                "Economic Times": "https://economictimes.indiatimes.com/markets",
                "MoneyControl": "https://www.moneycontrol.com/news/business/markets/",
                "Reuters Markets": "https://www.reuters.com/markets/",
                "Bloomberg": "https://www.bloomberg.com/markets"
            })

        # Market data tools
        elif 'market' in tool_name.lower() or 'smart' in tool_name.lower():
            symbol = args.get('symbol', '').upper()
            if 'BTC' in symbol or 'ETH' in symbol or 'SOL' in symbol:
                sources.update({
                    "Delta Exchange": "https://www.delta.exchange/",
                    "CoinGecko": f"https://www.coingecko.com/en/coins/{symbol.replace('USDT', '').lower()}",
                    "CoinMarketCap": "https://coinmarketcap.com/"
                })
            elif 'NSEI' in symbol or 'NSEBANK' in symbol or 'nifty' in symbol.lower():
                sources.update({
                    "Dhan API": "https://dhan.co/",
                    "NSE India": "https://www.nseindia.com/",
                    "Yahoo Finance": f"https://finance.yahoo.com/quote/{symbol}",
                    "MoneyControl": f"https://www.moneycontrol.com/india/stockpricequote/{symbol}"
                })
            else:
                sources.update({
                    "Yahoo Finance": f"https://finance.yahoo.com/quote/{symbol}" if symbol else "https://finance.yahoo.com/",
                    "Google Finance": f"https://www.google.com/finance/quote/{symbol}" if symbol else "https://www.google.com/finance/"
                })

        # Other tool types
        elif 'fii' in tool_name.lower() or 'dii' in tool_name.lower():
            sources.update({
                "NSE India": "https://www.nseindia.com/market-data/securities-available-for-trading",
                "SEBI": "https://www.sebi.gov.in/",
                "MoneyControl FII/DII": "https://www.moneycontrol.com/stocks/marketstats/fii_dii_activity/"
            })

        elif 'detect' in tool_name.lower() or 'symbol' in tool_name.lower():
            sources.update({
                "Symbol Detection": "AI-powered image analysis",
                "Market Mapping": "Internal symbol database"
            })

    return sources

def display_beautiful_tool_results(tool_usage):
    """Display tool results with real URLs and simplified formatting."""
    st.markdown("### 🛠️ Tools Used & Results")

    # Handle both old format (tool_usage list) and new format (tool_results dict)
    if isinstance(tool_usage, dict):
        # New optimized format from parallel execution
        for tool_name, tool_result in tool_usage.items():
            display_single_tool_result(tool_name, tool_result)
    elif isinstance(tool_usage, list):
        # Old format compatibility
        for tool_call in tool_usage:
            tool_name = tool_call.get('tool_name', tool_call.get('name', 'Unknown Tool'))
            display_single_tool_result(tool_name, tool_call)

def display_single_tool_result(tool_name, tool_result):
    """Display a single tool result with real URLs."""
    # Get appropriate icon
    if 'news' in tool_name.lower():
        icon = "📰"
    elif 'market' in tool_name.lower() or 'data' in tool_name.lower():
        icon = "📊"
    elif 'detect' in tool_name.lower() or 'symbol' in tool_name.lower():
        icon = "🎯"
    elif 'fii' in tool_name.lower() or 'dii' in tool_name.lower():
        icon = "�"
    else:
        icon = "🔧"

    with st.expander(f"{icon} {tool_name.replace('_', ' ').title()}", expanded=True):
        # Show execution status and time
        if hasattr(tool_result, 'success'):
            status = "✅ Success" if tool_result.success else "❌ Failed"
            exec_time = f"({tool_result.execution_time:.2f}s)" if hasattr(tool_result, 'execution_time') else ""
            st.markdown(f"**Status:** {status} {exec_time}")

            if not tool_result.success and hasattr(tool_result, 'error'):
                st.error(f"Error: {tool_result.error}")
                return

            # Display the actual data
            if hasattr(tool_result, 'data') and tool_result.data:
                display_tool_data(tool_name, tool_result.data)
        else:
            # Old format compatibility
            result = tool_result.get('result_summary', 'No result')
            st.write(result)

        # Display input parameters if available
        if isinstance(tool_result, dict):
            args = tool_result.get('arguments', {})
            if args:
                st.markdown("**📥 Input Parameters:**")
                for key, value in args.items():
                    st.write(f"**{key}**: {str(value)[:100]}")

            # Show quality metrics if available
            quality_score = tool_result.get('quality_score', 0)
            if quality_score > 0:
                col1, col2 = st.columns([1, 3])
                with col1:
                    quality_color = "🟢" if quality_score >= 0.8 else "🟡" if quality_score >= 0.6 else "🔴"
                    st.metric("Quality", f"{quality_color} {quality_score:.0%}")
                with col2:
                    quality_issues = tool_result.get('quality_issues', [])
                    if quality_issues:
                        st.caption(f"⚠️ {len(quality_issues)} issue(s) detected")
                        with st.expander("View quality issues"):
                            for issue in quality_issues:
                                st.warning(f"• {issue}")
                    else:
                        st.caption("✅ High quality data")

        # Display results with enhanced formatting
        if isinstance(tool_result, dict):
            result = tool_result.get('result_summary', tool_result.get('result', 'No result'))
        else:
            result = str(tool_result)

        st.markdown("**📤 Results:**")

        # Handle empty or minimal results
        if not result or result == 'No result' or (isinstance(result, str) and len(result.strip()) < 10):
            st.warning("⚠️ Tool returned empty or minimal data")
            st.write(f"Raw result: `{result}`")
            return

        # Try to parse and display structured data
        try:
            # Handle different result formats
            parsed_result = None
            if isinstance(result, dict):
                parsed_result = result
            elif isinstance(result, str):
                # Try to parse JSON if it looks like JSON
                if result.strip().startswith('{') or result.strip().startswith('['):
                    try:
                        parsed_result = json.loads(result)
                    except:
                        # If JSON parsing fails, treat as plain text
                        parsed_result = None

            if parsed_result:

                # Handle news results specially
                if 'news' in tool_name.lower() and isinstance(parsed_result, dict):
                    if 'news' in parsed_result:
                        st.markdown("**📰 Latest Headlines:**")
                        for idx, news_item in enumerate(parsed_result['news'][:5], 1):
                            title = news_item.get('title', 'No title')
                            publisher = news_item.get('publisher', 'Unknown')
                            link = news_item.get('link', '')
                            summary = news_item.get('summary', '')

                            if link:
                                st.markdown(f"""
                                **{idx}. [{title}]({link})**
                                *Source: {publisher}*
                                {summary[:150]}{'...' if len(summary) > 150 else ''}
                                """)
                            else:
                                st.markdown(f"""
                                **{idx}. {title}**
                                *Source: {publisher}*
                                {summary[:150]}{'...' if len(summary) > 150 else ''}
                                """)

                # Handle market data results
                elif 'market' in tool_name.lower() and isinstance(parsed_result, dict):
                    if 'price_summary' in parsed_result:
                        price_data = parsed_result['price_summary']
                        st.markdown("**💰 Price Information:**")

                        cols = st.columns(3)
                        with cols[0]:
                            if price_data.get('current_price'):
                                st.metric("Current Price", f"${price_data['current_price']:,.2f}")
                        with cols[1]:
                            if price_data.get('price_change_24h') is not None:
                                change = price_data['price_change_24h']
                                st.metric("24h Change", f"{change:+.2f}%", delta=f"{change:+.2f}%")
                        with cols[2]:
                            if price_data.get('avg_volume'):
                                st.metric("Avg Volume", f"{price_data['avg_volume']:,.0f}")

                # Handle technical analysis results
                elif 'technical' in tool_name.lower() and isinstance(parsed_result, dict):
                    if 'trading_signals' in parsed_result:
                        signals = parsed_result['trading_signals']
                        st.markdown("**🎯 Trading Signals:**")

                        signal_color = "#28a745" if signals.get('overall_signal') == 'BUY' else "#dc3545" if signals.get('overall_signal') == 'SELL' else "#ffc107"
                        st.markdown(f"""
                        <div style="padding: 10px; border-radius: 5px; background-color: {signal_color}20; border-left: 4px solid {signal_color};">
                            <strong>Signal: {signals.get('overall_signal', 'NEUTRAL')}</strong><br>
                            Confidence: {signals.get('confidence', 0)}%<br>
                            Recommendation: {signals.get('recommendation', 'No recommendation')}
                        </div>
                        """, unsafe_allow_html=True)

                        if signals.get('signals'):
                            st.markdown("**Key Indicators:**")
                            for signal in signals['signals']:
                                st.write(f"• {signal}")

                else:
                    # Generic structured data display
                    st.json(parsed_result)

            else:
                # Plain text result
                if len(result) > 500:
                    st.write(result[:500] + "...")
                    with st.expander("View full result"):
                        st.text(result)
                else:
                    st.write(result)

        except Exception:
            # Fallback to simple display
            if len(result) > 500:
                st.write(result[:500] + "...")
                with st.expander("View full result"):
                    st.text(result)
            else:
                st.write(result)

        # Reference sources removed to fix display issues

        # Execution info
        if isinstance(tool_result, dict):
            timestamp = tool_result.get('timestamp', 'Unknown')
            execution_time = tool_result.get('execution_time', 'Unknown')
            st.caption(f"⏱️ Executed at: {timestamp} | Duration: {execution_time}")

def display_tool_data(tool_name, data):
    """Display tool data with appropriate formatting."""
    if not data:
        st.warning("No data available")
        return

    # Handle news data
    if 'news' in tool_name.lower() and isinstance(data, dict):
        articles = data.get('articles', [])
        if articles:
            st.markdown(f"**📰 Found {len(articles)} news articles:**")
            for i, article in enumerate(articles[:5], 1):  # Show top 5
                title = article.get('title', 'No title')
                url = article.get('url', '#')
                source = article.get('source', 'Unknown')
                date = article.get('date', 'Unknown date')

                st.markdown(f"**{i}. [{title}]({url})**")
                st.caption(f"Source: {source} | Date: {date}")

                if article.get('body'):
                    with st.expander("Read more"):
                        st.write(article['body'][:500] + "..." if len(article['body']) > 500 else article['body'])
        else:
            st.info("No news articles found")

    # Handle market data
    elif 'market' in tool_name.lower() or 'data' in tool_name.lower():
        if isinstance(data, dict):
            st.json(data)
        else:
            st.write(data)

    # Handle FII/DII data
    elif 'fii' in tool_name.lower() or 'dii' in tool_name.lower():
        if isinstance(data, dict):
            flows = data.get('flows', {})
            if flows:
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("FII Flow", flows.get('fii', 'N/A'))
                with col2:
                    st.metric("DII Flow", flows.get('dii', 'N/A'))
            else:
                st.json(data)
        else:
            st.write(data)

    # Default handling
    else:
        if isinstance(data, (dict, list)):
            st.json(data)
        else:
            st.write(data)

warnings.filterwarnings(
    "ignore",
    message=(
        "You are using the default legacy behaviour of the <class 'transformers.models.llama.tokenization_llama_fast.LlamaTokenizerFast'>"
    ),
)

# Ollama import removed - not needed for type hints


# --- Configuration ---
st.set_page_config(
    page_title="AI Trading Analysis Dashboard",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded",
)

# Load custom CSS
load_css()

# Professional Header
st.markdown("""
<div class="main-header">
    <h1 style="color: white; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">🚀 AI Trading Analysis Dashboard</h1>
    <p style="color: rgba(255,255,255,0.9); font-size: 1.1rem; font-weight: 400;">Advanced Chart Analysis with LangGraph Workflow & Multi-Tool Integration</p>
</div>
""", unsafe_allow_html=True)

st.markdown(
    """
    <style>
    .main {background-color: #f7f9fa;}
    .block-container {padding-top: 2rem; max-width: 900px !important;}
    .stButton>button {background-color: #1a73e8; color: white; font-weight: bold; border-radius: 8px;}
    .stButton>button:hover {background-color: #155ab6;}
    .stMarkdown, .stTable {font-size: 1.1rem; text-align: left !important;}
    .stImage {border-radius: 10px; box-shadow: 0 2px 8px #aaa;}
    h1, h2, h3, h4, h5, h6 {text-align: left !important;}
    .css-18e3th9 {padding-left: 2rem !important;}
    </style>
    """,
    unsafe_allow_html=True,
)

# --- App Title and Description ---
st.markdown(
    """
<h1 style='color:#1a73e8; font-weight:700; margin-bottom:0.2em; text-align:left;'>TradingAnalysis AI <span style='font-size:1.2em;'>💹</span></h1>
<div style='font-size:1.15em; color:#333; margin-bottom:1em; text-align:left;'>
<b>Your AI-powered trading assistant.</b> Upload a screenshot of any trading chart and get a professional, actionable technical analysis powered by Gemini or a local model via Ollama.<br>
<span style='color:#1a73e8;'>Get clear entry/exit signals, risk management, and more.</span>

# Display API quota status
quota_status = quota_manager.get_quota_status()
if quota_status['remaining'] <= 5:
    st.warning(f"⚠️ API Quota: {quota_status['remaining']}/{quota_status['daily_quota']} requests remaining. Fallback summarization will be used when quota is exceeded.")
elif quota_status['remaining'] <= 15:
    st.info(f"📊 API Quota: {quota_status['remaining']}/{quota_status['daily_quota']} requests remaining.")
else:
    st.success(f"✅ API Quota: {quota_status['remaining']}/{quota_status['daily_quota']} requests available.")
</div>
""",
    unsafe_allow_html=True,
)


# --- Load Config and Check Models ---
google_api_key = load_google_api_key()
if not google_api_key:
    st.warning(
        "Your Gemini API key is missing in config.json. Please add a 'google_api_key' entry with your valid key."
    )
    st.stop()

ollama_running, ollama_models = check_ollama_status()


# --- Sidebar for Model Selection and Instructions ---
with st.sidebar:
    # 🤖 Model Selection
    st.markdown("<h3 style='color:#1a73e8;'>🤖 Model Selection</h3>", unsafe_allow_html=True)

    model_options = {
        "Gemini 2.5 Pro": "gemini-2.5-pro",
        "Gemini 2.5 Flash": "gemini-2.5-flash",
        "Gemini 2.0 Flash": "gemini-2.0-flash"
    }

    selected_model_display = st.selectbox(
        "Choose Analysis Model:",
        options=list(model_options.keys()),
        index=0,  # Default to 2.5 Pro
        help="Select the AI model for trading analysis. 2.5 Pro is recommended for detailed analysis."
    )

    # Store selected model in session state
    st.session_state['selected_model'] = model_options[selected_model_display]

    # Rate Limit Tier Selection
    tier_options = {
        "Free Tier": "free",
        "Tier 1 (Paid)": "tier1",
        "Tier 2 (High Usage)": "tier2",
        "Tier 3 (Enterprise)": "tier3"
    }

    selected_tier_display = st.selectbox(
        "API Tier:",
        options=list(tier_options.keys()),
        index=0,  # Default to free
        help="Select your Google API tier for accurate rate limiting."
    )

    # Store selected tier in session state
    st.session_state['rate_limit_tier'] = tier_options[selected_tier_display]

    st.markdown("---")

    # 🚦 Rate Limit Dashboard Widget
    try:
        from Agent_Trading.GUI.rate_limit_dashboard import display_rate_limit_widget
        display_rate_limit_widget()
        st.markdown("---")
    except Exception as e:
        st.warning(f"Rate limit widget unavailable: {e}")

    # AI Quota Status Display
    st.markdown("<h3 style='color:#1a73e8;'>🤖 AI Quota Status</h3>", unsafe_allow_html=True)
    try:
        quota_manager = QuotaManager(quota_file="GUI/quota_usage.json")
        quota_status = quota_manager.get_quota_status()

        # Create quota progress bar
        used = quota_status['used_requests']
        total = quota_status['daily_quota']
        remaining = quota_status['remaining']

        # Color coding based on usage
        if remaining > total * 0.5:
            color = "#10b981"  # Green
        elif remaining > total * 0.2:
            color = "#f59e0b"  # Yellow
        else:
            color = "#ef4444"  # Red

        # Display quota info
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Used", f"{used}/{total}", help="API requests used today")
        with col2:
            st.metric("Remaining", remaining, help="API requests remaining today")

        # Progress bar
        progress = used / total if total > 0 else 0
        st.progress(progress)

        # Status indicator
        status_text = "✅ Available" if quota_status['can_make_request'] else "⚠️ Quota Exceeded"
        st.markdown(f"**Status:** {status_text}")

        # Reset info
        st.caption(f"Resets daily at midnight (Last reset: {quota_status['last_reset']})")

    except Exception as e:
        st.error(f"Could not load quota status: {str(e)}")

    st.divider()

    st.markdown("<h3 style='color:#1a73e8;'>Model Selection</h3>", unsafe_allow_html=True)

    available_sources = []
    if google_api_key:
        available_sources.append("Cloud")
    if ollama_running:
        available_sources.append("Local")

    if not available_sources:
        st.error("No models available. Please check your Gemini API key or ensure Ollama is running.")
        st.stop()

    model_source = st.radio(
        "Select Model Source:",
        available_sources,
        captions=["Uses Google Gemini API", "Uses your local Ollama server"],
    )

    gemini_model_id = "gemini-2.5-pro"
    ollama_model_name = ""

    if model_source == "Cloud":
        gemini_available_models = ["gemini-2.5-pro", "gemini-2.5-flash", "gemini-2.0-flash-exp"]
        gemini_model_id = st.selectbox(
            "Select Gemini Model:",
            gemini_available_models,
            index=0,  # Default to 2.5 Pro (best model)
            key="gemini_model_selector",
            help="🥇 2.5 Pro: Best analysis quality | ⚡ 2.5 Flash: Fast & good | 🔄 2.0 Flash: Fallback option"
        )

        # Model descriptions
        model_descriptions = {
            "gemini-2.5-pro": "🥇 **Premium Model** - Best analysis quality, advanced reasoning",
            "gemini-2.5-flash": "⚡ **Fast Model** - Quick analysis with good quality",
            "gemini-2.0-flash-exp": "🔄 **Fallback Model** - Reliable backup option"
        }

        st.info(model_descriptions.get(gemini_model_id, f"Using **{gemini_model_id}** for analysis."))
        st.caption("💡 Tool summarization uses 2.5 Flash → 2.0 Flash fallback automatically")

        # Show optimization info
        st.success("🚀 **Optimized Analysis Enabled** - 2-3x faster with parallel tool execution!")
    elif model_source == "Local":
        st.info("Using a local model via **Ollama**.")
        ollama_model_name = st.text_input(
            "Ollama Model Name:",
            value="Janus-Pro-7B",
            help="Enter the name of your local Ollama model. Popular options: Janus-Pro-7B, llava, bakllava"
        )
        if not ollama_models:
            st.warning("Could not detect any local Ollama models.")
        elif ollama_model_name not in ollama_models:
            st.warning(
                f"Model '{ollama_model_name}' not found. Please ensure it's pulled via `ollama pull {ollama_model_name}`."
            )

    st.markdown("<h3 style='color:#1a73e8;'>Analysis Type</h3>", unsafe_allow_html=True)
    analysis_type_choice = st.radio(
        "Choose Analysis Type:",
        ["Positional", "Scalp"],
        captions=[
            "For 15-min, 1-hour, or 4-hour charts (Swing/Positional)",
            "For 1-min or 3-min charts (High-Frequency Scalping)",
        ],
        help="📊 **Positional**: Long-term analysis for swing trading (days to weeks) with larger stop losses. **Scalp**: Short-term analysis for quick trades (minutes to hours) with tight stop losses."
    )

    # Timeframe selection based on analysis type
    st.markdown("<h4 style='color:#1a73e8;'>⏰ Select Timeframes for Analysis</h4>", unsafe_allow_html=True)

    if analysis_type_choice == "Positional":
        st.markdown("**📈 Positional Trading Timeframes:**")
        col1, col2, col3 = st.columns(3)

        with col1:
            tf_15m = st.checkbox("15 Minutes", value=True, key="pos_15m", help="Short-term entry timing")
        with col2:
            tf_1h = st.checkbox("1 Hour", value=True, key="pos_1h", help="Medium-term trend analysis")
        with col3:
            tf_1d = st.checkbox("1 Day", value=True, key="pos_1d", help="Long-term trend confirmation")

        selected_timeframes = []
        if tf_15m: selected_timeframes.append("15m")
        if tf_1h: selected_timeframes.append("1h")
        if tf_1d: selected_timeframes.append("1d")

    else:  # Scalp
        st.markdown("**⚡ Scalping Timeframes:**")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            tf_1m = st.checkbox("1 Minute", value=True, key="scalp_1m", help="Ultra-short entry timing")
        with col2:
            tf_3m = st.checkbox("3 Minutes", value=True, key="scalp_3m", help="Quick momentum analysis")
        with col3:
            tf_5m = st.checkbox("5 Minutes", value=True, key="scalp_5m", help="Short-term trend")
        with col4:
            tf_15m_scalp = st.checkbox("15 Minutes", value=False, key="scalp_15m", help="Context confirmation")

        selected_timeframes = []
        if tf_1m: selected_timeframes.append("1m")
        if tf_3m: selected_timeframes.append("3m")
        if tf_5m: selected_timeframes.append("5m")
        if tf_15m_scalp: selected_timeframes.append("15m")

    if not selected_timeframes:
        st.warning("⚠️ Please select at least one timeframe for analysis!")

    # Display selected timeframes
    if selected_timeframes:
        st.info(f"🎯 **Selected Timeframes**: {', '.join(selected_timeframes)} | **Analysis Type**: {analysis_type_choice}")

        # Store in session state for use in analysis
        st.session_state["selected_timeframes"] = selected_timeframes
        st.session_state["analysis_type"] = analysis_type_choice

    # Market Specialization Selection
    st.markdown("<h3 style='color:#1a73e8;'>Market Specialization</h3>", unsafe_allow_html=True)
    market_specialization = st.radio(
        "Select Market Focus:",
        ["Indian Market", "Crypto", "Others"],
        captions=[
            "Nifty 50, Bank Nifty with FII/DII flows & Dhan API",
            "BTC, ETH, SOL with Delta Exchange data & funding rates",
            "General market analysis with standard tools"
        ],
        help="🌍 Choose the market type to get specialized analysis tools and data sources. Each option uses different APIs and analysis techniques optimized for that market."
    )

    st.markdown("<h3 style='color:#1a73e8;'>Market Data</h3>", unsafe_allow_html=True)
    st.info("🤖 The AI will intelligently decide if additional market data is needed based on your chart analysis. No manual data selection required!")
    
    # Optional: Advanced users can still provide hints
    with st.expander("Advanced: Provide hints to AI (Optional)"):
        st.markdown("If you want to guide the AI, you can provide hints:")
        ticker_hint = st.text_input(
            "Ticker Symbol Hint (if not clear from chart):",
            placeholder="e.g., AAPL, BTC-USD, Bank Nifty, Nifty 50",
            help="💡 Optional: Provide the ticker symbol if it's not clearly visible in your chart. This helps the AI fetch relevant market data and news."
        )
        context_hint = st.text_input(
            "Analysis Context Hint:",
            placeholder="e.g., 'Focus on crypto correlation' or 'Consider sector rotation'",
            help="💡 Optional: Give the AI additional context about what you want to focus on in the analysis. This helps tailor the insights to your specific needs."
        )

        # Auto-detect symbol from hint
        if ticker_hint:
            detected_info = indian_market_analyzer.detect_symbol_from_text(ticker_hint)
            futures_symbol = indian_market_analyzer.get_futures_symbol(detected_info)
            if detected_info != futures_symbol:
                st.info(f"🎯 **Symbol Detection**: '{ticker_hint}' → Using '{detected_info}' (Futures: '{futures_symbol}') for analysis")
    
    st.image("https://img.icons8.com/fluency/96/000000/line-chart.png", width=80)
    st.markdown("<h3 style='color:#1a73e8;'>How to Use</h3>", unsafe_allow_html=True)
    st.markdown(
        """
        <ol style='font-size:1.05em;'>
        <li><b>For Cloud:</b> Ensure <code>config.json</code> is present with your Gemini API key.</li>
        <li><b>For Local:</b> Make sure the Ollama application is running and you have pulled a multimodal model (e.g., <code>ollama pull Janus-Pro-7B</code>).</li>
        <li><b>Upload a Chart Image:</b> Click <b>Browse files</b>.</li>
        <li><b>Get Analysis:</b> Click <b>Analyze Chart</b>.</li>
        </ol>
        """,
        unsafe_allow_html=True,
    )
    st.info("Your API key is loaded from <code>config.json</code> and is not stored or shared.", icon="🔒")


# Initialize session state for feedback text area value and analysis history
if "feedback_text_value" not in st.session_state:
    st.session_state.feedback_text_value = ""

# Initialize analysis history
if "analysis_history" not in st.session_state:
    st.session_state.analysis_history = []

if "selected_analysis_id" not in st.session_state:
    st.session_state.selected_analysis_id = None


# --- Sidebar: Simplified Controls ---
with st.sidebar:
    st.markdown("### � Industry Standard Analysis")

    # Advanced settings
    with st.expander("🔧 Advanced Settings"):
        st.markdown("**Thinking Budget Settings:**")
        thinking_mode = st.selectbox(
            "Thinking Mode",
            ["Dynamic (Recommended)", "Conservative", "Aggressive", "Disabled"],
            help="Controls how much the AI 'thinks' before responding"
        )

        if thinking_mode == "Conservative":
            st.info("💡 Uses less thinking tokens, faster responses")
        elif thinking_mode == "Aggressive":
            st.info("🧠 Uses more thinking tokens, deeper analysis")
        elif thinking_mode == "Disabled":
            st.warning("⚠️ No thinking process, basic responses only")

        # Cache settings
        st.markdown("**Cache Settings:**")
        cache_enabled = st.checkbox("Enable Smart Caching", value=True)
        if cache_enabled:
            st.success("✅ Caching enabled - saves API calls and costs")
        else:
            st.warning("⚠️ Caching disabled - higher API usage")

# --- Sidebar: Recent Analysis History ---
with st.sidebar:
    st.markdown("### 📊 Recent Analysis History")

    if st.session_state.analysis_history:
        st.markdown("*Click to reload analysis for feedback:*")

        for i, analysis in enumerate(reversed(st.session_state.analysis_history[-10:])):  # Show last 10
            analysis_time = analysis.get('timestamp', 'Unknown')
            analysis_symbol = analysis.get('symbol', 'Unknown')
            analysis_type = analysis.get('analysis_type', 'general')

            # Create a short display name
            display_time = analysis_time.split(' ')[1][:5] if ' ' in analysis_time else analysis_time[:10]
            display_name = f"{display_time} - {analysis_symbol} ({analysis_type})"

            if st.button(display_name, key=f"load_analysis_{i}", help="Click to reload this analysis"):
                # Reload the analysis
                st.session_state["last_ai_analysis"] = analysis.get('analysis_result', {})
                st.session_state["last_analyzed_images"] = [analysis.get('image')] if analysis.get('image') else []
                st.session_state["current_analysis_id"] = analysis.get('analysis_id')
                st.session_state.selected_analysis_id = analysis.get('analysis_id')
                st.success(f"✅ Loaded analysis from {display_time}")
                st.rerun()
    else:
        st.info("No recent analyses yet. Upload charts to get started!")

    st.markdown("---")

    # Quick Stats
    if st.session_state.analysis_history:
        total_analyses = len(st.session_state.analysis_history)
        st.metric("Total Analyses", total_analyses)

        # Show analysis types distribution
        analysis_types = {}
        for analysis in st.session_state.analysis_history:
            atype = analysis.get('analysis_type', 'general')
            analysis_types[atype] = analysis_types.get(atype, 0) + 1

        st.markdown("**Analysis Types:**")
        for atype, count in analysis_types.items():
            st.write(f"• {atype.title()}: {count}")


# Main Content Container
st.markdown('<div class="main-container">', unsafe_allow_html=True)

# File Upload Section
st.markdown("### 📁 Upload Chart Images")
uploaded_files = st.file_uploader(
    "Upload trading chart images (multiple allowed)",
    type=["png", "jpg", "jpeg"],
    accept_multiple_files=True,
    help="Supported formats: PNG, JPG, JPEG. You can upload multiple charts for batch analysis."
)

# Smart symbol detection hint
if uploaded_files:
    st.info("💡 **Smart Symbol Detection**: The AI will automatically detect Bank Nifty, Nifty 50, and other Indian market symbols from your charts and use the correct futures symbols for analysis.")

images_for_analysis = []
if uploaded_files is not None:
    for uploaded_file in uploaded_files:
        image = Image.open(uploaded_file)
        images_for_analysis.append(image)
        st.image(image, caption=f"Uploaded Chart: {uploaded_file.name}", use_container_width=True)

    # Analysis Button with Professional Styling
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("🚀 Analyze Chart", key="analyze_button", use_container_width=True):
            with st.spinner("🤖 AI is analyzing the chart..."):
                # Using LangGraph workflow system - prompts are handled internally
                # Analysis type and market specialization are passed to the workflow

                # Display selected combination
                if market_specialization == "Indian Market":
                    st.info(f"🇮🇳 Using {analysis_type_choice} analysis with Indian market specialization | Timeframes: {', '.join(selected_timeframes) if selected_timeframes else 'None'}")
                elif market_specialization == "Crypto":
                    st.info(f"🪙 Using {analysis_type_choice} analysis with crypto market specialization | Timeframes: {', '.join(selected_timeframes) if selected_timeframes else 'None'}")
                else:
                    st.info(f"📊 Using {analysis_type_choice} analysis with general market tools | Timeframes: {', '.join(selected_timeframes) if selected_timeframes else 'None'}")

                # Validate timeframe selection
                if not selected_timeframes:
                    st.error("❌ **ANALYSIS BLOCKED**: Please select at least one timeframe before proceeding!")
                    st.stop()

            # Get memory system for storing analysis
            memory_system = get_memory_system()

            # Check for similar past analyses
            similar_analyses = memory_system.get_contextual_memory(
                symbol=ticker_hint if 'ticker_hint' in locals() else None,
                mode=analysis_type_choice.lower(),
                market=market_specialization.lower().replace(" ", "_"),
                query=f"chart analysis {analysis_type_choice}"
            )

            if similar_analyses:
                st.info(f"💡 Found {len(similar_analyses)} similar successful analyses in memory")

            # Create enhanced progress tracking containers
            progress_container = st.container()
            with progress_container:
                st.markdown("### 🔄 AI Analysis in Progress")

                # Main progress bar
                progress_bar = st.progress(0)
                status_text = st.empty()

                # Create columns for detailed progress
                col1, col2 = st.columns([2, 1])

                with col1:
                    step_details = st.empty()
                    workflow_status = st.empty()

                with col2:
                    phase_indicator = st.empty()
                    time_tracker = st.empty()

            # Track analysis start time
            analysis_start_time = time.time()

            # Enhanced progress callback function
            def update_progress(message: str, step: int = None, total_steps: int = None):
                current_time = time.time()
                elapsed = current_time - analysis_start_time

                if step is not None and total_steps is not None:
                    progress = step / total_steps
                    progress_bar.progress(progress)
                    status_text.markdown(f"**Step {step}/{total_steps}**: {message}")

                    # Update time tracker
                    time_tracker.metric("Elapsed Time", f"{elapsed:.1f}s")

                    # Update phase indicator
                    if step <= 3:
                        phase = "🔧 Initialization"
                    elif step <= 6:
                        phase = "📊 Chart Analysis"
                    elif step <= 8:
                        phase = "🔧 Tool Execution"
                    else:
                        phase = "📝 Final Analysis"

                    phase_indicator.markdown(f"**Current Phase:**\n{phase}")
                else:
                    status_text.markdown(f"**Status**: {message}")

                # Enhanced step details with better formatting
                with step_details.container():
                    if "tool" in message.lower() and "executing" in message.lower():
                        st.info(f"🔧 **Tool Execution**: {message}")
                    elif "completed" in message.lower() and "tool" in message.lower():
                        st.success(f"✅ **Tool Completed**: {message}")
                    elif "error" in message.lower() or "❌" in message:
                        st.error(f"❌ **Error**: {message}")
                    elif "analysis" in message.lower() and ("completed" in message.lower() or "✅" in message):
                        st.success(f"🎯 **Analysis Complete**: {message}")
                    elif "initializing" in message.lower() or "configuring" in message.lower():
                        st.info(f"⚙️ **Setup**: {message}")
                    elif "processing" in message.lower() or "parsing" in message.lower():
                        st.info(f"⚡ **Processing**: {message}")
                    else:
                        st.info(f"ℹ️ {message}")

                # Update workflow status
                workflow_steps = [
                    "🔧 System Initialization",
                    "🖼️ Image Processing",
                    "🔑 API Configuration",
                    "🤖 AI Model Setup",
                    "💬 Analysis Start",
                    "📊 Chart Examination",
                    "🔧 Tool Execution",
                    "📝 Response Processing",
                    "✅ Analysis Complete"
                ]

                if step is not None and step <= len(workflow_steps):
                    current_step = step - 1
                    workflow_display = []
                    for i, workflow_step in enumerate(workflow_steps):
                        if i < current_step:
                            workflow_display.append(f"✅ {workflow_step}")
                        elif i == current_step:
                            workflow_display.append(f"🔄 {workflow_step}")
                        else:
                            workflow_display.append(f"⏳ {workflow_step}")

                    workflow_status.markdown("**Workflow Progress:**\n" + "\n".join(workflow_display[:6]))

            try:
                if model_source == "Cloud":
                    # Industry Standard: LLM analyzes chart and decides tools dynamically
                    update_progress("🚀 Starting industry standard LLM analysis...", 1, 10)

                    # Display charts for user
                    for i, image in enumerate(images_for_analysis):
                        update_progress(f"📊 Preparing chart {i+1} for analysis...", 2+i, 10)
                        st.image(image, caption=f"Chart {i+1}", use_container_width=True)

                    # Use LangGraph workflow system for comprehensive analysis
                    update_progress("🤖 Starting LangGraph workflow analysis...", 8, 10)

                    # Import the ORIGINAL sophisticated LangGraph workflow system
                    from Agent_Trading.helpers.complete_langgraph_workflow import CompleteLangGraphTradingWorkflow
                    from Agent_Trading.helpers.utils import load_google_api_key
                    from Agent_Trading.GUI.rate_limit_dashboard import display_rate_limit_widget

                    # Convert PIL images to bytes for LangGraph system
                    chart_images_bytes = []
                    for img in images_for_analysis:
                        img_bytes = io.BytesIO()
                        img.save(img_bytes, format='PNG')
                        chart_images_bytes.append(img_bytes.getvalue())

                    # Determine analysis mode from GUI selection
                    analysis_mode = "scalp" if analysis_type_choice == "Scalp" else "positional"

                    # Initialize and run the ORIGINAL sophisticated LangGraph workflow
                    api_key = load_google_api_key()

                    # 🤖 Get user's preferred model from sidebar
                    preferred_model = st.session_state.get('selected_model', 'gemini-2.5-pro')
                    tier = st.session_state.get('rate_limit_tier', 'free')

                    workflow = CompleteLangGraphTradingWorkflow(
                        google_api_key=api_key,
                        tier=tier,
                        preferred_model=preferred_model
                    )

                    # Track analysis start for performance metrics
                    analysis_start = time.time()

                    # Create user query from GUI selections
                    user_query = f"{analysis_type_choice} trading analysis for {market_specialization} market"
                    if selected_timeframes:
                        user_query += f" focusing on {', '.join(selected_timeframes)} timeframes"

                    response_content = workflow.analyze_chart(
                        chart_images=chart_images_bytes,
                        analysis_mode=analysis_mode,
                        user_query=user_query,
                        market_specialization=market_specialization
                    )

                    analysis_duration = time.time() - analysis_start

                    update_progress("✅ LangGraph workflow analysis completed!", 10, 10)

                    # Store quality scores if available
                    if PERFORMANCE_AVAILABLE and response_content.get("quality_score"):
                        if 'quality_scores' not in st.session_state:
                            st.session_state.quality_scores = []
                        st.session_state.quality_scores.append(response_content["quality_score"])

                        # Keep only last 50 scores to prevent memory issues
                        if len(st.session_state.quality_scores) > 50:
                            st.session_state.quality_scores = st.session_state.quality_scores[-50:]

                else:
                    if not ollama_model_name:
                        st.error("Please enter an Ollama model name in the sidebar.")
                        st.stop()
                    update_progress("🤖 Starting local analysis...", 1, 5)
                    # Create user query for local analysis
                    local_user_query = f"{analysis_type_choice} trading analysis for {market_specialization} market"
                    if selected_timeframes:
                        local_user_query += f" focusing on {', '.join(selected_timeframes)} timeframes"
                    response_content = generate_analysis_local(images_for_analysis, local_user_query, ollama_model_name)
                    update_progress("✅ Local analysis completed!", 5, 5)
            except Exception as exc:
                update_progress(f"❌ Error during analysis: {exc}")
                st.error(f"Error during analysis: {exc}")
                st.stop()

            # Show completion summary
            if response_content and not isinstance(response_content, dict) or not response_content.get("error"):
                analysis_end_time = time.time()
                total_time = analysis_end_time - analysis_start_time

                with progress_container:
                    st.success(f"🎯 **Analysis Completed Successfully!** (Total time: {total_time:.1f}s)")

                    # Show final summary
                    if isinstance(response_content, dict) and "tool_usage" in response_content:
                        tool_count = len(response_content["tool_usage"])
                        st.info(f"📊 **Analysis Summary**: Used {tool_count} tools to provide comprehensive market analysis")

                    # Add a small delay to show completion before clearing
                    time.sleep(2)

            # Clear progress indicators after completion
            progress_container.empty()

            # Store analysis in memory and session history
            if response_content and images_for_analysis:
                try:
                    # Extract symbol from response if available
                    detected_symbol = None
                    if isinstance(response_content, dict):
                        detected_symbol = response_content.get('detected_symbol')

                    analysis_id = memory_system.store_analysis(
                        analysis_data={"raw_response": response_content},
                        symbol=detected_symbol or (ticker_hint if 'ticker_hint' in locals() else None),
                        mode=analysis_type_choice.lower(),
                        market="crypto" if market_specialization == "Crypto" else "indian" if market_specialization == "Indian Market" else "general"
                    )
                    st.session_state["current_analysis_id"] = analysis_id

                    # 🧠 Store analysis result for feedback system and RAG integration
                    st.session_state["last_analysis_result"] = response_content

                    # Add to session history for quick access
                    analysis_entry = {
                        'analysis_id': analysis_id,
                        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        'symbol': ticker_hint if 'ticker_hint' in locals() else 'Unknown',
                        'analysis_type': analysis_type_choice.lower(),
                        'analysis_result': response_content,
                        'image': images_for_analysis[0] if images_for_analysis else None
                    }
                    st.session_state.analysis_history.append(analysis_entry)

                    # Keep only last 50 analyses in session
                    if len(st.session_state.analysis_history) > 50:
                        st.session_state.analysis_history = st.session_state.analysis_history[-50:]

                    st.success(f"📝 Analysis stored in memory (ID: {analysis_id[:8]}...)")
                except Exception as e:
                    st.warning(f"Could not store analysis in memory: {e}")

            st.session_state["last_ai_analysis"] = response_content
            st.session_state["last_analyzed_images"] = images_for_analysis

            # Always store analysis in history, even if parsing fails
            try:
                analysis_entry = {
                    'analysis_id': st.session_state.get("current_analysis_id", f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'symbol': ticker_hint if 'ticker_hint' in locals() else 'Unknown',
                    'analysis_type': analysis_type_choice.lower(),
                    'analysis_result': response_content,
                    'image': images_for_analysis[0] if images_for_analysis else None
                }
                st.session_state.analysis_history.append(analysis_entry)

                # Keep only last 50 analyses in session
                if len(st.session_state.analysis_history) > 50:
                    st.session_state.analysis_history = st.session_state.analysis_history[-50:]

                st.success(f"✅ Analysis added to history (Total: {len(st.session_state.analysis_history)})")
            except Exception as e:
                st.warning(f"Could not add to analysis history: {e}")

            st.markdown(
                "<h3 style='color:#1a73e8; margin-top:0.5em; text-align:left;'>AI-Powered Trading Analysis</h3>",
                unsafe_allow_html=True,
            )

            # Display thinking process if available
            if isinstance(response_content, dict) and response_content.get('thinking_process'):
                with st.expander("🧠 AI Thinking Process (Gemini 2.5 Flash)", expanded=False):
                    st.markdown("**AI's reasoning process for this analysis:**")
                    for i, thought in enumerate(response_content['thinking_process'], 1):
                        st.markdown(f"**Step {i}:** {thought}")

                    # Show thinking insights (simplified version)
                    if response_content.get('thinking_process'):
                        st.markdown("**Key Insights from AI Thinking:**")
                        # Extract key insights from thinking process
                        thinking_text = " ".join(response_content['thinking_process']).lower()
                        if 'bullish' in thinking_text or 'buy' in thinking_text:
                            st.info("💡 Market sentiment appears bullish based on analysis")
                        elif 'bearish' in thinking_text or 'sell' in thinking_text:
                            st.info("💡 Market sentiment appears bearish based on analysis")
                        if 'support' in thinking_text:
                            st.info("💡 Key support levels identified in analysis")
                        if 'resistance' in thinking_text:
                            st.info("💡 Key resistance levels identified in analysis")

            # Industry standard analysis completed - no complex optimization needed

            # Handle different response formats
            if response_content:
                # If response contains an error but has raw_response, try to parse it
                if isinstance(response_content, dict) and "error" in response_content and "raw_response" in response_content:
                    st.warning(f"⚠️ JSON Parsing Issue: {response_content['error']}")

                    # Try to extract useful information from raw response
                    raw_response = response_content.get("raw_response", "")
                    if "analysis_notes" in response_content:
                        st.markdown("### 📝 AI Analysis")
                        st.markdown(response_content["analysis_notes"])
                    elif raw_response:
                        st.markdown("### 📝 AI Analysis (Raw)")
                        st.markdown(raw_response)

                    # Still show tool usage if available
                    if "tool_usage" in response_content:
                        display_beautiful_tool_results(response_content["tool_usage"])

                elif isinstance(response_content, dict):
                    # Display main AI analysis first - simplified logic
                    st.markdown("""
                    <div class="analysis-card">
                        <h3 style="color: #1f2937; margin-bottom: 1rem;">📝 AI Trading Analysis</h3>
                    """, unsafe_allow_html=True)

                    # Priority order for displaying main analysis
                    main_analysis = None
                    if response_content.get('detailed_report'):
                        main_analysis = response_content['detailed_report']
                    elif response_content.get('analysis_summary'):
                        main_analysis = response_content['analysis_summary']
                    elif response_content.get('raw_response'):
                        main_analysis = response_content['raw_response']

                    # Display main analysis if found
                    if main_analysis and len(str(main_analysis).strip()) > 50:
                        st.markdown(main_analysis)
                        analysis_displayed = True
                    else:
                        analysis_displayed = False

                    # Close analysis card
                    st.markdown("</div>", unsafe_allow_html=True)

                    # Show structured data if no main analysis or as supplement
                    if not analysis_displayed or response_content.get('trade_ideas'):
                        # Normal structured response handling
                        if analysis_type_choice == "Positional":
                            if "status" in response_content and isinstance(response_content["status"], str):
                                st.markdown(f"**Status:** {response_content['status']}")
                            if "analysis_summary" in response_content and isinstance(
                                response_content["analysis_summary"], str
                            ):
                                st.markdown(f"**Summary:** {response_content['analysis_summary']}")

                            # Extract trade_ideas from various possible locations
                            trade_ideas = None

                            if "trade_ideas" in response_content and isinstance(response_content["trade_ideas"], list):
                                trade_ideas = response_content["trade_ideas"]
                            elif "trading_signals" in response_content and isinstance(response_content["trading_signals"], dict):
                                if "trade_ideas" in response_content["trading_signals"] and isinstance(response_content["trading_signals"]["trade_ideas"], list):
                                    trade_ideas = response_content["trading_signals"]["trade_ideas"]
                            elif "analysis" in response_content and isinstance(response_content["analysis"], str):
                                try:
                                    # Extract JSON from markdown if present
                                    analysis_text = response_content["analysis"]
                                    if "```json" in analysis_text:
                                        json_start = analysis_text.find("```json") + 7
                                        json_end = analysis_text.find("```", json_start)
                                        if json_end > json_start:
                                            json_str = analysis_text[json_start:json_end].strip()
                                            parsed_analysis = json.loads(json_str)
                                            if "trade_ideas" in parsed_analysis:
                                                trade_ideas = parsed_analysis["trade_ideas"]
                                except (json.JSONDecodeError, ValueError):
                                    pass

                            if trade_ideas and isinstance(trade_ideas, list):
                                st.markdown("<h4>Trade Ideas:</h4>", unsafe_allow_html=True)
                                if trade_ideas:
                                    st.dataframe(trade_ideas, use_container_width=True)
                                else:
                                    st.info("No trade ideas recommended at this time.")

                            # Try to extract analysis content from various possible locations
                            analysis_content = None

                            # Check for detailed_report first
                            if "detailed_report" in response_content and isinstance(response_content["detailed_report"], str):
                                analysis_content = response_content["detailed_report"]

                            # Check direct analysis_notes
                            elif "analysis_notes" in response_content and isinstance(response_content["analysis_notes"], str):
                                analysis_content = response_content["analysis_notes"]

                            # Check inside trading_signals
                            elif "trading_signals" in response_content and isinstance(response_content["trading_signals"], dict):
                                if "analysis_notes" in response_content["trading_signals"] and isinstance(response_content["trading_signals"]["analysis_notes"], str):
                                    analysis_content = response_content["trading_signals"]["analysis_notes"]

                            # Check if there's a nested analysis field with JSON
                            elif "analysis" in response_content and isinstance(response_content["analysis"], str):
                                try:
                                    # Extract JSON from markdown if present
                                    analysis_text = response_content["analysis"]
                                    if "```json" in analysis_text:
                                        json_start = analysis_text.find("```json") + 7
                                        json_end = analysis_text.find("```", json_start)
                                        if json_end > json_start:
                                            json_str = analysis_text[json_start:json_end].strip()
                                            parsed_analysis = json.loads(json_str)
                                            if "analysis_notes" in parsed_analysis:
                                                analysis_content = parsed_analysis["analysis_notes"]
                                except (json.JSONDecodeError, ValueError):
                                    pass

                            if analysis_content:
                                st.markdown("### 📝 **DETAILED ANALYSIS**")
                                st.markdown(analysis_content)
                            else:
                                st.warning("⚠️ Main analysis content not found in expected format")
                                with st.expander("🔍 View Raw Response Structure"):
                                    st.json(response_content)
                        else:
                            # For scalping analysis
                            if (
                                "scalp_opportunity" in response_content
                                and isinstance(response_content["scalp_opportunity"], bool)
                            ):
                                status_msg = (
                                    "✅ Valid Scalp Opportunity" if response_content["scalp_opportunity"] else "❌ No Scalp Setup"
                                )
                                st.markdown(f"**{status_msg}**")

                            # Enhanced trade setup display with structured tables
                            # Extract trade_ideas from various possible locations
                            trade_ideas_scalp = None

                            if "trade_ideas" in response_content and response_content["trade_ideas"]:
                                trade_ideas_scalp = response_content["trade_ideas"]
                            elif "trading_signals" in response_content and isinstance(response_content["trading_signals"], dict):
                                if "trade_ideas" in response_content["trading_signals"] and response_content["trading_signals"]["trade_ideas"]:
                                    trade_ideas_scalp = response_content["trading_signals"]["trade_ideas"]
                            elif "analysis" in response_content and isinstance(response_content["analysis"], str):
                                try:
                                    # Extract JSON from markdown if present
                                    analysis_text = response_content["analysis"]
                                    if "```json" in analysis_text:
                                        json_start = analysis_text.find("```json") + 7
                                        json_end = analysis_text.find("```", json_start)
                                        if json_end > json_start:
                                            json_str = analysis_text[json_start:json_end].strip()
                                            parsed_analysis = json.loads(json_str)
                                            if "trade_ideas" in parsed_analysis and parsed_analysis["trade_ideas"]:
                                                trade_ideas_scalp = parsed_analysis["trade_ideas"]
                                except (json.JSONDecodeError, ValueError):
                                    pass

                            if trade_ideas_scalp:
                                st.markdown("### 📊 **TRADE SETUP TABLE**")

                                # Create DataFrame for better display
                                import pandas as pd

                                trade_data = []
                                for i, trade in enumerate(trade_ideas_scalp):
                                    trade_data.append({
                                        "Setup": f"Trade {i+1}",
                                        "Direction": trade.get("Direction", "N/A"),
                                        "Entry Range": trade.get("Entry_Price_Range", "N/A"),
                                        "Stop Loss": trade.get("Stop_Loss", "N/A"),
                                        "Take Profit 1": trade.get("Take_Profit_1", "N/A"),
                                        "Take Profit 2": trade.get("Take_Profit_2", "N/A"),
                                        "Risk:Reward": trade.get("Risk_Reward_Ratio", "N/A"),
                                        "Timeframe": trade.get("Timeframe", "N/A"),
                                        "Confidence": f"{trade.get('Confidence', 'N/A')}/10"
                                    })

                                if trade_data:
                                    df = pd.DataFrame(trade_data)
                                    st.dataframe(df, use_container_width=True, hide_index=True)

                                    # Display entry conditions separately
                                    st.markdown("### 🎯 **ENTRY CONDITIONS**")
                                    for i, trade in enumerate(trade_ideas_scalp):
                                        with st.expander(f"Trade {i+1} Entry Condition"):
                                            st.write(trade.get("Entry_Condition", "No specific condition provided"))

                            # Display key levels - extract from various locations
                            key_levels = None
                            if "key_levels" in response_content:
                                key_levels = response_content["key_levels"]
                            elif "trading_signals" in response_content and isinstance(response_content["trading_signals"], dict):
                                if "key_levels" in response_content["trading_signals"]:
                                    key_levels = response_content["trading_signals"]["key_levels"]
                            elif "analysis" in response_content and isinstance(response_content["analysis"], str):
                                try:
                                    analysis_text = response_content["analysis"]
                                    if "```json" in analysis_text:
                                        json_start = analysis_text.find("```json") + 7
                                        json_end = analysis_text.find("```", json_start)
                                        if json_end > json_start:
                                            json_str = analysis_text[json_start:json_end].strip()
                                            parsed_analysis = json.loads(json_str)
                                            if "key_levels" in parsed_analysis:
                                                key_levels = parsed_analysis["key_levels"]
                                except (json.JSONDecodeError, ValueError):
                                    pass

                            if key_levels:
                                st.markdown("### 🎯 **KEY LEVELS**")
                                col1, col2 = st.columns(2)
                                with col1:
                                    st.markdown("**🟢 Support Levels:**")
                                    for level in key_levels.get("support", []):
                                        st.write(f"• {level}")

                                with col2:
                                    st.markdown("**🔴 Resistance Levels:**")
                                    for level in key_levels.get("resistance", []):
                                        st.write(f"• {level}")

                            # Display market context - extract from various locations
                            market_context = None
                            if "market_context" in response_content:
                                market_context = response_content["market_context"]
                            elif "trading_signals" in response_content and isinstance(response_content["trading_signals"], dict):
                                if "market_context" in response_content["trading_signals"]:
                                    market_context = response_content["trading_signals"]["market_context"]
                            elif "analysis" in response_content and isinstance(response_content["analysis"], str):
                                try:
                                    analysis_text = response_content["analysis"]
                                    if "```json" in analysis_text:
                                        json_start = analysis_text.find("```json") + 7
                                        json_end = analysis_text.find("```", json_start)
                                        if json_end > json_start:
                                            json_str = analysis_text[json_start:json_end].strip()
                                            parsed_analysis = json.loads(json_str)
                                            if "market_context" in parsed_analysis:
                                                market_context = parsed_analysis["market_context"]
                                except (json.JSONDecodeError, ValueError):
                                    pass

                            if market_context:
                                st.markdown("### 📰 **MARKET CONTEXT**")
                                st.info(market_context)

                            # Display risk management - extract from various locations
                            risk_management = None
                            if "risk_management" in response_content:
                                risk_management = response_content["risk_management"]
                            elif "trading_signals" in response_content and isinstance(response_content["trading_signals"], dict):
                                if "risk_management" in response_content["trading_signals"]:
                                    risk_management = response_content["trading_signals"]["risk_management"]
                            elif "analysis" in response_content and isinstance(response_content["analysis"], str):
                                try:
                                    analysis_text = response_content["analysis"]
                                    if "```json" in analysis_text:
                                        json_start = analysis_text.find("```json") + 7
                                        json_end = analysis_text.find("```", json_start)
                                        if json_end > json_start:
                                            json_str = analysis_text[json_start:json_end].strip()
                                            parsed_analysis = json.loads(json_str)
                                            if "risk_management" in parsed_analysis:
                                                risk_management = parsed_analysis["risk_management"]
                                except (json.JSONDecodeError, ValueError):
                                    pass

                            if risk_management:
                                st.markdown("### ⚠️ **RISK MANAGEMENT**")
                                st.warning(risk_management)

                            # Fallback for old format
                            elif "trade_setup" in response_content and isinstance(response_content["trade_setup"], dict):
                                st.markdown("<h4>Trade Setup (Legacy Format):</h4>", unsafe_allow_html=True)
                                st.json(response_content["trade_setup"])

                    # Try to extract analysis_notes from various possible locations
                    analysis_notes = None

                    # Check direct location
                    if "analysis_notes" in response_content and isinstance(response_content["analysis_notes"], str):
                        analysis_notes = response_content["analysis_notes"]

                    # Check inside trading_signals
                    elif "trading_signals" in response_content and isinstance(response_content["trading_signals"], dict):
                        if "analysis_notes" in response_content["trading_signals"] and isinstance(response_content["trading_signals"]["analysis_notes"], str):
                            analysis_notes = response_content["trading_signals"]["analysis_notes"]

                    # Check if there's a nested analysis field with JSON
                    elif "analysis" in response_content and isinstance(response_content["analysis"], str):
                        try:
                            # Extract JSON from markdown if present
                            analysis_text = response_content["analysis"]
                            if "```json" in analysis_text:
                                json_start = analysis_text.find("```json") + 7
                                json_end = analysis_text.find("```", json_start)
                                if json_end > json_start:
                                    json_str = analysis_text[json_start:json_end].strip()
                                    parsed_analysis = json.loads(json_str)
                                    if "analysis_notes" in parsed_analysis:
                                        analysis_notes = parsed_analysis["analysis_notes"]
                        except (json.JSONDecodeError, ValueError):
                            pass

                    if analysis_notes:
                        st.markdown("### 📝 **DETAILED ANALYSIS**")
                        st.markdown(analysis_notes)
                    else:
                        st.warning("⚠️ Analysis notes not found in expected format")
                        with st.expander("🔍 View Raw Response Structure"):
                            st.json(response_content)
            else:
                st.error("Failed to parse AI response as valid JSON. Displaying raw text if available.")
                if response_content:
                    st.text(str(response_content))
                else:
                    st.warning("No response content received from AI.")

            # Debug section to help identify response structure issues
            if st.checkbox("🔍 Debug: Show Response Structure", key="debug_response"):
                st.markdown("**Response Content Keys:**")
                if isinstance(response_content, dict):
                    st.write(list(response_content.keys()))
                    st.markdown("**Response Content Preview:**")
                    st.json(response_content)
                else:
                    st.write(f"Response type: {type(response_content)}")
                    st.text(str(response_content)[:500] + "..." if len(str(response_content)) > 500 else str(response_content))

            # Display structured trade setup if available
            if response_content and isinstance(response_content, dict):
                # Display trade ideas in structured table format
                if "trade_ideas" in response_content and isinstance(response_content["trade_ideas"], list):
                    st.markdown("""
                    <div class="analysis-card">
                        <h3 style="color: #1f2937; margin-bottom: 1rem;">🎯 Trading Opportunities</h3>
                    """, unsafe_allow_html=True)

                    for idx, trade in enumerate(response_content["trade_ideas"], 1):
                        if isinstance(trade, dict):
                            st.markdown(f"### 📊 Trade Setup #{idx}")

                            # Create columns for key metrics
                            col1, col2, col3, col4 = st.columns(4)

                            with col1:
                                direction = trade.get("Direction", "N/A")
                                direction_color = "🟢" if direction.lower() == "long" else "🔴" if direction.lower() == "short" else "⚪"
                                st.metric(
                                    label="Direction",
                                    value=f"{direction_color} {direction}",
                                    delta=f"Timeframe: {trade.get('Timeframe', 'N/A')}"
                                )

                            with col2:
                                st.metric(
                                    label="Entry Range",
                                    value=trade.get("Entry_Price_Range", "N/A"),
                                    delta="Entry Zone"
                                )

                            with col3:
                                st.metric(
                                    label="Risk:Reward",
                                    value=f"1:{trade.get('Risk_Reward_Ratio', 'N/A')}",
                                    delta="Ratio"
                                )

                            with col4:
                                confidence = trade.get("Confidence", "N/A")
                                confidence_color = "🟢" if str(confidence).replace('.','').isdigit() and float(confidence) >= 8 else "🟡" if str(confidence).replace('.','').isdigit() and float(confidence) >= 6 else "🔴"
                                st.metric(
                                    label="Confidence",
                                    value=f"{confidence_color} {confidence}/10",
                                    delta="Score"
                                )

                            # Trade details in a structured table
                            st.markdown("**📋 Trade Parameters:**")

                            # Create a clean table
                            trade_details = {
                                "🎯 Entry Condition": trade.get("Entry_Condition", "N/A"),
                                "🛑 Stop Loss": trade.get("Stop_Loss", "N/A"),
                                "💰 Take Profit 1": trade.get("Take_Profit_1", "N/A"),
                                "💎 Take Profit 2": trade.get("Take_Profit_2", "N/A")
                            }

                            # Display in a nice format
                            for param, value in trade_details.items():
                                col_param, col_value = st.columns([1, 2])
                                with col_param:
                                    st.write(f"**{param}**")
                                with col_value:
                                    st.write(value)

                            st.markdown("---")

                # Add LLM-based chart annotation after analysis is complete
                if "trade_ideas" in response_content and uploaded_files:
                    st.markdown("### 📊 Smart Chart Analysis with Trading Levels")

                    # Find the smallest timeframe chart for annotation (entry chart)
                    if selected_timeframes:
                        smallest_tf = min(selected_timeframes, key=lambda x: {
                            "1m": 1, "3m": 3, "5m": 5, "15m": 15, "1h": 60, "1d": 1440
                        }.get(x, 999))

                        st.info(f"🎯 **Entry Chart Focus**: Annotating {smallest_tf} timeframe chart for precise entry timing")

                    for idx, uploaded_file in enumerate(uploaded_files):
                        try:
                            # Re-read the image for annotation
                            uploaded_file.seek(0)
                            image = Image.open(uploaded_file)
                            if image.mode != 'RGB':
                                image = image.convert('RGB')

                            # Convert to bytes for LLM analysis
                            img_buffer = io.BytesIO()
                            image.save(img_buffer, format='JPEG', quality=95)
                            img_bytes = img_buffer.getvalue()

                            # LLM-based chart annotation prompt
                            annotation_prompt = f"""
                            Analyze this chart image and provide detailed annotations for the trading setup:

                            Trade Setup: {response_content.get('trade_ideas', [{}])[0] if response_content.get('trade_ideas') else {}}

                            Provide:
                            1. **Entry Zone Description**: Describe where exactly on the chart the entry should occur
                            2. **Stop Loss Placement**: Explain the stop loss positioning relative to chart structure
                            3. **Take Profit Levels**: Identify where TP1 and TP2 align with chart levels
                            4. **Key Chart Patterns**: Point out important patterns, support/resistance
                            5. **Risk Areas**: Highlight areas to avoid or watch for invalidation

                            Format as clear, actionable annotations that a trader can use for precise entry timing.
                            """

                            # Generate LLM-based chart analysis
                            with st.spinner(f"🤖 AI analyzing chart {idx+1} for trading levels..."):
                                try:
                                    # Use Gemini for chart annotation analysis
                                    import google.generativeai as genai
                                    genai.configure(api_key=google_api_key)
                                    model = genai.GenerativeModel(gemini_model_id)

                                    # Convert image to base64 for Gemini
                                    import base64
                                    img_base64 = base64.b64encode(img_bytes).decode()

                                    response = model.generate_content([
                                        annotation_prompt,
                                        {"mime_type": "image/jpeg", "data": img_base64}
                                    ])

                                    chart_analysis = response.text

                                    # Display original chart with LLM analysis
                                    col1, col2 = st.columns([3, 2])
                                    with col1:
                                        st.image(image, caption=f"📊 Chart {idx+1} - {smallest_tf if 'smallest_tf' in locals() else 'Entry'} Timeframe", use_container_width=True)

                                    with col2:
                                        st.markdown("### 🎯 AI Chart Analysis")
                                        st.markdown(chart_analysis)

                                        # Add download button for analysis
                                        st.download_button(
                                            label="📄 Download Analysis",
                                            data=f"Chart Analysis for {uploaded_file.name}\n\n{chart_analysis}",
                                            file_name=f"chart_analysis_{idx+1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                                            mime="text/plain",
                                            key=f"download_analysis_{idx}"
                                        )

                                except Exception as e:
                                    st.error(f"❌ LLM chart analysis failed: {e}")
                                    # Fallback to basic display
                                    st.image(image, caption=f"📊 Chart {idx+1} (Analysis unavailable)", use_container_width=True)

                        except Exception as e:
                            st.warning(f"⚠️ Chart processing failed for chart {idx+1}: {e}")

            # Display beautiful tool usage information
            tool_usage = None
            if response_content and isinstance(response_content, dict):
                # Check for both tool_usage and tool_usage_log
                if "tool_usage" in response_content:
                    tool_usage = response_content["tool_usage"]
                elif "tool_usage_log" in response_content:
                    tool_usage = response_content["tool_usage_log"]

                if tool_usage:
                    # Use the new beautiful display function
                    display_beautiful_tool_results(tool_usage)
                else:
                    st.warning("⚠️ **ISSUE**: No external tools were used! The AI should have called multiple tools (market data, news, FII/DII flows) for comprehensive analysis. This may indicate a prompt following issue.")
                    st.info("💡 **Expected Tools**: get_market_context_summary, get_comprehensive_market_news, get_fii_dii_flows (for Indian markets)")


# --- Enhanced Feedback Section with Memory Integration ---
if "last_ai_analysis" in st.session_state and st.session_state["last_ai_analysis"] is not None:
    st.markdown("<br><hr><h3 style='color:#1a73e8; margin-top:0.5em; text-align:left;'>📝 Provide Feedback & Rate Analysis</h3>", unsafe_allow_html=True)

    # Success rating system
    col1, col2 = st.columns([1, 2])

    with col1:
        st.markdown("**Rate this analysis:**")
        success_rating = st.select_slider(
            "How accurate was this analysis?",
            options=[0.1, 0.3, 0.5, 0.7, 0.9, 1.0],
            value=0.5,
            format_func=lambda x: {
                0.1: "❌ Very Poor",
                0.3: "⚠️ Poor",
                0.5: "🤔 Average",
                0.7: "✅ Good",
                0.9: "🎯 Excellent",
                1.0: "🏆 Perfect"
            }[x],
            key="success_rating"
        )

    with col2:
        st.markdown("**Trade outcome (if executed):**")
        trade_outcome = st.selectbox(
            "What happened with this trade?",
            ["Not traded yet", "Profitable", "Break-even", "Loss", "Stopped out"],
            key="trade_outcome"
        )

    feedback_image_uploaded_file = st.file_uploader(
        "Upload an image for feedback (e.g., trade result, updated chart)",
        type=["png", "jpg", "jpeg"],
        key="feedback_image_uploader",
    )

    feedback_image = None
    if feedback_image_uploaded_file is not None:
        feedback_image = Image.open(feedback_image_uploaded_file)
        st.image(feedback_image, caption="Feedback Image", use_container_width=True)

    user_feedback = st.text_area(
        "Additional feedback or different view on the AI's analysis:",
        key="user_feedback_text_area",
        height=150,
        value=st.session_state.feedback_text_value,
        placeholder="What worked well? What could be improved? Any market context the AI missed?"
    )

    # Two buttons: one for just storing feedback, one for AI re-evaluation
    col1, col2 = st.columns(2)

    with col1:
        if st.button("💾 Save Feedback to Memory", key="save_feedback_button"):
            # Store feedback in both memory system AND ChromaDB RAG for pattern learning
            if "current_analysis_id" in st.session_state and "last_analysis_result" in st.session_state:
                memory_system = get_memory_system()
                feedback_data = {
                    "success_score": success_rating,
                    "trade_outcome": trade_outcome,
                    "user_feedback": user_feedback,
                    "timestamp": str(datetime.now())
                }

                # Store in basic memory system
                success = memory_system.add_feedback(
                    st.session_state["current_analysis_id"],
                    feedback_data
                )

                # 🧠 ALSO store in ChromaDB RAG for pattern learning
                if success and trade_outcome != "Not traded yet":
                    try:
                        from helpers.chromadb_rag_system import ChromaDBTradingRAGSystem

                        rag_system = ChromaDBTradingRAGSystem()
                        last_result = st.session_state["last_analysis_result"]

                        # Extract setup data from last analysis
                        setup_data = {
                            "chart_analysis": last_result.get("chart_analysis", {}),
                            "tool_summaries": last_result.get("tool_summaries", {}),
                            "market_context": last_result.get("market_context", ""),
                            "analysis_mode": last_result.get("analysis_mode", "positional"),
                            "confidence_score": success_rating
                        }

                        # Convert trade outcome to standard format
                        outcome_mapping = {
                            "Profitable": "win",
                            "Break-even": "breakeven",
                            "Loss": "loss",
                            "Stopped out": "loss"
                        }

                        outcome_data = {
                            "outcome": outcome_mapping.get(trade_outcome, "unknown"),
                            "user_feedback": user_feedback,
                            "success_score": success_rating,
                            "timestamp": str(datetime.now())
                        }

                        # Store in RAG for pattern learning
                        rag_doc_id = rag_system.store_trading_outcome(
                            setup_data=setup_data,
                            outcome_data=outcome_data,
                            symbol=last_result.get("detected_symbol", ""),
                            mode=last_result.get("analysis_mode", "positional"),
                            market=last_result.get("market_type", "crypto")
                        )

                        if rag_doc_id:
                            st.success("✅ Feedback saved to memory AND RAG system! This will help improve future pattern recognition.")
                        else:
                            st.success("✅ Feedback saved to memory! (RAG storage had issues but basic memory is saved)")

                    except Exception as e:
                        st.success("✅ Feedback saved to memory! (RAG integration had issues but basic memory is saved)")
                        st.error(f"RAG storage error: {str(e)}")

                if success:
                    # Show memory stats
                    stats = memory_system.get_analysis_stats()
                    st.info(f"📊 Memory Stats: {stats['total_analyses']} analyses, {stats['success_rate']:.1%} success rate")
                else:
                    st.error("❌ Could not save feedback to memory")
            else:
                st.warning("No analysis data found. Please analyze a chart first.")

    with col2:
        if st.button("🤖 Get AI Re-evaluation", key="send_feedback_button"):
            if user_feedback:
                with st.spinner("AI is re-evaluating based on your feedback..."):
                    try:
                        original_ai_output_json_string = json.dumps(st.session_state["last_ai_analysis"], indent=2)
                        re_evaluation_prompt = feedback_prompt_template.format(
                            original_ai_output_json=original_ai_output_json_string,
                            user_feedback_text=user_feedback,
                        )
                    except Exception as exc:
                        st.error(f"Error preparing re-evaluation prompt: {exc}")
                        st.stop()

                    re_evaluation_response_text = ""
                    if model_source == "Cloud":
                        try:
                            # Convert PIL images to bytes for LangGraph system
                            chart_images_bytes = []
                            for img in st.session_state["last_analyzed_images"]:
                                if feedback_image:
                                    chart_images_bytes.append(feedback_image)
                                img_bytes = io.BytesIO()
                                img.save(img_bytes, format='PNG')
                                chart_images_bytes.append(img_bytes.getvalue())

                            # Use the ORIGINAL sophisticated LangGraph workflow for re-evaluation
                            preferred_model = st.session_state.get('selected_model', 'gemini-2.5-pro')
                            tier = st.session_state.get('rate_limit_tier', 'free')

                            workflow = CompleteLangGraphTradingWorkflow(
                                google_api_key=google_api_key,
                                tier=tier,
                                preferred_model=preferred_model
                            )
                            response = workflow.analyze_chart(
                                chart_images=chart_images_bytes,
                                analysis_mode="positional",  # Default for re-evaluation
                                user_query=re_evaluation_prompt
                            )
                            re_evaluation_response_text = json.dumps(response, indent=2)
                        except Exception as exc:
                            st.error(
                                f"Error configuring or calling Gemini API during re-evaluation: {exc}"
                            )
                            st.stop()
                    else:
                        images_to_send = list(st.session_state["last_analyzed_images"])
                        if feedback_image:
                            images_to_send.append(feedback_image)
                        full_response = generate_analysis_local(
                            images_to_send, re_evaluation_prompt, ollama_model_name
                        )
                        re_evaluation_response_text = json.dumps(full_response, indent=2)

                    st.markdown(
                        "<h4 style='color:#1a73e8; margin-top:0.5em; text-align:left;'>AI's Re-evaluation:</h4>",
                        unsafe_allow_html=True,
                    )
                    st.markdown(re_evaluation_response_text)
                    st.session_state.feedback_text_value = ""
            else:
                st.warning("Please enter your feedback before sending.")


# --- Performance Dashboard ---
if PERFORMANCE_AVAILABLE:
    st.markdown("<hr>", unsafe_allow_html=True)
    st.markdown("<h3 style='color:#1a73e8; margin-top:0.5em; text-align:left;'>⚡ Performance Dashboard</h3>", unsafe_allow_html=True)

    # Create tabs for different performance views
    perf_tab1, perf_tab2, perf_tab3, perf_tab4 = st.tabs(["📊 Overview", "🔋 Token Usage", "🚀 Cache", "💰 Cost Analysis"])

    try:
        # Initialize performance systems
        performance_optimizer = get_performance_optimizer()
        qa_system = get_qa_system() if hasattr(st.session_state, 'google_api_key') else None

        with perf_tab1:
            display_performance_metrics(performance_optimizer, qa_system)

        with perf_tab2:
            display_token_usage_breakdown(performance_optimizer)

        with perf_tab3:
            display_cache_performance(performance_optimizer)

        with perf_tab4:
            display_cost_optimization_insights(performance_optimizer)

        # Add quality metrics if available
        if hasattr(st.session_state, 'quality_scores') and st.session_state.quality_scores:
            st.markdown("<hr>", unsafe_allow_html=True)
            display_quality_metrics(st.session_state.quality_scores)

        # Create performance sidebar
        create_performance_sidebar(performance_optimizer)

    except Exception as e:
        st.error(f"Performance dashboard error: {e}")

# --- Memory Dashboard ---
st.markdown("<hr>", unsafe_allow_html=True)
st.markdown("<h3 style='color:#1a73e8; margin-top:0.5em; text-align:left;'>📊 Analysis Memory Dashboard</h3>", unsafe_allow_html=True)

try:
    memory_system = get_memory_system()
    stats = memory_system.get_analysis_stats()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Analyses", stats['total_analyses'])

    with col2:
        st.metric("With Feedback", stats['analyses_with_feedback'])

    with col3:
        st.metric("Successful", stats['successful_analyses'])

    with col4:
        success_rate = stats['success_rate'] * 100 if stats['success_rate'] else 0
        st.metric("Success Rate", f"{success_rate:.1f}%")

    # Symbol breakdown
    if stats['symbol_breakdown']:
        st.markdown("**Analysis by Symbol:**")
        symbol_data = stats['symbol_breakdown']
        symbols = list(symbol_data.keys())
        counts = list(symbol_data.values())

        # Create a simple bar chart using columns
        for symbol, count in symbol_data.items():
            if symbol != 'unknown':
                st.write(f"• **{symbol}**: {count} analyses")

except Exception as e:
    st.warning(f"Could not load memory dashboard: {e}")

# Close main container
st.markdown('</div>', unsafe_allow_html=True)

# Footer
st.markdown("<hr>", unsafe_allow_html=True)
st.markdown(
    """
    <div style='color:#888; font-size:0.98em; margin-top:1em; text-align: center;'>
    <b>Disclaimer:</b> This is an AI-generated analysis and should not be considered financial advice.<br>
    Always do your own research before making any trading decisions.<br><br>
    <b>Enhanced Features:</b> This system now includes Delta Exchange crypto integration,
    enhanced Indian market analysis, and memory-based learning from your feedback.
    </div>
    """,
    unsafe_allow_html=True,
)
