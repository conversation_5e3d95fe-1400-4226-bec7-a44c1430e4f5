#!/usr/bin/env python3
"""
🚦 GEMINI API RATE LIMITER
Official rate limits tracking based on Google's documentation:
https://ai.google.dev/gemini-api/docs/rate-limits

Tracks RPM (Requests Per Minute), TPM (Tokens Per Minute), and RPD (Requests Per Day)
for all Gemini models across different usage tiers.
"""

import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class RateLimit:
    """Rate limit configuration for a specific model and tier."""
    rpm: int  # Requests per minute
    tpm: int  # Tokens per minute  
    rpd: int  # Requests per day
    batch_tokens: Optional[int] = None  # Batch enqueued tokens

@dataclass
class UsageStats:
    """Current usage statistics."""
    requests_this_minute: int = 0
    tokens_this_minute: int = 0
    requests_today: int = 0
    last_minute_reset: float = 0
    last_day_reset: float = 0

class GeminiRateLimiter:
    """
    🚦 OFFICIAL GEMINI API RATE LIMITER
    
    Based on official Google documentation with exact limits for:
    - Free Tier, Tier 1, Tier 2, Tier 3
    - All Gemini models (2.5 Pro, 2.5 Flash, 2.0 Flash, etc.)
    - RPM, TPM, RPD tracking
    """
    
    # 📊 OFFICIAL RATE LIMITS FROM GOOGLE DOCUMENTATION
    RATE_LIMITS = {
        "free": {
            "gemini-2.5-pro": RateLimit(rpm=5, tpm=250000, rpd=100),
            "gemini-2.5-flash": RateLimit(rpm=10, tpm=250000, rpd=250),
            "gemini-2.5-flash-lite": RateLimit(rpm=15, tpm=250000, rpd=1000),
            "gemini-2.0-flash": RateLimit(rpm=15, tpm=1000000, rpd=200),
            "gemini-2.0-flash-lite": RateLimit(rpm=30, tpm=1000000, rpd=200),
            "gemini-2.0-flash-exp": RateLimit(rpm=15, tpm=1000000, rpd=200),  # Experimental
        },
        "tier1": {
            "gemini-2.5-pro": RateLimit(rpm=150, tpm=2000000, rpd=10000, batch_tokens=5000000),
            "gemini-2.5-flash": RateLimit(rpm=1000, tpm=1000000, rpd=10000, batch_tokens=3000000),
            "gemini-2.5-flash-lite": RateLimit(rpm=4000, tpm=4000000, rpd=999999, batch_tokens=10000000),
            "gemini-2.0-flash": RateLimit(rpm=2000, tpm=4000000, rpd=999999, batch_tokens=10000000),
            "gemini-2.0-flash-lite": RateLimit(rpm=4000, tpm=4000000, rpd=999999, batch_tokens=10000000),
            "gemini-2.0-flash-exp": RateLimit(rpm=2000, tpm=4000000, rpd=999999, batch_tokens=10000000),
        },
        "tier2": {
            "gemini-2.5-pro": RateLimit(rpm=1000, tpm=5000000, rpd=50000, batch_tokens=500000000),
            "gemini-2.5-flash": RateLimit(rpm=2000, tpm=3000000, rpd=100000, batch_tokens=400000000),
            "gemini-2.5-flash-lite": RateLimit(rpm=10000, tpm=10000000, rpd=999999, batch_tokens=500000000),
            "gemini-2.0-flash": RateLimit(rpm=10000, tpm=10000000, rpd=999999, batch_tokens=1000000000),
            "gemini-2.0-flash-lite": RateLimit(rpm=20000, tpm=10000000, rpd=999999, batch_tokens=1000000000),
            "gemini-2.0-flash-exp": RateLimit(rpm=10000, tpm=10000000, rpd=999999, batch_tokens=1000000000),
        },
        "tier3": {
            "gemini-2.5-pro": RateLimit(rpm=2000, tpm=8000000, rpd=999999, batch_tokens=1000000000),
            "gemini-2.5-flash": RateLimit(rpm=10000, tpm=8000000, rpd=999999, batch_tokens=1000000000),
            "gemini-2.5-flash-lite": RateLimit(rpm=30000, tpm=30000000, rpd=999999, batch_tokens=1000000000),
            "gemini-2.0-flash": RateLimit(rpm=30000, tpm=30000000, rpd=999999, batch_tokens=5000000000),
            "gemini-2.0-flash-lite": RateLimit(rpm=30000, tpm=30000000, rpd=999999, batch_tokens=5000000000),
            "gemini-2.0-flash-exp": RateLimit(rpm=30000, tpm=30000000, rpd=999999, batch_tokens=5000000000),
        }
    }
    
    def __init__(self, tier: str = "free", storage_file: str = "gemini_usage.json"):
        """
        Initialize rate limiter.
        
        Args:
            tier: Usage tier ("free", "tier1", "tier2", "tier3")
            storage_file: File to persist usage statistics
        """
        self.tier = tier.lower()
        self.storage_file = Path(storage_file)
        self.usage_stats: Dict[str, UsageStats] = {}
        
        # Load existing usage stats
        self._load_usage_stats()
        
        logger.info(f"🚦 Gemini Rate Limiter initialized for {tier.upper()} tier")
    
    def _load_usage_stats(self):
        """Load usage statistics from file."""
        try:
            if self.storage_file.exists():
                with open(self.storage_file, 'r') as f:
                    data = json.load(f)
                    for model, stats in data.items():
                        self.usage_stats[model] = UsageStats(**stats)
                logger.info(f"📊 Loaded usage stats for {len(self.usage_stats)} models")
        except Exception as e:
            logger.warning(f"⚠️ Could not load usage stats: {e}")
            self.usage_stats = {}
    
    def _save_usage_stats(self):
        """Save usage statistics to file."""
        try:
            data = {model: asdict(stats) for model, stats in self.usage_stats.items()}
            with open(self.storage_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"❌ Could not save usage stats: {e}")
    
    def _reset_counters_if_needed(self, model: str):
        """Reset minute and day counters if time periods have elapsed."""
        now = time.time()
        stats = self.usage_stats.get(model, UsageStats())
        
        # Reset minute counter (every 60 seconds)
        if now - stats.last_minute_reset >= 60:
            stats.requests_this_minute = 0
            stats.tokens_this_minute = 0
            stats.last_minute_reset = now
        
        # Reset day counter (at midnight Pacific time)
        # For simplicity, using 24-hour periods from last reset
        if now - stats.last_day_reset >= 86400:  # 24 hours
            stats.requests_today = 0
            stats.last_day_reset = now
        
        self.usage_stats[model] = stats
    
    def get_rate_limits(self, model: str) -> Optional[RateLimit]:
        """Get rate limits for a specific model in current tier."""
        tier_limits = self.RATE_LIMITS.get(self.tier, {})
        return tier_limits.get(model)
    
    def check_rate_limit(self, model: str, estimated_tokens: int = 1000) -> Tuple[bool, str]:
        """
        Check if request would exceed rate limits.
        
        Args:
            model: Model name (e.g., "gemini-2.5-pro")
            estimated_tokens: Estimated tokens for the request
            
        Returns:
            (can_proceed, reason_if_blocked)
        """
        # Reset counters if needed
        self._reset_counters_if_needed(model)
        
        # Get rate limits for this model
        limits = self.get_rate_limits(model)
        if not limits:
            return False, f"❌ Model {model} not supported in {self.tier} tier"
        
        # Get current usage
        stats = self.usage_stats.get(model, UsageStats())
        
        # Check RPM (Requests Per Minute)
        if stats.requests_this_minute >= limits.rpm:
            return False, f"🚫 RPM limit exceeded: {stats.requests_this_minute}/{limits.rpm}"
        
        # Check TPM (Tokens Per Minute)
        if stats.tokens_this_minute + estimated_tokens > limits.tpm:
            return False, f"🚫 TPM limit exceeded: {stats.tokens_this_minute + estimated_tokens}/{limits.tpm}"
        
        # Check RPD (Requests Per Day)
        if limits.rpd != 999999 and stats.requests_today >= limits.rpd:
            return False, f"🚫 RPD limit exceeded: {stats.requests_today}/{limits.rpd}"
        
        return True, "✅ Rate limit check passed"
    
    def record_request(self, model: str, actual_tokens: int):
        """
        Record a completed request.
        
        Args:
            model: Model name
            actual_tokens: Actual tokens used in the request
        """
        # Reset counters if needed
        self._reset_counters_if_needed(model)
        
        # Get or create usage stats
        stats = self.usage_stats.get(model, UsageStats())
        
        # Update counters
        stats.requests_this_minute += 1
        stats.tokens_this_minute += actual_tokens
        stats.requests_today += 1
        
        # Store updated stats
        self.usage_stats[model] = stats
        
        # Save to file
        self._save_usage_stats()
        
        logger.info(f"📊 Recorded request: {model} - {actual_tokens} tokens")
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """Get comprehensive usage summary for dashboard display."""
        summary = {
            "tier": self.tier.upper(),
            "models": {},
            "total_requests_today": 0,
            "total_tokens_today": 0
        }
        
        for model, stats in self.usage_stats.items():
            self._reset_counters_if_needed(model)
            limits = self.get_rate_limits(model)
            
            if limits:
                model_summary = {
                    "requests_this_minute": f"{stats.requests_this_minute}/{limits.rpm}",
                    "tokens_this_minute": f"{stats.tokens_this_minute:,}/{limits.tpm:,}",
                    "requests_today": f"{stats.requests_today}/{limits.rpd}" if limits.rpd != 999999 else f"{stats.requests_today}/∞",
                    "rpm_usage_percent": round((stats.requests_this_minute / limits.rpm) * 100, 1),
                    "tpm_usage_percent": round((stats.tokens_this_minute / limits.tpm) * 100, 1),
                    "rpd_usage_percent": round((stats.requests_today / limits.rpd) * 100, 1) if limits.rpd != 999999 else 0,
                    "limits": {
                        "rpm": limits.rpm,
                        "tpm": f"{limits.tpm:,}",
                        "rpd": limits.rpd if limits.rpd != 999999 else "No limit"
                    }
                }
                summary["models"][model] = model_summary
                summary["total_requests_today"] += stats.requests_today
        
        return summary
    
    def get_tier_info(self) -> Dict[str, Any]:
        """Get information about current tier and upgrade requirements."""
        tier_info = {
            "current_tier": self.tier.upper(),
            "description": "",
            "upgrade_requirements": "",
            "next_tier_benefits": ""
        }
        
        if self.tier == "free":
            tier_info["description"] = "Free tier - Users in eligible countries"
            tier_info["upgrade_requirements"] = "Link billing account to upgrade to Tier 1"
            tier_info["next_tier_benefits"] = "Higher RPM/TPM limits, batch processing"
        elif self.tier == "tier1":
            tier_info["description"] = "Tier 1 - Billing account linked"
            tier_info["upgrade_requirements"] = "Spend >$250 and wait 30 days for Tier 2"
            tier_info["next_tier_benefits"] = "Much higher limits, more models"
        elif self.tier == "tier2":
            tier_info["description"] = "Tier 2 - $250+ spent, 30+ days"
            tier_info["upgrade_requirements"] = "Spend >$1,000 and wait 30 days for Tier 3"
            tier_info["next_tier_benefits"] = "Maximum limits, unlimited RPD"
        elif self.tier == "tier3":
            tier_info["description"] = "Tier 3 - $1,000+ spent, 30+ days (Highest tier)"
            tier_info["upgrade_requirements"] = "Already at highest tier"
            tier_info["next_tier_benefits"] = "You have maximum rate limits"
        
        return tier_info

# Global rate limiter instance
rate_limiter = None

def get_rate_limiter(tier: str = "free") -> GeminiRateLimiter:
    """Get or create global rate limiter instance."""
    global rate_limiter
    if rate_limiter is None or rate_limiter.tier != tier.lower():
        rate_limiter = GeminiRateLimiter(tier)
    return rate_limiter
