#!/usr/bin/env python3
"""
🎯 Test flow.svg Architecture Implementation
Tests the complete 4-step workflow with real trading chart images.

EXPECTED FLOW:
Step 1: Chart Upload → Gemini 2.5 Pro (Vision + Symbol Detection) → LLM Call 1
Step 2: Smart Tool Selection → LLM decides which APIs to call → API Calls (NOT LLM)
Step 3: Tool Results → Gemini 2.5 Flash Summarization → LLM Call 2  
Step 4: Final Analysis → Chart + Summary + RAG Tool → Gemini 2.5 Pro → LLM Call 3

✅ ONLY 3 LLM CALLS TOTAL
"""

import sys
import os
import json
import time
from pathlib import Path
from PIL import Image

# Add Agent_Trading to path
sys.path.insert(0, os.path.join(os.getcwd(), 'Agent_Trading'))

def test_flow_svg_architecture():
    """Test the complete flow.svg architecture with real trading charts."""
    
    print("🎯" + "="*60)
    print("🎯 TESTING FLOW.SVG ARCHITECTURE IMPLEMENTATION")
    print("🎯" + "="*60)
    
    try:
        # Import the workflow
        from helpers.complete_langgraph_workflow import CompleteLangGraphTradingWorkflow
        import google.generativeai as genai
        
        # Load configuration
        config_path = "config.json"
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Configure Gemini API
        genai.configure(api_key=config['google_api_key'])
        
        print("✅ Configuration loaded successfully")
        print("✅ Gemini API configured")
        
        # Initialize workflow
        workflow = CompleteLangGraphTradingWorkflow(
            google_api_key=config['google_api_key'],
            tier="free",
            preferred_model="gemini-2.5-pro"
        )
        
        print("✅ CompleteLangGraphTradingWorkflow initialized")
        
        # Test with both testing images
        test_images = [
            "Testing_images/Screenshot 2025-08-03 190956.png",
            "Testing_images/Screenshot 2025-08-03 191041.png"
        ]
        
        for i, image_path in enumerate(test_images, 1):
            print(f"\n🧪" + "="*50)
            print(f"🧪 TEST {i}: {image_path}")
            print(f"🧪" + "="*50)
            
            if not os.path.exists(image_path):
                print(f"❌ Image not found: {image_path}")
                continue
            
            # Load image
            try:
                chart_image = Image.open(image_path)
                print(f"✅ Loaded chart: {chart_image.size} pixels, mode: {chart_image.mode}")

                # Convert to bytes for the workflow
                import io
                img_bytes = io.BytesIO()
                if chart_image.mode == 'RGBA':
                    chart_image = chart_image.convert('RGB')
                chart_image.save(img_bytes, format='JPEG', quality=95)
                chart_bytes = img_bytes.getvalue()
                print(f"✅ Converted to bytes: {len(chart_bytes)} bytes")

            except Exception as e:
                print(f"❌ Failed to load image: {e}")
                continue
            
            # Test different analysis modes
            test_configs = [
                {"analysis_mode": "positional", "market_specialization": "indian market"},
                {"analysis_mode": "scalp", "market_specialization": "crypto"}
            ]
            
            for config_test in test_configs:
                print(f"\n🎯 Testing: {config_test['analysis_mode']} + {config_test['market_specialization']}")
                
                # Record start time
                start_time = time.time()
                
                # Run the analysis
                try:
                    result = workflow.analyze_chart(
                        chart_images=[chart_bytes],
                        analysis_mode=config_test["analysis_mode"],
                        market_specialization=config_test["market_specialization"]
                    )
                    
                    execution_time = time.time() - start_time
                    
                    # Analyze results
                    print(f"⏱️  Total execution time: {execution_time:.2f}s")
                    print(f"✅ Analysis completed: {result.get('success', False)}")
                    
                    if result.get('success'):
                        # Verify flow.svg architecture
                        print("\n🔍 VERIFYING FLOW.SVG ARCHITECTURE:")
                        
                        # Check detected symbol (Step 1)
                        detected_symbol = result.get('detected_symbol', 'Unknown')
                        market_type = result.get('market_type', 'unknown')
                        print(f"  🎯 Step 1 - Symbol Detection: {detected_symbol} ({market_type})")
                        
                        # Check tool results (Step 2) - Fix: use 'tool_usage' not 'tool_results'
                        tool_results = result.get('tool_usage', {})
                        print(f"  ⚡ Step 2 - Tools Executed: {len(tool_results)} tools")
                        for tool_name in tool_results.keys():
                            print(f"    - {tool_name}")

                        # Also check for summarization evidence
                        progress_messages = result.get('progress_messages', [])
                        flash_summarization = any("Flash Summarization" in msg or "LLM Call 2" in msg for msg in progress_messages)
                        if flash_summarization:
                            print("  ✅ Step 3 - Flash Summarization Confirmed")
                        else:
                            print("  ❌ Step 3 - Flash Summarization NOT detected")
                        
                        # Check if we have analysis (Step 4)
                        analysis = result.get('analysis', '')
                        if analysis:
                            print(f"  🎯 Step 4 - Final Analysis: {len(analysis)} characters")
                            print(f"    Preview: {analysis[:100]}...")
                        
                        # Check workflow status
                        workflow_status = result.get('workflow_status', 'unknown')
                        print(f"  📊 Workflow Status: {workflow_status}")
                        
                        # Check progress messages for flow.svg confirmation
                        progress_messages = result.get('progress_messages', [])
                        flow_svg_confirmed = any("flow.svg" in msg for msg in progress_messages)
                        if flow_svg_confirmed:
                            print("  ✅ flow.svg Architecture Confirmed in Progress Messages")
                        
                        print(f"\n📊 RESULT SUMMARY:")
                        print(f"  - Success: {result.get('success')}")
                        print(f"  - Symbol: {detected_symbol}")
                        print(f"  - Market: {market_type}")
                        print(f"  - Tools: {len(tool_results)}")
                        print(f"  - Analysis Length: {len(analysis)} chars")
                        print(f"  - Execution Time: {execution_time:.2f}s")
                        
                    else:
                        print(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
                        
                except Exception as e:
                    execution_time = time.time() - start_time
                    print(f"❌ Test failed after {execution_time:.2f}s: {e}")
                    import traceback
                    traceback.print_exc()
                
                print("-" * 50)
        
        print(f"\n🎯" + "="*60)
        print("🎯 FLOW.SVG ARCHITECTURE TEST COMPLETED")
        print("🎯" + "="*60)
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_flow_svg_architecture()
