"""
Initialize Database and Fetch Historical Data
============================================

Professional script to:
1. Create database directory structure
2. Initialize SQLite3 database with proper schema
3. Test Delta Exchange API connection
4. Fetch 1 year of historical data for backtesting
5. Validate data quality
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from core.config_manager import ConfigManager
    from core.logger import get_logger
    from crypto_market.engines.data_engine.database_manager import DatabaseManager
    from crypto_market.engines.data_engine.historical_manager import HistoricalManager
    from crypto_market.engines.data_engine.market_data_feed import MarketDataFeed
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure you're running from the project root directory")
    sys.exit(1)

class DatabaseInitializer:
    """Professional database initialization and data fetching"""
    
    def __init__(self):
        """Initialize the database setup process"""
        self.logger = get_logger()
        self.logger.info("🚀 Starting database initialization process...")
        
        # Load configuration
        try:
            self.config = ConfigManager("Agent_Trading/config.json")
            self.logger.info("✅ Configuration loaded successfully")
        except Exception as e:
            self.logger.error(f"❌ Failed to load configuration: {e}")
            raise
    
    def create_directory_structure(self):
        """Create necessary directory structure"""
        self.logger.info("📁 Creating directory structure...")
        
        # Get database path from config
        db_config = self.config.get_database_config()
        db_path = Path(db_config['db_path'])
        
        # Create data directory
        data_dir = db_path.parent
        data_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"✅ Created data directory: {data_dir}")
        return data_dir
    
    def initialize_database(self):
        """Initialize database with proper schema"""
        self.logger.info("🗄️ Initializing database...")
        
        try:
            # Create database manager
            self.db_manager = DatabaseManager(self.config)
            self.logger.info("✅ Database initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"❌ Database initialization failed: {e}")
            return False
    
    def test_api_connection(self):
        """Test Delta Exchange API connection"""
        self.logger.info("🔌 Testing Delta Exchange API connection...")
        
        try:
            # Create market data feed
            self.market_feed = MarketDataFeed(self.config)
            
            # Test by getting latest price
            latest_price = self.market_feed.get_latest_price()
            if latest_price:
                self.logger.info(f"✅ API connection successful - BTC Price: ${latest_price:,.2f}")
                return True
            else:
                self.logger.error("❌ API connection failed - No price data received")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ API connection test failed: {e}")
            return False
    
    def fetch_historical_data(self):
        """Fetch 1 year of historical data"""
        self.logger.info("📊 Starting historical data fetch...")
        
        try:
            # Create historical manager
            self.historical_manager = HistoricalManager(self.config)
            
            # Fetch data for all timeframes
            success = self.historical_manager.fetch_and_store_historical_data(
                days=365,  # 1 year of data
                force_refresh=True
            )
            
            if success:
                self.logger.info("✅ Historical data fetch completed successfully")
                return True
            else:
                self.logger.error("❌ Historical data fetch failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Historical data fetch error: {e}")
            return False
    
    def validate_data_quality(self):
        """Validate the quality of fetched data"""
        self.logger.info("🔍 Validating data quality...")
        
        try:
            # Get data summary for each timeframe
            timeframes = ["3m", "15m", "1h"]
            
            for timeframe in timeframes:
                # Get latest data
                data = self.db_manager.get_market_data(
                    symbol="BTCUSD",
                    timeframe=timeframe,
                    limit=1000
                )
                
                if not data.empty:
                    self.logger.info(f"✅ {timeframe}: {len(data)} records, "
                                   f"Latest: {data.index[-1]}, "
                                   f"Price range: ${data['low'].min():,.0f} - ${data['high'].max():,.0f}")
                else:
                    self.logger.warning(f"⚠️ No data found for {timeframe}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Data validation error: {e}")
            return False
    
    def generate_summary_report(self):
        """Generate a summary report of the initialization"""
        self.logger.info("📋 Generating summary report...")
        
        try:
            # Database info
            db_config = self.config.get_database_config()
            db_path = Path(db_config['db_path'])
            
            if db_path.exists():
                db_size = db_path.stat().st_size / (1024 * 1024)  # MB
                self.logger.info(f"📊 Database size: {db_size:.2f} MB")
            
            # Data summary
            timeframes = ["3m", "15m", "1h"]
            total_records = 0
            
            for timeframe in timeframes:
                count = self.db_manager.get_record_count("BTCUSD", timeframe)
                total_records += count
                self.logger.info(f"📈 {timeframe} records: {count:,}")
            
            self.logger.info(f"🎯 Total records: {total_records:,}")
            self.logger.info("✅ Database initialization completed successfully!")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Summary report error: {e}")
            return False

def main():
    """Main initialization function"""
    print("🚀 Professional BTC Trading System - Database Initialization")
    print("=" * 60)
    
    initializer = DatabaseInitializer()
    
    steps = [
        ("📁 Create Directory Structure", initializer.create_directory_structure),
        ("🗄️ Initialize Database", initializer.initialize_database),
        ("🔌 Test API Connection", initializer.test_api_connection),
        ("📊 Fetch Historical Data", initializer.fetch_historical_data),
        ("🔍 Validate Data Quality", initializer.validate_data_quality),
        ("📋 Generate Summary Report", initializer.generate_summary_report)
    ]
    
    for step_name, step_function in steps:
        print(f"\n{step_name}")
        print("-" * 40)
        
        try:
            result = step_function()
            if result:
                print(f"✅ {step_name} completed successfully")
            else:
                print(f"❌ {step_name} failed")
                break
        except Exception as e:
            print(f"❌ {step_name} error: {e}")
            break
        
        # Small delay between steps
        time.sleep(1)
    
    print("\n" + "=" * 60)
    print("🎯 Database initialization process completed!")
    print("📊 Your trading system is now ready for backtesting and live trading.")

if __name__ == "__main__":
    main()
